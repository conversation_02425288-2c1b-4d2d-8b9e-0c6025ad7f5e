import 'package:flutter/material.dart';
import 'package:callitris/services/webview_payment_service.dart';
import 'package:callitris/services/app_logger.dart';

/// Page de test pour l'URL spécifique qui posait problème
class SpecificUrlTestPage extends StatefulWidget {
  const SpecificUrlTestPage({Key? key}) : super(key: key);

  @override
  State<SpecificUrlTestPage> createState() => _SpecificUrlTestPageState();
}

class _SpecificUrlTestPageState extends State<SpecificUrlTestPage> {
  // L'URL problématique fournie par l'utilisateur
  final String _problematicUrl = 'https://checkout.cinetpay.com/payment/89590c61cac46019688bf68192053225fcb09420c3856918b47645f3f7aff0e45a172ba443de5c833a2b696198aa64f68840fec03ea23f?command_id=59236&client_id=44017&transaction_id=unknown&success_url=https%3A%2F%2Fapi.callitris-distribution.com%2Fdeep_links_config%2Fbackend%2Fpayment_redirect.php%3Fprovider%3Dgeneric%26status%3Dsuccess%26command_id%3D59236%26client_id%3D44017%26transaction_id%3Dunknown&failure_url=https%3A%2F%2Fapi.callitris-distribution.com%2Fdeep_links_config%2Fbackend%2Fpayment_redirect.php%3Fprovider%3Dgeneric%26status%3Dfailure%26command_id%3D59236%26client_id%3D44017%26transaction_id%3Dunknown&pending_url=https%3A%2F%2Fapi.callitris-distribution.com%2Fdeep_links_config%2Fbackend%2Fpayment_redirect.php%3Fprovider%3Dgeneric%26status%3Dpending%26command_id%3D59236%26client_id%3D44017%26transaction_id%3Dunknown';

  final List<String> _testUrls = [];
  bool _isLoading = false;
  String _testResult = '';

  @override
  void initState() {
    super.initState();
    _testUrls.addAll([
      _problematicUrl,
      'https://checkout.cinetpay.com/payment/status/failed',
      'https://checkout.cinetpay.com/payment/status/success',
      'https://api.callitris-distribution.com/deep_links_config/backend/payment_redirect.php?provider=generic&status=success&command_id=59236&client_id=44017&transaction_id=unknown',
      'https://api.callitris-distribution.com/deep_links_config/backend/payment_redirect.php?provider=generic&status=failure&command_id=59236&client_id=44017&transaction_id=unknown',
    ]);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Test URL Spécifique'),
        backgroundColor: const Color.fromARGB(255, 249, 221, 175),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildProblemExplanation(),
            const SizedBox(height: 16),
            _buildUrlAnalysis(),
            const SizedBox(height: 16),
            _buildTestSection(),
            const SizedBox(height: 16),
            _buildResultSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildProblemExplanation() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.bug_report, color: Colors.red, size: 20),
                const SizedBox(width: 8),
                Text(
                  'Problème Identifié',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.red.shade700,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            const Text(
              'La WebView se fermait prématurément à cause d\'une détection trop large.\n\n'
              'AVANT: Détectait "cinetpay.com" + "failed" dans les paramètres\n'
              'MAINTENANT: Détecte uniquement "/payment/status/failed"\n\n'
              'Votre URL contient "failure_url" dans les paramètres, ce qui déclenchait '
              'la fermeture par erreur.',
              style: TextStyle(fontSize: 14, height: 1.4),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUrlAnalysis() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Analyse de l\'URL Problématique',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            
            _buildAnalysisRow('URL de base', 'checkout.cinetpay.com/payment/...', Colors.green, 'OK - URL de paiement normale'),
            _buildAnalysisRow('Contient "cinetpay.com"', 'Oui', Colors.orange, 'Détection trop large avant'),
            _buildAnalysisRow('Contient "failed"', 'Oui (dans failure_url)', Colors.orange, 'Faux positif avant'),
            _buildAnalysisRow('Contient "/status/failed"', 'Non', Colors.green, 'Pas de fermeture maintenant'),
            _buildAnalysisRow('Comportement attendu', 'WebView reste ouverte', Colors.green, 'Corrigé ✓'),
          ],
        ),
      ),
    );
  }

  Widget _buildAnalysisRow(String label, String value, Color color, String explanation) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.w500, fontSize: 12),
            ),
          ),
          Icon(Icons.circle, color: color, size: 8),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(value, style: const TextStyle(fontSize: 12)),
                Text(
                  explanation,
                  style: TextStyle(fontSize: 10, color: Colors.grey.shade600),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTestSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Tests Disponibles',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            
            // Test URL problématique
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isLoading ? null : () => _testSpecificUrl(_problematicUrl),
                icon: _isLoading 
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Icon(Icons.play_arrow),
                label: const Text('Tester URL Problématique'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
            
            const SizedBox(height: 8),
            
            // Test URL d'échec
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: _isLoading ? null : () => _testSpecificUrl('https://checkout.cinetpay.com/payment/status/failed'),
                icon: const Icon(Icons.error),
                label: const Text('Tester URL d\'Échec (doit fermer)'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: Colors.red,
                ),
              ),
            ),
            
            const SizedBox(height: 8),
            
            // Test URL de succès
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: _isLoading ? null : () => _testSpecificUrl('https://checkout.cinetpay.com/payment/status/success'),
                icon: const Icon(Icons.check_circle),
                label: const Text('Tester URL de Succès (doit fermer)'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: Colors.green,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildResultSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Résultats du Test',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Container(
              width: double.infinity,
              height: 200,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: SingleChildScrollView(
                child: Text(
                  _testResult.isEmpty ? 'Aucun test effectué' : _testResult,
                  style: const TextStyle(
                    fontFamily: 'monospace',
                    fontSize: 11,
                  ),
                ),
              ),
            ),
            const SizedBox(height: 8),
            SizedBox(
              width: double.infinity,
              child: TextButton.icon(
                onPressed: () {
                  setState(() {
                    _testResult = '';
                  });
                },
                icon: const Icon(Icons.clear),
                label: const Text('Effacer'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _testSpecificUrl(String testUrl) async {
    setState(() {
      _isLoading = true;
      _testResult += '\n=== TEST DÉMARRÉ ===\n';
      _testResult += 'URL: ${testUrl.length > 100 ? testUrl.substring(0, 100) + '...' : testUrl}\n';
      _testResult += 'Heure: ${DateTime.now()}\n\n';
    });

    try {
      final transactionId = 'test_${DateTime.now().millisecondsSinceEpoch}';
      
      await WebViewPaymentService.launchCinetPayWebView(
        context: context,
        paymentUrl: testUrl,
        transactionId: transactionId,
        commandId: '59236',
        clientId: '44017',
        onPaymentCompleted: () {
          setState(() {
            _testResult += 'RÉSULTAT: ✅ Paiement complété\n';
            _testResult += 'COMPORTEMENT: WebView fermée normalement\n';
          });
          AppLogger.info('Test URL spécifique - Succès', 'SpecificUrlTest');
        },
        onPaymentFailed: (reason) {
          setState(() {
            _testResult += 'RÉSULTAT: ❌ Paiement échoué - $reason\n';
            if (reason.contains('Redirection vers navigateur')) {
              _testResult += 'COMPORTEMENT: ✅ WebView fermée + Navigateur ouvert\n';
            } else {
              _testResult += 'COMPORTEMENT: WebView fermée normalement\n';
            }
          });
          AppLogger.info('Test URL spécifique - Échec: $reason', 'SpecificUrlTest');
        },
        onPaymentCancelled: () {
          setState(() {
            _testResult += 'RÉSULTAT: ⚠️ Paiement annulé par utilisateur\n';
            _testResult += 'COMPORTEMENT: WebView fermée par annulation\n';
          });
          AppLogger.info('Test URL spécifique - Annulation', 'SpecificUrlTest');
        },
      );
    } catch (e) {
      setState(() {
        _testResult += 'ERREUR: $e\n';
      });
      AppLogger.error('Erreur test URL spécifique: $e', 'SpecificUrlTest');
    } finally {
      setState(() {
        _isLoading = false;
        _testResult += '\n=== TEST TERMINÉ ===\n\n';
      });
    }
  }
}
