import 'package:flutter/material.dart';
import 'package:callitris/services/payment_config_service.dart';

/// Widget pour configurer les préférences de paiement
class PaymentSettingsWidget extends StatefulWidget {
  const PaymentSettingsWidget({Key? key}) : super(key: key);

  @override
  State<PaymentSettingsWidget> createState() => _PaymentSettingsWidgetState();
}

class _PaymentSettingsWidgetState extends State<PaymentSettingsWidget> {
  bool _useWebViewForCinetPay = true;
  bool _autoCloseWebView = true;
  bool _enableDeepLinks = true;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    try {
      final settings = await PaymentConfigService.getAllSettings();
      setState(() {
        _useWebViewForCinetPay = settings['useWebView'] ?? true;
        _autoCloseWebView = settings['autoClose'] ?? true;
        _enableDeepLinks = settings['deepLinks'] ?? true;
        _isLoading = false;
      });
    } catch (e) {
      print('Erreur lors du chargement des paramètres: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _saveSettings() async {
    try {
      await PaymentConfigService.setUseWebViewForCinetPay(
          _useWebViewForCinetPay);
      await PaymentConfigService.setAutoCloseWebView(_autoCloseWebView);
      await PaymentConfigService.setDeepLinksEnabled(_enableDeepLinks);

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Paramètres sauvegardés'),
          backgroundColor: Colors.green,
          duration: Duration(seconds: 2),
        ),
      );
    } catch (e) {
      print('Erreur lors de la sauvegarde des paramètres: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Erreur lors de la sauvegarde'),
          backgroundColor: Colors.red,
          duration: Duration(seconds: 2),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.payment,
                  color: Colors.blue,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'Paramètres de Paiement',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 20),

            // Option WebView pour CinetPay
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.web,
                        color: Colors.blue,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'CinetPay WebView',
                        style: TextStyle(
                          fontWeight: FontWeight.w600,
                          color: Colors.blue.shade700,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  SwitchListTile(
                    title: const Text('Utiliser WebView intégrée'),
                    subtitle: const Text(
                      'Ouvre CinetPay dans l\'application au lieu du navigateur',
                    ),
                    value: _useWebViewForCinetPay,
                    onChanged: (value) {
                      setState(() {
                        _useWebViewForCinetPay = value;
                      });
                      _saveSettings();
                    },
                    activeColor: Colors.blue,
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Option fermeture automatique
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.auto_awesome,
                        color: Colors.green,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Fermeture Automatique',
                        style: TextStyle(
                          fontWeight: FontWeight.w600,
                          color: Colors.green.shade700,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  SwitchListTile(
                    title: const Text('Fermer automatiquement'),
                    subtitle: const Text(
                      'Ferme la WebView automatiquement après le paiement',
                    ),
                    value: _autoCloseWebView,
                    onChanged: (value) {
                      setState(() {
                        _autoCloseWebView = value;
                      });
                      _saveSettings();
                    },
                    activeColor: Colors.green,
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Option Deep Links
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.purple.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.purple.withValues(alpha: 0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.link,
                        color: Colors.purple,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Deep Links',
                        style: TextStyle(
                          fontWeight: FontWeight.w600,
                          color: Colors.purple.shade700,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  SwitchListTile(
                    title: const Text('Activer les Deep Links'),
                    subtitle: const Text(
                      'Permet le retour automatique vers l\'application',
                    ),
                    value: _enableDeepLinks,
                    onChanged: (value) {
                      setState(() {
                        _enableDeepLinks = value;
                      });
                      _saveSettings();
                    },
                    activeColor: Colors.purple,
                  ),
                ],
              ),
            ),

            const SizedBox(height: 20),

            // Informations
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        color: Colors.grey.shade600,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Informations',
                        style: TextStyle(
                          fontWeight: FontWeight.w600,
                          color: Colors.grey.shade700,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '• WebView intégrée : Plus fluide, reste dans l\'app\n'
                    '• Navigateur externe : Plus compatible, mais sort de l\'app\n'
                    '• Deep Links : Permettent le retour automatique après paiement\n'
                    '• Fermeture auto : Ferme la WebView après succès/échec',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                      height: 1.4,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
