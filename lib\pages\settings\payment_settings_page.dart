import 'package:flutter/material.dart';
import 'package:callitris/widgets/payment_settings_widget.dart';
import 'package:callitris/services/payment_config_service.dart';
import 'package:callitris/services/webview_payment_service.dart';

/// Page de paramètres pour la configuration des paiements
class PaymentSettingsPage extends StatefulWidget {
  const PaymentSettingsPage({Key? key}) : super(key: key);

  @override
  State<PaymentSettingsPage> createState() => _PaymentSettingsPageState();
}

class _PaymentSettingsPageState extends State<PaymentSettingsPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Paramètres de Paiement'),
        backgroundColor: const Color.fromARGB(255, 249, 221, 175),
        actions: [
          IconButton(
            icon: const Icon(Icons.restore),
            onPressed: _resetToDefaults,
            tooltip: 'Remettre par défaut',
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            const SizedBox(height: 16),

            // Widget principal de configuration
            const PaymentSettingsWidget(),

            const SizedBox(height: 16),

            // Informations sur les deep links
            _buildDeepLinksInfo(),

            const SizedBox(height: 16),

            // Boutons d'action
            _buildActionButtons(),

            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildDeepLinksInfo() {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: Colors.blue,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'À propos des Deep Links',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Colors.blue.shade700,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              'Les deep links permettent à l\'application de recevoir automatiquement '
              'les résultats de paiement depuis les plateformes externes.\n\n'
              'URLs supportées :\n'
              '• callitris://payment/success\n'
              '• callitris://payment/failure\n'
              '• callitris://payment/pending\n\n'
              'Ces liens fonctionnent avec ou sans WebView intégrée.',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade700,
                height: 1.4,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _testWebView,
              icon: const Icon(Icons.play_arrow),
              label: const Text('Tester WebView CinetPay'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
          const SizedBox(height: 8),
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: _testDeepLinks,
              icon: const Icon(Icons.link),
              label: const Text('Tester Deep Links'),
              style: OutlinedButton.styleFrom(
                foregroundColor: Colors.purple,
                side: BorderSide(color: Colors.purple),
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _resetToDefaults() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Remettre par défaut'),
        content: const Text(
          'Voulez-vous remettre tous les paramètres de paiement par défaut ?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Confirmer'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await PaymentConfigService.resetToDefaults();
      setState(() {}); // Recharger la page

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Paramètres remis par défaut'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  Future<void> _testWebView() async {
    // URL de test CinetPay (remplacez par une vraie URL de test)
    const testUrl = 'https://checkout.cinetpay.com/test';

    try {
      await WebViewPaymentService.launchCinetPayWebView(
        context: context,
        paymentUrl: testUrl,
        transactionId: 'test_${DateTime.now().millisecondsSinceEpoch}',
        commandId: 'test_command',
        clientId: 'test_client',
        onPaymentCompleted: () {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Test WebView - Paiement simulé réussi'),
              backgroundColor: Colors.green,
            ),
          );
        },
        onPaymentFailed: (reason) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Test WebView - Échec: $reason'),
              backgroundColor: Colors.red,
            ),
          );
        },
        onPaymentCancelled: () {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Test WebView - Annulé'),
              backgroundColor: Colors.orange,
            ),
          );
        },
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Erreur test WebView: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _testDeepLinks() async {
    final testUrls = [
      'callitris://payment/success?transaction_id=test123&command_id=cmd456&client_id=client789',
      'callitris://payment/failure?transaction_id=test123&reason=Test%20échec',
      'callitris://payment/pending?transaction_id=test123',
    ];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Test Deep Links'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('URLs de test disponibles :'),
            const SizedBox(height: 16),
            ...testUrls.map((url) => Padding(
                  padding: const EdgeInsets.symmetric(vertical: 4),
                  child: SelectableText(
                    url,
                    style: const TextStyle(
                      fontSize: 12,
                      fontFamily: 'monospace',
                    ),
                  ),
                )),
            const SizedBox(height: 16),
            const Text(
              'Copiez ces URLs et testez-les avec adb (Android) ou xcrun (iOS)',
              style: TextStyle(fontSize: 12),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Fermer'),
          ),
        ],
      ),
    );
  }
}
