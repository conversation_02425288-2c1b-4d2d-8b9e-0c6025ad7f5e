import 'dart:convert';
import 'package:callitris/pages/menu.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;
import 'package:provider/provider.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:intl/intl.dart';
import '../auth_provider.dart';
import '../clients/user_page_boutique.dart';
import '../clients/boutique_payment_method_page.dart';

class Option {
  final String id;
  final String value;

  Option(this.id, this.value);
}

class Kit {
  final String id;
  final String livret;
  final String option_kit;
  final String montant;
  final String total_prod;
  final String cout_journal;
  final String photo_kit;

  Kit({
    required this.id,
    required this.livret,
    required this.option_kit,
    required this.montant,
    required this.total_prod,
    required this.cout_journal,
    required this.photo_kit,
  });
}

class BoutiquePage extends StatefulWidget {
  final String id;

  const BoutiquePage({super.key, required this.id});

  @override
  _BoutiquePageState createState() => _BoutiquePageState();
}

class _BoutiquePageState extends State<BoutiquePage> {
  Option? selectedOption1;
  Option? selectedOption2;
  List<Option> _dureeOptions = [];
  List<Option> _livretOptions = [];
  List<Kit> kits = [];
  bool isLivretAvailable = true;

  // Variables pour le versement
  final _controllerMontant = TextEditingController();
  final _controllerJours = TextEditingController();
  double? monnaie;
  bool useMonnaie = false;
  bool isSubmitting = false;
  String? journalier;
  String? jour_reste;
  String? commandeId;
  String? commandeCle;

  Future<void> _fetchDureeOptions() async {
    final provide = Provider.of<AuthProvider>(context, listen: false);
    final token = provide.token;

    try {
      final response = await http.get(
        Uri.parse(provide.getEndpoint('products/getDuree.php')),
        headers: {'Authorization': token!, 'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        final List<Option> options = data.map((item) {
          return Option(
              item['id_duree'].toString(), item['nombre_mois'].toString());
        }).toList();
        setState(() {
          _dureeOptions = options;
        });
      } else {
        _handleError(response);
      }
    } catch (error) {
      _handleError(error);
    }
  }

  Future<void> _fetchLivretOptions(/*String dureeId*/) async {
    final provide = Provider.of<AuthProvider>(context, listen: false);
    final token = provide.token;
    final user = provide.user;

    String localId = user!['local_id'].toString();
    print(user);
    try {
      final response = await http.get(
        Uri.parse(provide.getEndpoint(
            'products/getLivret.php?duree_id=null&local_id=$localId&personnelId=${user['id_personnel']}')),
        headers: {'Authorization': token!, 'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        final List<Option> options = data.map((item) {
          return Option(
              item['id_livret'].toString(), item['code_livret'].toString());
        }).toList();
        print('Livrets disponibles: $options');
        setState(() {
          _livretOptions = options;
          isLivretAvailable = _livretOptions.isNotEmpty;
        });

        if (!isLivretAvailable) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(
                'Aucun carnet disponible.',
                style: TextStyle(color: Colors.red),
              ),
            ),
          );
        }
      } else {
        _handleError(response);
      }
    } catch (error) {
      _handleError(error);
    }
  }

  Future<void> _fetchKit(String idLivret) async {
    final provide = Provider.of<AuthProvider>(context, listen: false);
    final token = provide.token;

    try {
      final response = await http.get(
        Uri.parse(
            provide.getEndpoint('products/getKit.php?livret_id=$idLivret')),
        headers: {'Authorization': token!, 'Content-Type': 'application/json'},
      );
      String loadImg2(String imagePath) {
        if (imagePath.contains("../../")) {
          imagePath = imagePath.replaceAll("../../", "app/");
        }

        return "https://app.callitris-distribution.com/$imagePath";
      }

      if (response.statusCode == 200) {
        final List<dynamic> responseData = jsonDecode(response.body);
        print("Response pour tous les kits: $responseData");
        List<Kit> fetchedKits = responseData.map((kitData) {
          return Kit(
            id: kitData['id_kit'].toString(),
            livret: kitData['livret_id'].toString(),
            option_kit: kitData['option_kit'].toString(),
            montant: kitData['montant_total_kit'].toString(),
            total_prod: kitData['total_prod_kit'].toString(),
            photo_kit: loadImg2(kitData['photo_kit'].toString()),
            cout_journal: kitData['cout_journalier_kit'].toString(),
          );
        }).toList();

        setState(() {
          kits = fetchedKits;
        });
      } else {
        _handleError(response);
      }
    } catch (error) {
      _handleError(error);
    }
  }

  void _handleError(dynamic error) {
    print('Erreur : $error');
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Une erreur est survenue.'),
        backgroundColor: Colors.red,
      ),
    );
  }

  @override
  void initState() {
    super.initState();
    _fetchLivretOptions();
    _fetchMonnaie();
    _fetchCommandeInfo();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _fetchDureeOptions();
    });
  }

  Future<void> _fetchMonnaie() async {
    try {
      final provide = Provider.of<AuthProvider>(context, listen: false);
      final token = Provider.of<AuthProvider>(context, listen: false).token;

      final response = await http.get(
        Uri.parse(provide.getEndpoint(
            'products/getMonnaie.php?id=${widget.id}&compte_id=1')),
        headers: {'Authorization': token!, 'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        print('Réponse de la monnaie: ${response.body}');
        final Map<String, dynamic> responseData = jsonDecode(response.body);
        String monnaieValue = responseData['montant'].toString();
        setState(() {
          monnaie = double.parse(monnaieValue);
        });
      }
    } catch (error) {
      print('Erreur lors de la récupération de la monnaie: $error');
    }
  }

  Future<void> _fetchCommandeInfo() async {
    try {
      final provide = Provider.of<AuthProvider>(context, listen: false);
      final token = Provider.of<AuthProvider>(context, listen: false).token;

      final response = await http.get(
        Uri.parse(provide.getEndpoint(
            'products/getCommandeInfo.php?id=${widget.id}&compte_id=1')),
        headers: {'Authorization': token!, 'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        if (responseData is Map<String, dynamic>) {
          setState(() {
            journalier = responseData['journalier']?.toString();
            jour_reste = responseData['reste']?.toString();
          });
        }
      }
    } catch (error) {
      print('Erreur lors de la récupération des infos commande: $error');
    }
  }

  String _formatNumber(String s) {
    final numberFormat = NumberFormat("#,###", "fr_FR");
    return numberFormat.format(int.parse(
        s.replaceAll('.', '').replaceAll(' ', '').replaceAll('\u202F', '')));
  }

  Future<void> _envoyerMontant(
      int montantSaisi, double resteMonnaie, int monnaieExact) async {
    setState(() {
      isSubmitting = true;
    });

    try {
      final provide = Provider.of<AuthProvider>(context, listen: false);
      final user = Provider.of<AuthProvider>(context, listen: false).user;
      final token = Provider.of<AuthProvider>(context, listen: false).token;

      String personnelId = user!['id_personnel'].toString();

      final response = await http.post(
        Uri.parse(provide.getEndpoint('products/addVersementCom.php')),
        headers: {
          'Authorization': token!,
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'commande_id': commandeId!,
          'clientId': widget.id,
          'montant': montantSaisi,
          'monnaieExact': monnaieExact,
          'resteMonnaie': resteMonnaie,
          'personnelId': personnelId,
          'monnaie': monnaie,
        }),
      );

      if (response.statusCode == 200) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Versement effectué avec succès'),
            backgroundColor: Colors.green,
          ),
        );
        // Rafraîchir les données
        _fetchMonnaie();
        _fetchCommandeInfo();
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Erreur lors du versement'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (error) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Erreur de connexion'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        isSubmitting = false;
      });
    }
  }

  void _showNouvelleTontineSheet(BuildContext context) {
    double montantJ = 0;
    int montantSaisi = 0;
    int nbrePaye = 0;
    int quotient = 0;
    int monnaieExact = 0;
    double resteMonnaie = 0;

    void recalculerMontants(StateSetter updateState) {
      try {
        montantJ = double.parse(journalier ?? '0');
        String amount = _controllerMontant.text
            .replaceAll('.', '')
            .replaceAll(' ', '')
            .replaceAll('\u202F', '');
        montantSaisi = amount.isNotEmpty ? int.parse(amount) : 0;

        double monnaieToUse = useMonnaie ? (monnaie ?? 0) : 0;

        if (montantSaisi >= montantJ &&
            (resteMonnaie + (monnaie ?? 0)) < montantJ) {
          useMonnaie = false;
          monnaieToUse = 0;
        }

        if (montantJ > 0 && montantSaisi >= (montantJ - monnaieToUse)) {
          resteMonnaie = (montantSaisi + monnaieToUse) % montantJ;
          quotient = (montantSaisi + monnaieToUse) ~/ montantJ;
        } else {
          resteMonnaie = 0;
          quotient = 0;
        }

        updateState(() {});
        monnaieExact =
            (useMonnaie ? ((quotient * montantJ.toInt()) - montantSaisi) : 0);
      } catch (e) {
        resteMonnaie = 0;
        quotient = 0;
        monnaieExact = 0;
        updateState(() {});
      }
    }

    if (int.parse(jour_reste ?? '0') > 0) {
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (BuildContext context) {
          return StatefulBuilder(
            builder: (BuildContext context, StateSetter setState) {
              return Container(
                padding: EdgeInsets.only(
                  bottom: MediaQuery.of(context).viewInsets.bottom,
                ),
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius:
                        BorderRadius.vertical(top: Radius.circular(20)),
                  ),
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Text(
                        'Versement Boutique',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 20,
                        ),
                      ),
                      const SizedBox(height: 16),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Monnaie : ${monnaie ?? 0} F CFA',
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Switch(
                            value: useMonnaie,
                            onChanged: (bool value) {
                              setState(() {
                                useMonnaie = value;
                                recalculerMontants(setState);
                              });
                            },
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      Row(
                        children: [
                          Expanded(
                            child: TextFormField(
                              controller: _controllerMontant,
                              inputFormatters: [
                                FilteringTextInputFormatter.digitsOnly,
                                TextInputFormatter.withFunction(
                                    (oldValue, newValue) {
                                  String newText = _formatNumber(newValue.text);
                                  return TextEditingValue(
                                    text: newText,
                                    selection: TextSelection.collapsed(
                                        offset: newText.length),
                                  );
                                }),
                              ],
                              keyboardType: TextInputType.number,
                              onChanged: (value) {
                                recalculerMontants(setState);
                              },
                              decoration: const InputDecoration(
                                border: OutlineInputBorder(),
                                labelText: 'Montant à verser',
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: TextFormField(
                              controller: _controllerJours,
                              inputFormatters: [
                                FilteringTextInputFormatter.digitsOnly
                              ],
                              keyboardType: TextInputType.number,
                              onChanged: (value) {
                                setState(() {
                                  nbrePaye = int.tryParse(value) ?? 0;
                                });
                              },
                              decoration: const InputDecoration(
                                border: OutlineInputBorder(),
                                labelText: 'Jours à payer',
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: const Color.fromARGB(255, 224, 222, 222),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          'Jours Correspondants : $quotient Jour(s)',
                          style: const TextStyle(fontSize: 16),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: const Color.fromARGB(255, 224, 222, 222),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          'Monnaie restante : $resteMonnaie FCFA',
                          style: const TextStyle(fontSize: 16),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: isSubmitting
                            ? null
                            : () {
                                if (!useMonnaie &&
                                    (resteMonnaie + (monnaie ?? 0)) >
                                        montantJ) {
                                  showDialog<void>(
                                    context: context,
                                    barrierDismissible: false,
                                    builder: (BuildContext context) {
                                      return AlertDialog(
                                        content: const Column(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Icon(Icons.error,
                                                color: Colors.red, size: 48.0),
                                            SizedBox(height: 16.0),
                                            Text(
                                              'Le total des monnaies dépasse le montant journalier. La monnaie doit être utilisée.',
                                              style: TextStyle(
                                                  fontWeight: FontWeight.bold),
                                            ),
                                          ],
                                        ),
                                        actions: [
                                          TextButton(
                                            onPressed: () =>
                                                Navigator.pop(context),
                                            child: const Text('OK'),
                                          ),
                                        ],
                                      );
                                    },
                                  );
                                  return;
                                }
                                if (montantSaisi >= 0 &&
                                    (quotient == nbrePaye) &&
                                    nbrePaye > 0) {
                                  _envoyerMontant(
                                      montantSaisi, resteMonnaie, monnaieExact);
                                  Navigator.pop(context);
                                } else {
                                  Navigator.pop(context);
                                  showDialog<void>(
                                    context: context,
                                    barrierDismissible: false,
                                    builder: (BuildContext context) {
                                      return AlertDialog(
                                        content: const Column(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Icon(Icons.error,
                                                color: Colors.red, size: 48.0),
                                            SizedBox(height: 16.0),
                                            Text(
                                              'Veuillez entrer le montant et le nombre de jours correspondant',
                                              style: TextStyle(
                                                  fontWeight: FontWeight.bold),
                                            ),
                                          ],
                                        ),
                                        actions: [
                                          TextButton(
                                            onPressed: () =>
                                                Navigator.pop(context),
                                            child: const Text('OK'),
                                          ),
                                        ],
                                      );
                                    },
                                  );
                                }
                                setState(() {
                                  _controllerMontant.clear();
                                  _controllerJours.clear();
                                  resteMonnaie = 0;
                                  quotient = 0;
                                  useMonnaie = false;
                                });
                              },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                          padding: const EdgeInsets.symmetric(
                              vertical: 12, horizontal: 24),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                        ),
                        child: isSubmitting
                            ? const CircularProgressIndicator(
                                valueColor:
                                    AlwaysStoppedAnimation<Color>(Colors.white),
                              )
                            : const Text(
                                'Enregistrer',
                                style: TextStyle(color: Colors.white),
                              ),
                      ),
                    ],
                  ),
                ),
              );
            },
          );
        },
      );
    } else {
      showDialog<void>(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return AlertDialog(
            content: const Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.error, color: Colors.red, size: 48.0),
                SizedBox(height: 16.0),
                Text(
                  'Impossible de faire un versement, aucun jour de versement restant.',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('OK'),
              ),
            ],
          );
        },
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Espace Boutique'),
        backgroundColor: const Color.fromARGB(255, 249, 221, 175),
        actions: buildAppBarActions(context),
      ),
      backgroundColor: Colors.grey[50],
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildModernHeader(),
              const SizedBox(height: 24),
              _buildSelectionSection(),
              const SizedBox(height: 24),
              if (/*selectedOption1 != null &&*/ selectedOption2 != null)
                _buildKitsGrid(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildModernHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.orange.shade50,
            Colors.amber.shade50,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.orange.shade100),
        boxShadow: [
          BoxShadow(
            color: Colors.orange.withValues(alpha: 0.08),
            blurRadius: 12,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Colors.orange.shade300,
                  Colors.amber.shade400,
                ],
              ),
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.orange.withValues(alpha: 0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 3),
                ),
              ],
            ),
            child: const Icon(
              Icons.shopping_bag,
              color: Colors.white,
              size: 28,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Nouvelle Commande',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w600,
                    color: Colors.orange.shade800,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Sélectionnez la durée et le livret pour voir les kits disponibles',
                  style: TextStyle(
                    fontSize: 14,
                    color: const Color.fromARGB(255, 0, 0, 0),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSelectionSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Configuration de la commande',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.grey.shade800,
            ),
          ),
          const SizedBox(height: 20),

          // _buildModernDropdown(
          //   'Durée du livret',
          //   'Sélectionnez la durée',
          //   _dureeOptions,
          //   selectedOption1,
          //   (Option? value) {
          //     setState(() {
          //       selectedOption1 = value;
          //       selectedOption2 = null;
          //       kits.clear();
          //     });
          //     if (value != null) {
          //       _fetchLivretOptions(value.id);
          //     }
          //   },
          //   Icons.schedule,
          //   Colors.blue,
          // ),

          const SizedBox(height: 16),

          // if (selectedOption1 == null)
          //   _buildHelpMessage(
          //     'Veuillez sélectionner la durée du livret pour continuer',
          //     Colors.blue,
          //     Icons.info_outline,
          //   ),

          if (/*selectedOption1 != null &&*/ _livretOptions.isNotEmpty) ...[
            const SizedBox(height: 16),
            _buildModernDropdown(
              'Numéro du livret',
              'Sélectionnez le livret',
              _livretOptions,
              selectedOption2,
              (Option? value) {
                setState(() {
                  selectedOption2 = value;
                  kits.clear();
                });
                if (value != null) {
                  _fetchKit(value.id);
                }
              },
              Icons.book,
              Colors.teal,
            ),
            const SizedBox(height: 16),
            if (selectedOption2 == null)
              _buildHelpMessage(
                'Sélectionnez le numéro du livret pour voir les kits disponibles',
                Colors.teal,
                Icons.info_outline,
              ),
          ],

          if (_livretOptions.isEmpty && !isLivretAvailable)
            _buildHelpMessage(
              'Aucun carnet disponible.Veuillez contacter l\'administration.',
              Colors.orange,
              Icons.warning_outlined,
            ),
        ],
      ),
    );
  }

  Widget _buildKitsGrid() {
    if (kits.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(40),
        child: Column(
          children: [
            const CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.orange),
            ),
            const SizedBox(height: 16),
            Text(
              'Chargement des kits...',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.inventory_2,
              color: Colors.orange.shade600,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              'Kits disponibles',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: const Color.fromARGB(255, 0, 0, 0),
              ),
            ),
            const Spacer(),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.orange.shade50,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                '${kits.length} kit${kits.length > 1 ? 's' : ''}',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.orange.shade700,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            childAspectRatio: 0.85,
          ),
          itemCount: kits.length,
          itemBuilder: (context, index) {
            final kit = kits[index];
            return ModernKitCard(
              kit: kit,
              clientId: widget.id,
            );
          },
        ),
      ],
    );
  }

  Widget _buildModernDropdown(
    String label,
    String hint,
    List<Option> options,
    Option? selectedValue,
    Function(Option?) onChanged,
    IconData icon,
    Color color,
  ) {
    return Container(
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 18),
                const SizedBox(width: 8),
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: color,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            DropdownButtonFormField<Option>(
              value: selectedValue,
              onChanged: onChanged,
              isExpanded: true,
              decoration: InputDecoration(
                hintText: hint,
                hintStyle: TextStyle(color: Colors.grey[500]),
                border: InputBorder.none,
                contentPadding: EdgeInsets.zero,
              ),
              style: TextStyle(
                color: Colors.grey[800],
                fontSize: 16,
              ),
              items: options.map((Option option) {
                return DropdownMenuItem<Option>(
                  value: option,
                  child: Text(
                    option.value,
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHelpMessage(String message, Color color, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(icon, color: color, size: 16),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              message,
              style: TextStyle(
                fontSize: 13,
                color: color,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDropDownField(String labelText, List<Option> options,
      Option? selectedValue, Function(Option?) onChanged) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: DropdownButtonFormField<Option>(
        value: selectedValue,
        onChanged: onChanged,
        decoration: InputDecoration(
          labelText: labelText,
          border: const OutlineInputBorder(),
        ),
        items: options.map((Option option) {
          return DropdownMenuItem<Option>(
            value: option,
            child: Text(option.value),
          );
        }).toList(),
      ),
    );
  }
}

class FillImageCard extends StatelessWidget {
  final double width;
  final String photo;
  final String title;
  final Kit kit;
  final String clientId;

  const FillImageCard(
      {super.key,
      required this.width,
      required this.photo,
      required this.title,
      required this.kit,
      required this.clientId});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: width,
      child: Card(
        elevation: 4,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
          side: const BorderSide(
              color: Color.fromARGB(255, 54, 149, 244), width: 2),
        ),
        color: Colors.white,
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Text(
                title,
                textAlign: TextAlign.center,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ),
            Expanded(
              child: ClipRRect(
                borderRadius:
                    const BorderRadius.vertical(top: Radius.circular(8)),
                child: CachedNetworkImage(
                  imageUrl: photo,
                  placeholder: (context, url) => const SizedBox(
                    width: 50,
                    height: 50,
                    child: Center(
                      child: SizedBox(
                        width: 24,
                        height: 24,
                        child: CircularProgressIndicator(
                          strokeWidth: 5,
                        ),
                      ),
                    ),
                  ),
                  errorWidget: (context, url, error) => const Icon(Icons.error),
                ),
              ),
            ),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color.fromARGB(255, 54, 149, 244),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => ArticleDetailPage(
                      kit: kit,
                      clientId: clientId,
                    ),
                  ),
                );
              },
              child: const Text(
                'Commander',
                style: TextStyle(color: Colors.white),
              ),
            ),
            const SizedBox(height: 10),
          ],
        ),
      ),
    );
  }
}

class ArticleDetailPage extends StatefulWidget {
  final Kit kit;
  final String clientId;

  const ArticleDetailPage(
      {super.key, required this.kit, required this.clientId});

  @override
  ArticleDetailPageState createState() => ArticleDetailPageState();
}

class ArticleDetailPageState extends State<ArticleDetailPage> {
  bool isSubmitting = false;

  // Variables pour le versement
  final _controllerMontant = TextEditingController();
  final _controllerJours = TextEditingController();
  double? monnaie;
  bool useMonnaie = false;
  String? journalier;
  String? jour_reste;

  @override
  void initState() {
    super.initState();
    _fetchMonnaie();
    _fetchCommandeInfo();
  }

  Future<void> _fetchMonnaie() async {
    try {
      final token = Provider.of<AuthProvider>(context, listen: false).token;
      final provide = Provider.of<AuthProvider>(context, listen: false);

      final response = await http.get(
        Uri.parse(provide.getEndpoint(
            'client/getMonnaie.php?clientId=${widget.clientId}')),
        headers: {'Authorization': token!, 'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);
        String monnaieValue = responseData['montant'].toString();
        setState(() {
          monnaie = double.parse(monnaieValue);
        });
      } else {
        print(
            'Erreur lors de la récupération de la monnaie : ${response.statusCode}');
      }
    } catch (error) {
      print('Erreur lors de la récupération de la monnaie : $error');
    }
  }

  Future<void> _fetchCommandeInfo() async {
    try {
      final provide = Provider.of<AuthProvider>(context, listen: false);
      final token = Provider.of<AuthProvider>(context, listen: false).token;

      final response = await http.get(
        Uri.parse(provide.getEndpoint(
            'products/getCommandeInfo.php?id=${widget.clientId}&compte_id=1')),
        headers: {'Authorization': token!, 'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        if (responseData is Map<String, dynamic>) {
          setState(() {
            journalier = responseData['journalier']?.toString() ??
                widget.kit.cout_journal;
            jour_reste = responseData['reste']
                ?.toString(); // Valeur par défaut pour nouveau kit
          });
        }
      } else {
        // Si l'API échoue, utiliser les données du kit
        setState(() {
          journalier = widget.kit.cout_journal;
          jour_reste = '150'; // Valeur par défaut pour nouveau kit
        });
      }
    } catch (error) {
      print('Erreur lors de la récupération des infos commande: $error');
      // En cas d'erreur, utiliser les données du kit
      setState(() {
        journalier = widget.kit.cout_journal;
        jour_reste = '30'; // Valeur par défaut pour nouveau kit
      });
    }
  }

  String _formatNumber(String s) {
    final numberFormat = NumberFormat("#,###", "fr_FR");
    return numberFormat.format(int.parse(
        s.replaceAll('.', '').replaceAll(' ', '').replaceAll('\u202F', '')));
  }

  Future<void> _envoyerMontant(
      int montantSaisi, double resteMonnaie, int monnaieExact) async {
    setState(() {
      isSubmitting = true;
    });

    try {
      final provide = Provider.of<AuthProvider>(context, listen: false);
      final user = Provider.of<AuthProvider>(context, listen: false).user;
      final token = Provider.of<AuthProvider>(context, listen: false).token;

      String personnelId = user!['id_personnel'].toString();

      // Étape 1: Créer la commande boutique
      String? commandeId = await _createBoutiqueCommande();
      print('=== VERIFICATION COMMANDE ===');
      print('commandeId reçu: $commandeId');
      print('_commandeCle reçue: $_commandeCle');

      if (commandeId == null || commandeId.isEmpty) {
        print('ERREUR: commandeId est null ou vide');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(
                  'Erreur lors de la création de la commande - ID manquant'),
              backgroundColor: Colors.red,
            ),
          );
        }
        return;
      }

      if (_commandeCle == null || _commandeCle!.isEmpty) {
        print('ERREUR: _commandeCle est null ou vide');
        print(
            'ATTENTION: Tentative de versement sans clé - cela pourrait échouer');
        _commandeCle = '';
      }

      print('Commande créée avec succès - Procédure au versement...');

      // Étape 2: Enregistrer le versement comme premier paiement
      print('=== DEBUG VERSEMENT ===');
      print('commandeId: $commandeId');
      print('clientId: ${widget.clientId}');
      print('montant: $montantSaisi');
      print('monnaieExact: $monnaieExact');
      print('resteMonnaie: $resteMonnaie');
      print('personnelId: $personnelId');
      print('monnaie: $monnaie');

      final versementData = {
        'commande_id': commandeId,
        'cle': _commandeCle,
        'clientId': widget.clientId,
        'montant': montantSaisi,
        'monnaieExact': monnaieExact,
        'resteMonnaie': resteMonnaie,
        'personnelId': personnelId,
        'monnaie': monnaie,
      };

      print('versementData: $versementData');

      final response = await http.post(
        Uri.parse(provide.getEndpoint('products/addVersementCom.php')),
        headers: {
          'Authorization': token!,
          'Content-Type': 'application/json',
        },
        body: jsonEncode(versementData),
      );

      print('Versement response status: ${response.statusCode}');
      print('Versement response body: ${response.body}');

      if (response.statusCode == 200) {
        final versementResponseData = jsonDecode(response.body);
        print('Versement response data: $versementResponseData');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(
                  'Commande créée et premier versement effectué avec succès'),
              backgroundColor: Colors.green,
            ),
          );
          // Naviguer vers la page de détails de la commande
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
              builder: (context) =>
                  ClientDetailBoutiquePage(id: widget.clientId),
            ),
          );
        }
      } else {
        print('Erreur versement: ${response.statusCode} - ${response.body}');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Erreur lors du versement'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (error) {
      print('Exception versement: $error');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Erreur de connexion'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        isSubmitting = false;
      });
    }
  }

  String? _commandeCle; // Variable pour stocker la clé de la commande

  Future<String?> _createBoutiqueCommande() async {
    try {
      final provide = Provider.of<AuthProvider>(context, listen: false);
      final user = Provider.of<AuthProvider>(context, listen: false).user;
      final token = Provider.of<AuthProvider>(context, listen: false).token;

      String personnelId = user!['id_personnel'].toString();

      print('=== DEBUG CREATION COMMANDE ===');
      print('clientId: ${widget.clientId}');
      print('personnelId: $personnelId');
      print('id_kit: ${widget.kit.id}');
      print('endpoint: ${provide.getEndpoint('products/addCom.php')}');

      final response = await http.post(
        Uri.parse(provide.getEndpoint('products/addCom.php')),
        headers: {
          'Authorization': token!,
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'clientId': widget.clientId,
          'personnelId': personnelId,
          'id_kit': widget.kit.id,
          'qte': 1,
        }),
      );

      print('Response status: ${response.statusCode}');
      print('Response body: ${response.body}');

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        print('Response data: $responseData');
        print(
            'Clés disponibles dans la réponse: ${responseData.keys.toList()}');

        // Récupérer l'ID et la clé de la commande
        String? commandeId = responseData['id_commande']?.toString();
        _commandeCle = responseData['cle']?.toString();

        print('Commande créée - ID: $commandeId, Clé: $_commandeCle');

        // Vérifier si d'autres champs contiennent la clé
        if (_commandeCle == null) {
          print('Recherche de la clé dans d\'autres champs...');
          print('code_cmd: ${responseData['code_cmd']}');
          print('code: ${responseData['code']}');
          print('key: ${responseData['key']}');
          print('cle_commande: ${responseData['cle_commande']}');
        }

        return commandeId;
      } else {
        print('HTTP Error: ${response.statusCode} - ${response.body}');
      }
      return null;
    } catch (error) {
      print('Exception lors de la création de la commande: $error');
      return null;
    }
  }

  void _showNouvelleTontineSheet(BuildContext context) {
    double montantJ = 0;
    int montantSaisi = 0;
    int nbrePaye = 0;
    int quotient = 0;
    int monnaieExact = 0;
    double resteMonnaie = 0;

    void recalculerMontants(StateSetter updateState) {
      try {
        montantJ = double.parse(journalier ?? '0');
        String amount = _controllerMontant.text
            .replaceAll('.', '')
            .replaceAll(' ', '')
            .replaceAll('\u202F', '');
        montantSaisi = amount.isNotEmpty ? int.parse(amount) : 0;

        double monnaieToUse = useMonnaie ? (monnaie ?? 0) : 0;

        if (montantSaisi >= montantJ &&
            (resteMonnaie + (monnaie ?? 0)) < montantJ) {
          useMonnaie = false;
          monnaieToUse = 0;
        }

        if (montantJ > 0 && montantSaisi >= (montantJ - monnaieToUse)) {
          resteMonnaie = (montantSaisi + monnaieToUse) % montantJ;
          quotient = (montantSaisi + monnaieToUse) ~/ montantJ;
        } else {
          resteMonnaie = 0;
          quotient = 0;
        }

        updateState(() {});
        monnaieExact =
            (useMonnaie ? ((quotient * montantJ.toInt()) - montantSaisi) : 0);
      } catch (e) {
        resteMonnaie = 0;
        quotient = 0;
        monnaieExact = 0;
        updateState(() {});
      }
    }

    // Pour la boutique, on permet toujours le versement car c'est un nouveau kit
    if (true) {
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (BuildContext context) {
          return StatefulBuilder(
            builder: (BuildContext context, StateSetter setState) {
              return Container(
                padding: EdgeInsets.only(
                  bottom: MediaQuery.of(context).viewInsets.bottom,
                ),
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius:
                        BorderRadius.vertical(top: Radius.circular(20)),
                  ),
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Text(
                        'Versement Boutique',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 20,
                        ),
                      ),
                      const SizedBox(height: 16),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Monnaie : ${monnaie ?? 0} F CFA',
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Switch(
                            value: useMonnaie,
                            onChanged: (bool value) {
                              setState(() {
                                useMonnaie = value;
                                recalculerMontants(setState);
                              });
                            },
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      Row(
                        children: [
                          Expanded(
                            child: TextFormField(
                              controller: _controllerMontant,
                              inputFormatters: [
                                FilteringTextInputFormatter.digitsOnly,
                                TextInputFormatter.withFunction(
                                    (oldValue, newValue) {
                                  String newText = _formatNumber(newValue.text);
                                  return TextEditingValue(
                                    text: newText,
                                    selection: TextSelection.collapsed(
                                        offset: newText.length),
                                  );
                                }),
                              ],
                              keyboardType: TextInputType.number,
                              onChanged: (value) {
                                recalculerMontants(setState);
                              },
                              decoration: const InputDecoration(
                                border: OutlineInputBorder(),
                                labelText: 'Montant à verser',
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: TextFormField(
                              controller: _controllerJours,
                              inputFormatters: [
                                FilteringTextInputFormatter.digitsOnly
                              ],
                              keyboardType: TextInputType.number,
                              onChanged: (value) {
                                setState(() {
                                  nbrePaye = int.tryParse(value) ?? 0;
                                });
                              },
                              decoration: const InputDecoration(
                                border: OutlineInputBorder(),
                                labelText: 'Jours à payer',
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: const Color.fromARGB(255, 224, 222, 222),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          'Jours Correspondants : $quotient Jour(s)',
                          style: const TextStyle(fontSize: 16),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: const Color.fromARGB(255, 224, 222, 222),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          'Monnaie restante : $resteMonnaie FCFA',
                          style: const TextStyle(fontSize: 16),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: isSubmitting
                            ? null
                            : () {
                                if (!useMonnaie &&
                                    (resteMonnaie + (monnaie ?? 0)) >
                                        montantJ) {
                                  showDialog<void>(
                                    context: context,
                                    barrierDismissible: false,
                                    builder: (BuildContext context) {
                                      return AlertDialog(
                                        content: const Column(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Icon(Icons.error,
                                                color: Colors.red, size: 48.0),
                                            SizedBox(height: 16.0),
                                            Text(
                                              'Le total des monnaies dépasse le montant journalier. La monnaie doit être utilisée.',
                                              style: TextStyle(
                                                  fontWeight: FontWeight.bold),
                                            ),
                                          ],
                                        ),
                                        actions: [
                                          TextButton(
                                            onPressed: () =>
                                                Navigator.pop(context),
                                            child: const Text('OK'),
                                          ),
                                        ],
                                      );
                                    },
                                  );
                                  return;
                                }
                                if (montantSaisi >= 0 &&
                                    (quotient == nbrePaye) &&
                                    nbrePaye > 0) {
                                  _envoyerMontant(
                                      montantSaisi, resteMonnaie, monnaieExact);
                                  Navigator.pop(context);
                                } else {
                                  Navigator.pop(context);
                                  showDialog<void>(
                                    context: context,
                                    barrierDismissible: false,
                                    builder: (BuildContext context) {
                                      return AlertDialog(
                                        content: const Column(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Icon(Icons.error,
                                                color: Colors.red, size: 48.0),
                                            SizedBox(height: 16.0),
                                            Text(
                                              'Veuillez entrer le montant et le nombre de jours correspondant',
                                              style: TextStyle(
                                                  fontWeight: FontWeight.bold),
                                            ),
                                          ],
                                        ),
                                        actions: [
                                          TextButton(
                                            onPressed: () =>
                                                Navigator.pop(context),
                                            child: const Text('OK'),
                                          ),
                                        ],
                                      );
                                    },
                                  );
                                }
                                setState(() {
                                  _controllerMontant.clear();
                                  _controllerJours.clear();
                                  resteMonnaie = 0;
                                  quotient = 0;
                                  useMonnaie = false;
                                });
                              },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                          padding: const EdgeInsets.symmetric(
                              vertical: 12, horizontal: 24),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                        ),
                        child: isSubmitting
                            ? const CircularProgressIndicator(
                                valueColor:
                                    AlwaysStoppedAnimation<Color>(Colors.white),
                              )
                            : const Text(
                                'Enregistrer',
                                style: TextStyle(color: Colors.white),
                              ),
                      ),
                    ],
                  ),
                ),
              );
            },
          );
        },
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Détails de la Commande'),
        backgroundColor: const Color.fromARGB(255, 249, 221, 175),
        actions: buildAppBarActions(context),
      ),
      backgroundColor: Colors.grey[50],
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildModernHeader(),
              const SizedBox(height: 24),
              _buildDetailsSection(),
              const SizedBox(height: 24),
              _buildConfirmButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildModernHeader() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.orange.shade50,
            Colors.amber.shade50,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.orange.shade100),
        boxShadow: [
          BoxShadow(
            color: Colors.orange.withValues(alpha: 0.08),
            blurRadius: 12,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Colors.orange.shade300,
                        Colors.amber.shade400,
                      ],
                    ),
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.orange.withValues(alpha: 0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 3),
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.inventory_2,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.kit.option_kit,
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.w600,
                          color: Colors.orange.shade800,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Kit de commande boutique',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.orange.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Container(
              width: double.infinity,
              height: 200,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                color: Colors.white,
                border: Border.all(color: Colors.orange.shade100),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: CachedNetworkImage(
                  imageUrl: widget.kit.photo_kit,
                  fit: BoxFit.cover,
                  placeholder: (context, url) => Container(
                    color: Colors.grey.shade100,
                    child: const Center(
                      child: CircularProgressIndicator(
                        valueColor:
                            AlwaysStoppedAnimation<Color>(Colors.orange),
                      ),
                    ),
                  ),
                  errorWidget: (context, url, error) => Container(
                    color: Colors.grey.shade100,
                    child: Icon(
                      Icons.image_not_supported,
                      color: Colors.grey.shade400,
                      size: 48,
                    ),
                  ),
                ),
              ),
            ),
          ),
          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _buildDetailsSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Détails de la commande',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.grey.shade800,
            ),
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                child: _buildDetailCard(
                  'Montant Total',
                  '${widget.kit.montant} FCFA',
                  Icons.payments,
                  Colors.green,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildDetailCard(
                  'Coût Journalier',
                  '${widget.kit.cout_journal} FCFA',
                  Icons.calendar_today,
                  Colors.blue,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildDetailCard(
                  'Total Produits',
                  widget.kit.total_prod,
                  Icons.inventory,
                  Colors.orange,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildDetailCard(
                  'Livret',
                  'N° ${widget.kit.livret}',
                  Icons.book,
                  Colors.teal,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDetailCard(
      String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: color,
            size: 24,
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade600,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _showPaymentMethodDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: const Text(
            'Choisir un moyen de paiement',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 18,
            ),
            textAlign: TextAlign.center,
          ),
          content: SizedBox(
            width: double.maxFinite,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildPaymentMethodOption(
                  'Orange Money',
                  'assets/orange_money.png',
                  Colors.orange,
                  'orange_money',
                ),
                const SizedBox(height: 12),
                _buildPaymentMethodOption(
                  'MTN MoMo',
                  'assets/mtn_momo.png',
                  Colors.yellow.shade700,
                  'mtn_momo',
                ),
                const SizedBox(height: 12),
                _buildPaymentMethodOption(
                  'MOOV Money',
                  'assets/moov_money.png',
                  Colors.blue,
                  'moov_money',
                ),
                const SizedBox(height: 12),
                _buildPaymentMethodOption(
                  'Effectuer un versement',
                  Icons.account_balance_wallet,
                  Colors.green,
                  'versement_manuel',
                ),
                // const SizedBox(height: 12),
                // _buildPaymentMethodOption(
                //   'WAVE CI',
                //   'assets/wave_ci.png',
                //   const Color.fromARGB(255, 49, 97, 238),
                //   'wave_ci',
                // ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text(
                'Annuler',
                style: TextStyle(color: Colors.grey),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildPaymentMethodOption(
      String name, dynamic iconOrAsset, Color color, String paymentMethod) {
    return InkWell(
      onTap: () {
        Navigator.of(context).pop();
        _navigateToPaymentPage(paymentMethod);
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(12),
          color: Colors.grey.shade50,
        ),
        child: Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: iconOrAsset is String
                  ? Image.asset(
                      iconOrAsset,
                      width: 24,
                      height: 24,
                      errorBuilder: (context, error, stackTrace) {
                        return Icon(
                          Icons.payment,
                          color: color,
                          size: 24,
                        );
                      },
                    )
                  : Icon(
                      iconOrAsset,
                      color: color,
                      size: 24,
                    ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Text(
                name,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: Colors.grey.shade400,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToPaymentPage(String paymentMethod) {
    if (paymentMethod == 'versement_manuel') {
      _showNouvelleTontineSheet(context);
    } else {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => BoutiquePaymentMethodPage(
            paymentMethod: paymentMethod,
            clientId: widget.clientId,
            kitData: {
              'id_kit': widget.kit.id,
              'option_kit': widget.kit.option_kit,
              'montant': widget.kit.montant,
              'total_prod': widget.kit.total_prod,
              'cout_journal': widget.kit.cout_journal,
              'livret': widget.kit.livret,
              'photo_kit': widget.kit.photo_kit,
            },
            clientData: {
              'id_client': widget.clientId,
              'nom_client': 'Client Boutique',
              'prenom_client': '',
              'telephone_client': '',
            },
          ),
        ),
      );
    }
  }

  Widget _buildConfirmButton() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.green.shade400,
            Colors.teal.shade500,
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.green.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: isSubmitting
              ? null
              : () {
                  _showPaymentMethodDialog();
                },
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (isSubmitting) ...[
                  const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  ),
                  const SizedBox(width: 12),
                  const Text(
                    'Confirmation en cours...',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ] else ...[
                  const Icon(Icons.check_circle, color: Colors.white, size: 24),
                  const SizedBox(width: 12),
                  const Text(
                    'Payer et Confirmer',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _checkout(String idKit) async {
    setState(() {
      isSubmitting = true;
    });

    final provide = Provider.of<AuthProvider>(context, listen: false);
    final token = provide.token;
    final user = provide.user;
    String idPersonnel = user!['id_personnel'].toString();

    final requestData = {
      'clientId': widget.clientId,
      'personnelId': idPersonnel,
      'id_kit': idKit,
      'qte': 1,
    };

    try {
      final response = await http.post(
        Uri.parse(provide.getEndpoint('products/addCom.php')),
        headers: {'Authorization': token!, 'Content-Type': 'application/json'},
        body: jsonEncode(requestData),
      );

      if (response.statusCode == 200) {
        showDialog(
          context: context,
          builder: (_) => const SuccessDialog(),
        ).then((_) {
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
                builder: (context) =>
                    ClientDetailBoutiquePage(id: widget.clientId)),
          );
        });
      } else {
        _handleError(context, response);
      }
    } catch (error) {
      _handleError(context, error);
    } finally {
      setState(() {
        isSubmitting = false;
      });
    }
  }
}

void _handleError(BuildContext context, dynamic error) {
  showDialog(
    context: context,
    builder: (BuildContext context) {
      return AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: const Row(
          children: [
            Icon(Icons.error_outline, color: Colors.red),
            SizedBox(width: 10),
            Text('Impossible'),
          ],
        ),
        content: const Text(
          'Carnet insuffisant pour passer cette commande.',
          style: TextStyle(fontSize: 16),
        ),
        actions: [
          TextButton(
            style: TextButton.styleFrom(
              foregroundColor: Colors.white,
              backgroundColor: Colors.red,
              shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.all(Radius.circular(8)),
              ),
            ),
            onPressed: () {
              Navigator.pop(context);
            },
            child: const Text('OK'),
          ),
        ],
      );
    },
  );
}

class SuccessDialog extends StatelessWidget {
  const SuccessDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      content: const Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.check_circle,
            color: Colors.green,
            size: 48.0,
          ),
          SizedBox(height: 16.0),
          Text(
            'Commande passée avec succès!',
            style: TextStyle(
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () {
            Navigator.pop(context);
          },
          child: const Text('OK'),
        ),
      ],
    );
  }
}

class ModernKitCard extends StatelessWidget {
  final Kit kit;
  final String clientId;

  const ModernKitCard({
    super.key,
    required this.kit,
    required this.clientId,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.white,
            Colors.orange.shade50,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.orange.shade100,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.orange.withValues(alpha: 0.08),
            blurRadius: 8,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => ArticleDetailPage(
                  kit: kit,
                  clientId: clientId,
                ),
              ),
            );
          },
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(8),
            child: LayoutBuilder(
              builder: (context, constraints) {
                final imageHeight = constraints.maxHeight * 0.6;
                final infoHeight = constraints.maxHeight * 0.4;

                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(
                      height: imageHeight,
                      width: double.infinity,
                      child: Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          color: Colors.grey.shade100,
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: CachedNetworkImage(
                            imageUrl: kit.photo_kit,
                            fit: BoxFit.cover,
                            placeholder: (context, url) => Container(
                              color: Colors.grey.shade200,
                              child: const Center(
                                child: SizedBox(
                                  width: 16,
                                  height: 16,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                        Colors.orange),
                                  ),
                                ),
                              ),
                            ),
                            errorWidget: (context, url, error) => Container(
                              color: Colors.grey.shade200,
                              child: Icon(
                                Icons.image_not_supported,
                                color: Colors.grey.shade400,
                                size: 20,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 6),
                    SizedBox(
                      height: infoHeight - 6,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            kit.option_kit,
                            style: const TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                              color: Colors.black87,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 4, vertical: 1),
                            decoration: BoxDecoration(
                              color: Colors.green.shade100,
                              borderRadius: BorderRadius.circular(3),
                            ),
                            child: Text(
                              '${kit.cout_journal} F/j',
                              style: TextStyle(
                                fontSize: 9,
                                fontWeight: FontWeight.w600,
                                color: Colors.green.shade700,
                              ),
                            ),
                          ),
                          Row(
                            children: [
                              Icon(
                                Icons.payments,
                                size: 10,
                                color: Colors.orange.shade600,
                              ),
                              const SizedBox(width: 2),
                              Expanded(
                                child: Text(
                                  '${kit.montant} F',
                                  style: TextStyle(
                                    fontSize: 11,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.orange.shade700,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
        ),
      ),
    );
  }
}
