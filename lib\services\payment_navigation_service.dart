import 'package:flutter/material.dart';
import '../pages/clients/code_verification_page.dart';
import '../pages/payment_success_page.dart';
import '../pages/home.dart';
import 'app_logger.dart';

/// Service de navigation pour les paiements
class PaymentNavigationService {
  
  /// Navigue vers la page appropriée après un paiement réussi
  static void navigateAfterPaymentSuccess(
    BuildContext context, {
    String? transactionId,
    String? commandId,
    String? clientId,
    Map<String, dynamic>? clientData,
  }) {
    AppLogger.navigation('Navigation après succès de paiement - Transaction: $transactionId, Commande: $commandId, Client: $clientId');
    
    try {
      if (commandId != null && clientId != null && transactionId != null) {
        // Navigation vers la page de vérification de code
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder: (context) => CodeVerificationPage(
              clientId: clientId,
              commandeId: commandId,
              transactionId: transactionId,
              clientData: clientData ?? {},
            ),
          ),
        );
      } else {
        // Navigation vers la page de succès générique
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder: (context) => const PaymentSuccessPage(),
          ),
        );
      }
    } catch (e) {
      AppLogger.error('Erreur lors de la navigation après paiement', 'Navigation', e);
      // Fallback vers la page d'accueil
      Navigator.pushAndRemoveUntil(
        context,
        MaterialPageRoute(builder: (context) => const HomePage()),
        (route) => false,
      );
    }
  }
  
  /// Navigue vers la page appropriée après un échec de paiement
  static void navigateAfterPaymentFailure(
    BuildContext context, {
    String? reason,
  }) {
    AppLogger.navigation('Navigation après échec de paiement - Raison: $reason');
    
    try {
      // Retourner à la page précédente ou à l'accueil
      if (Navigator.canPop(context)) {
        Navigator.popUntil(context, (route) => route.isFirst);
      } else {
        Navigator.pushAndRemoveUntil(
          context,
          MaterialPageRoute(builder: (context) => const HomePage()),
          (route) => false,
        );
      }
    } catch (e) {
      AppLogger.error('Erreur lors de la navigation après échec', 'Navigation', e);
      // Fallback vers la page d'accueil
      Navigator.pushAndRemoveUntil(
        context,
        MaterialPageRoute(builder: (context) => const HomePage()),
        (route) => false,
      );
    }
  }
  
  /// Navigue vers la page appropriée pour un paiement en attente
  static void navigateAfterPaymentPending(
    BuildContext context, {
    String? transactionId,
  }) {
    AppLogger.navigation('Navigation après paiement en attente - Transaction: $transactionId');
    
    try {
      // Rester sur la page actuelle ou retourner à l'accueil
      Navigator.pushAndRemoveUntil(
        context,
        MaterialPageRoute(builder: (context) => const HomePage()),
        (route) => false,
      );
    } catch (e) {
      AppLogger.error('Erreur lors de la navigation après paiement en attente', 'Navigation', e);
    }
  }
  
  /// Affiche un dialog de confirmation avant navigation
  static void showNavigationDialog(
    BuildContext context, {
    required String title,
    required String message,
    required VoidCallback onConfirm,
    VoidCallback? onCancel,
    Color? titleColor,
    IconData? icon,
  }) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (icon != null) ...[
                Icon(
                  icon,
                  size: 60,
                  color: titleColor ?? Colors.blue,
                ),
                const SizedBox(height: 16),
              ],
              Text(
                title,
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: titleColor ?? Colors.blue,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                message,
                textAlign: TextAlign.center,
                style: const TextStyle(fontSize: 16),
              ),
            ],
          ),
          actions: [
            if (onCancel != null)
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  onCancel();
                },
                child: const Text('Annuler'),
              ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                onConfirm();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: titleColor ?? Colors.blue,
                foregroundColor: Colors.white,
              ),
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }
  
  /// Affiche un dialog de succès avec navigation automatique
  static void showSuccessDialogWithNavigation(
    BuildContext context, {
    required String message,
    String? transactionId,
    String? commandId,
    String? clientId,
    Map<String, dynamic>? clientData,
  }) {
    showNavigationDialog(
      context,
      title: 'Paiement Réussi',
      message: message,
      icon: Icons.check_circle,
      titleColor: Colors.green,
      onConfirm: () {
        navigateAfterPaymentSuccess(
          context,
          transactionId: transactionId,
          commandId: commandId,
          clientId: clientId,
          clientData: clientData,
        );
      },
    );
  }
  
  /// Affiche un dialog d'échec avec navigation
  static void showFailureDialogWithNavigation(
    BuildContext context, {
    required String message,
    String? reason,
  }) {
    showNavigationDialog(
      context,
      title: 'Paiement Échoué',
      message: message,
      icon: Icons.error,
      titleColor: Colors.red,
      onConfirm: () {
        navigateAfterPaymentFailure(context, reason: reason);
      },
    );
  }
  
  /// Affiche un dialog de paiement en attente
  static void showPendingDialog(
    BuildContext context, {
    String? transactionId,
  }) {
    showNavigationDialog(
      context,
      title: 'Paiement en Attente',
      message: 'Votre paiement est en cours de traitement. Vous recevrez une notification une fois le traitement terminé.',
      icon: Icons.hourglass_empty,
      titleColor: Colors.orange,
      onConfirm: () {
        navigateAfterPaymentPending(context, transactionId: transactionId);
      },
    );
  }
}
