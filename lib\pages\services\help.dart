import 'package:callitris/pages/menu.dart';
import 'package:flutter/material.dart';

class HelpPage extends StatelessWidget {
  const HelpPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: const Color.fromARGB(255, 249, 221, 175),
        title: const Text('Aide'),
        actions: buildAppBarActions(context),
      ),
      body: const Center(
        child: Text(
          'Page en maintenance',
          style: TextStyle(color: Colors.red, fontSize: 20),
        ),
      ),
    );
  }
}
