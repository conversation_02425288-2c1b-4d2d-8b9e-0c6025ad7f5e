<?php
/**
 * Fichier de configuration exemple pour les callbacks de paiement
 * Copiez ce fichier vers config.php et modifiez les valeurs selon votre environnement
 */

return [
    // Configuration générale
    'app_scheme' => 'callitris',
    'environment' => 'production', // 'development', 'staging', 'production'
    'debug_mode' => false,
    
    // Configuration des logs
    'logging' => [
        'enabled' => true,
        'level' => 'info', // 'debug', 'info', 'warning', 'error'
        'max_file_size' => 10 * 1024 * 1024, // 10MB
        'max_files' => 5,
        'log_directory' => __DIR__ . '/logs',
    ],
    
    // Configuration Wave Money
    'wave' => [
        'api_key' => 'your_wave_api_key_here',
        'secret_key' => 'your_wave_secret_key_here',
        'api_url' => 'https://api.wave.com/v1',
        'webhook_secret' => 'your_wave_webhook_secret_here',
        'timeout' => 30, // secondes
        'verify_ssl' => true,
    ],
    
    // Configuration CinetPay
    'cinetpay' => [
        'api_key' => 'your_cinetpay_api_key_here',
        'site_id' => 'your_cinetpay_site_id_here',
        'secret_key' => 'your_cinetpay_secret_key_here',
        'api_url' => 'https://api-checkout.cinetpay.com/v2',
        'timeout' => 30, // secondes
        'verify_ssl' => true,
    ],
    
    // Configuration Orange Money
    'orange_money' => [
        'merchant_key' => 'your_orange_merchant_key_here',
        'api_url' => 'https://api.orange.com/orange-money-webpay/dev/v1',
        'timeout' => 30,
    ],
    
    // Configuration MTN Mobile Money
    'mtn_momo' => [
        'api_key' => 'your_mtn_api_key_here',
        'api_secret' => 'your_mtn_api_secret_here',
        'subscription_key' => 'your_mtn_subscription_key_here',
        'api_url' => 'https://sandbox.momodeveloper.mtn.com',
        'timeout' => 30,
    ],
    
    // Configuration Moov Money
    'moov_money' => [
        'merchant_id' => 'your_moov_merchant_id_here',
        'api_key' => 'your_moov_api_key_here',
        'api_url' => 'https://api.moov-africa.ci',
        'timeout' => 30,
    ],
    
    // URLs de callback
    'callback_urls' => [
        'base_url' => 'https://dev-mani.io/teams/api.callitris-distribution.com/deep_links_config/backend',
        'wave_callback' => '/wave_callback.php',
        'cinetpay_callback' => '/cinetpay_callback.php',
        'generic_callback' => '/payment_redirect.php',
    ],
    
    // Configuration de sécurité
    'security' => [
        'allowed_ips' => [
            // IPs autorisées pour les callbacks (optionnel)
            // '***********',
            // '********',
        ],
        'rate_limiting' => [
            'enabled' => true,
            'max_requests_per_minute' => 60,
            'max_requests_per_hour' => 1000,
        ],
        'signature_verification' => [
            'wave' => true,
            'cinetpay' => true,
            'strict_mode' => true, // Rejeter les requêtes sans signature valide
        ],
    ],
    
    // Configuration de la base de données (optionnel)
    'database' => [
        'enabled' => false,
        'host' => 'localhost',
        'port' => 3306,
        'database' => 'callitris_payments',
        'username' => 'your_db_username',
        'password' => 'your_db_password',
        'charset' => 'utf8mb4',
        'table_prefix' => 'cp_',
    ],
    
    // Configuration des notifications push (optionnel)
    'push_notifications' => [
        'enabled' => false,
        'firebase' => [
            'server_key' => 'your_firebase_server_key_here',
            'api_url' => 'https://fcm.googleapis.com/fcm/send',
        ],
    ],
    
    // Configuration email (optionnel)
    'email' => [
        'enabled' => false,
        'smtp_host' => 'smtp.gmail.com',
        'smtp_port' => 587,
        'smtp_username' => '<EMAIL>',
        'smtp_password' => 'your_email_password',
        'from_email' => '<EMAIL>',
        'from_name' => 'Callitris Payments',
    ],
    
    // Configuration de monitoring
    'monitoring' => [
        'enabled' => true,
        'webhook_url' => '', // URL pour les alertes (Slack, Discord, etc.)
        'alert_on_errors' => true,
        'alert_on_high_volume' => true,
        'metrics' => [
            'track_response_time' => true,
            'track_success_rate' => true,
            'track_error_rate' => true,
        ],
    ],
    
    // Configuration de cache (optionnel)
    'cache' => [
        'enabled' => false,
        'driver' => 'file', // 'file', 'redis', 'memcached'
        'ttl' => 3600, // Time to live en secondes
        'redis' => [
            'host' => '127.0.0.1',
            'port' => 6379,
            'password' => '',
            'database' => 0,
        ],
    ],
    
    // Configuration de développement
    'development' => [
        'mock_payments' => false, // Simuler les paiements en développement
        'test_mode' => false, // Mode test pour les providers
        'debug_headers' => false, // Afficher les headers de debug
        'log_all_requests' => true, // Logger toutes les requêtes
    ],
    
    // Messages personnalisés
    'messages' => [
        'success' => [
            'fr' => 'Paiement effectué avec succès',
            'en' => 'Payment completed successfully',
        ],
        'failure' => [
            'fr' => 'Échec du paiement',
            'en' => 'Payment failed',
        ],
        'pending' => [
            'fr' => 'Paiement en cours de traitement',
            'en' => 'Payment is being processed',
        ],
    ],
    
    // Configuration des timeouts
    'timeouts' => [
        'payment_verification' => 30, // secondes
        'callback_processing' => 10, // secondes
        'database_query' => 5, // secondes
        'external_api' => 30, // secondes
    ],
    
    // Configuration des retry
    'retry' => [
        'max_attempts' => 3,
        'delay_seconds' => 2,
        'exponential_backoff' => true,
    ],
    
    // Configuration des formats
    'formats' => [
        'date_format' => 'Y-m-d H:i:s',
        'timezone' => 'Africa/Abidjan',
        'currency' => 'XOF',
        'locale' => 'fr_CI',
    ],
];
?>
