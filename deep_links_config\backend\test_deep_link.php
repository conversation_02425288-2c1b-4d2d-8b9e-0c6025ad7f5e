<?php
/**
 * Page de test pour les deep links Callitris
 * Utilisez cette page pour tester manuellement les deep links
 */

// Paramètres de test par défaut
$testParams = [
    'transaction_id' => $_GET['transaction_id'] ?? 'test_' . time(),
    'command_id' => $_GET['command_id'] ?? 'cmd_test_123',
    'client_id' => $_GET['client_id'] ?? 'client_test_456',
    'status' => $_GET['status'] ?? 'success',
    'reason' => $_GET['reason'] ?? 'Test de deep link',
];

// Générer le deep link
function generateTestDeepLink($status, $params) {
    $baseUrl = 'callitris://payment/' . $status;
    
    $queryParams = [];
    if (!empty($params['transaction_id'])) $queryParams['transaction_id'] = $params['transaction_id'];
    if (!empty($params['command_id'])) $queryParams['command_id'] = $params['command_id'];
    if (!empty($params['client_id'])) $queryParams['client_id'] = $params['client_id'];
    if (!empty($params['reason']) && $status === 'failure') $queryParams['reason'] = $params['reason'];
    
    if (!empty($queryParams)) {
        $baseUrl .= '?' . http_build_query($queryParams);
    }
    
    return $baseUrl;
}

$deepLink = generateTestDeepLink($testParams['status'], $testParams);
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Deep Links Callitris</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .button {
            display: inline-block;
            padding: 12px 24px;
            margin: 10px 5px;
            background-color: #2196F3;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            border: none;
            cursor: pointer;
            font-size: 16px;
        }
        .button:hover {
            background-color: #1976D2;
        }
        .success { background-color: #4CAF50; }
        .failure { background-color: #f44336; }
        .pending { background-color: #ff9800; }
        .code {
            background: #f0f0f0;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            word-break: break-all;
            margin: 10px 0;
        }
        .form-group {
            margin: 15px 0;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input, .form-group select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 3px;
            box-sizing: border-box;
        }
        .status-indicator {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            text-align: center;
            font-weight: bold;
        }
        .instructions {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔗 Test Deep Links Callitris</h1>
            <p>Utilisez cette page pour tester les deep links de votre application</p>
        </div>

        <div class="instructions">
            <h3>📱 Instructions:</h3>
            <ol>
                <li>Assurez-vous que l'application Callitris est installée sur votre appareil</li>
                <li>Cliquez sur un des boutons de test ci-dessous</li>
                <li>L'application devrait s'ouvrir automatiquement</li>
                <li>Si l'app ne s'ouvre pas, vérifiez la configuration des deep links</li>
            </ol>
        </div>

        <div class="test-section">
            <h3>🧪 Test Rapide</h3>
            <p>Deep link généré:</p>
            <div class="code"><?php echo htmlspecialchars($deepLink); ?></div>
            
            <div class="status-indicator <?php echo $testParams['status']; ?>">
                Statut: <?php echo strtoupper($testParams['status']); ?>
            </div>

            <button onclick="testDeepLink('<?php echo addslashes($deepLink); ?>')" class="button <?php echo $testParams['status']; ?>">
                🚀 Tester ce Deep Link
            </button>
        </div>

        <div class="test-section">
            <h3>⚙️ Test Personnalisé</h3>
            <form method="GET" action="">
                <div class="form-group">
                    <label for="status">Statut du paiement:</label>
                    <select name="status" id="status">
                        <option value="success" <?php echo $testParams['status'] === 'success' ? 'selected' : ''; ?>>Succès</option>
                        <option value="failure" <?php echo $testParams['status'] === 'failure' ? 'selected' : ''; ?>>Échec</option>
                        <option value="pending" <?php echo $testParams['status'] === 'pending' ? 'selected' : ''; ?>>En attente</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="transaction_id">ID Transaction:</label>
                    <input type="text" name="transaction_id" id="transaction_id" value="<?php echo htmlspecialchars($testParams['transaction_id']); ?>">
                </div>
                
                <div class="form-group">
                    <label for="command_id">ID Commande:</label>
                    <input type="text" name="command_id" id="command_id" value="<?php echo htmlspecialchars($testParams['command_id']); ?>">
                </div>
                
                <div class="form-group">
                    <label for="client_id">ID Client:</label>
                    <input type="text" name="client_id" id="client_id" value="<?php echo htmlspecialchars($testParams['client_id']); ?>">
                </div>
                
                <div class="form-group">
                    <label for="reason">Raison (pour échec):</label>
                    <input type="text" name="reason" id="reason" value="<?php echo htmlspecialchars($testParams['reason']); ?>">
                </div>
                
                <button type="submit" class="button">🔄 Générer Nouveau Test</button>
            </form>
        </div>

        <div class="test-section">
            <h3>🎯 Tests Prédéfinis</h3>
            <button onclick="testDeepLink('callitris://payment/success?transaction_id=test_success_<?php echo time(); ?>&command_id=cmd_123&client_id=client_456')" class="button success">
                ✅ Test Paiement Réussi
            </button>
            
            <button onclick="testDeepLink('callitris://payment/failure?transaction_id=test_failure_<?php echo time(); ?>&reason=Paiement%20annulé')" class="button failure">
                ❌ Test Paiement Échoué
            </button>
            
            <button onclick="testDeepLink('callitris://payment/pending?transaction_id=test_pending_<?php echo time(); ?>')" class="button pending">
                ⏳ Test Paiement En Attente
            </button>
        </div>

        <div class="test-section">
            <h3>🔧 Debugging</h3>
            <p><strong>User Agent:</strong> <?php echo htmlspecialchars($_SERVER['HTTP_USER_AGENT'] ?? 'Non défini'); ?></p>
            <p><strong>Timestamp:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
            <p><strong>IP:</strong> <?php echo $_SERVER['REMOTE_ADDR'] ?? 'Non défini'; ?></p>
            
            <button onclick="copyToClipboard('<?php echo addslashes($deepLink); ?>')" class="button">
                📋 Copier Deep Link
            </button>
            
            <button onclick="showQRCode('<?php echo addslashes($deepLink); ?>')" class="button">
                📱 QR Code
            </button>
        </div>
    </div>

    <script>
        function testDeepLink(deepLink) {
            console.log('Testing deep link:', deepLink);
            
            // Méthode 1: Redirection directe
            window.location.href = deepLink;
            
            // Méthode 2: Créer un lien et le cliquer
            setTimeout(function() {
                const link = document.createElement('a');
                link.href = deepLink;
                link.style.display = 'none';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            }, 100);
            
            // Méthode 3: window.open comme fallback
            setTimeout(function() {
                try {
                    window.open(deepLink, '_self');
                } catch(e) {
                    console.error('Erreur ouverture app:', e);
                    alert('Impossible d\'ouvrir l\'application. Vérifiez qu\'elle est installée.');
                }
            }, 500);
        }
        
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                alert('Deep link copié dans le presse-papiers!');
            }).catch(function(err) {
                console.error('Erreur copie:', err);
                prompt('Copiez ce deep link:', text);
            });
        }
        
        function showQRCode(text) {
            const qrUrl = `https://api.qrserver.com/v1/create-qr-code/?size=300x300&data=${encodeURIComponent(text)}`;
            const newWindow = window.open('', '_blank', 'width=400,height=400');
            newWindow.document.write(`
                <html>
                    <head><title>QR Code - Deep Link</title></head>
                    <body style="text-align: center; padding: 20px;">
                        <h3>QR Code pour Deep Link</h3>
                        <img src="${qrUrl}" alt="QR Code" style="border: 1px solid #ccc;">
                        <p style="word-break: break-all; font-family: monospace; font-size: 12px;">${text}</p>
                    </body>
                </html>
            `);
        }
    </script>
</body>
</html>
