<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Gestion des requêtes OPTIONS (preflight)
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Vérification de la méthode HTTP
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'Méthode non autorisée'
    ]);
    exit();
}

// Vérification de l'autorisation
$headers = getallheaders();
if (!isset($headers['Authorization'])) {
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'message' => 'Token d\'autorisation manquant'
    ]);
    exit();
}

try {
    // Configuration de la base de données
    $host = 'localhost';
    $dbname = 'callitris';
    $username = 'root';
    $password = '';

    // Connexion à la base de données
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Requête pour récupérer tous les livrets disponibles (sans dépendance de durée)
    // Utilise la même structure que products/getLivret.php mais sans filtre de durée
    $stmt = $pdo->prepare("
        SELECT DISTINCT l.id_livret, l.code_livret
        FROM livret l
        INNER JOIN duree d ON l.duree_id = d.id_duree
        WHERE l.code_livret IS NOT NULL
        AND l.code_livret != ''
        AND l.code_livret != '0'
        ORDER BY CAST(l.code_livret AS UNSIGNED) ASC, l.code_livret ASC
    ");

    $stmt->execute();
    $livrets = $stmt->fetchAll(PDO::FETCH_ASSOC);

    if (empty($livrets)) {
        echo json_encode([
            'success' => false,
            'message' => 'Aucun livret disponible',
            'livrets' => []
        ]);
    } else {
        echo json_encode([
            'success' => true,
            'message' => 'Livrets récupérés avec succès',
            'livrets' => $livrets,
            'count' => count($livrets)
        ]);
    }

} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Erreur de base de données: ' . $e->getMessage()
    ]);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Erreur serveur: ' . $e->getMessage()
    ]);
}
?>
