import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:provider/provider.dart';
import 'package:callitris/pages/auth_provider.dart';
import 'package:intl/intl.dart';

class NewClientPage extends StatefulWidget {
  const NewClientPage({super.key});

  @override
  State<NewClientPage> createState() => _NewClientPageState();
}

class _NewClientPageState extends State<NewClientPage> {
  final TextEditingController _searchController = TextEditingController();
  final TextEditingController _startDateController = TextEditingController();
  final TextEditingController _endDateController = TextEditingController();
  List<Map<String, dynamic>> _allClients = [];
  List<Map<String, dynamic>> _filteredClients = [];
  bool _isLoading = true;
  bool _disposed = false;
  DateTime? _selectedStartDate;
  DateTime? _selectedEndDate;

  @override
  void initState() {
    super.initState();
    _fetchNewClients();
    _searchController.addListener(_filterClients);
  }

  @override
  void dispose() {
    _disposed = true;
    _searchController.dispose();
    _startDateController.dispose();
    _endDateController.dispose();
    super.dispose();
  }

  Future<void> _fetchNewClients() async {
    try {
      final provide = Provider.of<AuthProvider>(context, listen: false);
      final user = Provider.of<AuthProvider>(context, listen: false).user;
      final token = Provider.of<AuthProvider>(context, listen: false).token;

      if (token == null || user == null) {
        if (mounted && !_disposed) {
          setState(() => _isLoading = false);
        }
        return;
      }

      String idPersonnel = user['id_personnel'].toString();

      // Utiliser la date d'aujourd'hui par défaut pour debut et fin
      String today = DateFormat('yyyy-MM-dd').format(DateTime.now());
      String debut = _selectedStartDate != null
          ? DateFormat('yyyy-MM-dd').format(_selectedStartDate!)
          : today;
      String fin = _selectedEndDate != null
          ? DateFormat('yyyy-MM-dd').format(_selectedEndDate!)
          : today;
      print('debut: $debut, fin: $fin');
      final response = await http.get(
        Uri.parse(provide.getEndpoint(
            'client/getNewClients.php?personnel_id=$idPersonnel&debut=$debut&fin=$fin')),
        headers: {'Authorization': token, 'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        if (response.body.trim().isEmpty) {
          print('Erreur: Réponse vide du serveur');
          if (mounted && !_disposed) {
            setState(() {
              _allClients = [];
              _filteredClients = [];
              _isLoading = false;
            });
          }
          return;
        }

        try {
          final Map<String, dynamic> responseData = jsonDecode(response.body);

          if (responseData.containsKey('data') &&
              responseData['data'] is List) {
            final List<dynamic> clientsData = responseData['data'];
            if (mounted && !_disposed) {
              setState(() {
                _allClients = clientsData.cast<Map<String, dynamic>>();
                _filteredClients = List.from(_allClients);
                _isLoading = false;
              });
            }
          } else {
            if (mounted && !_disposed) {
              setState(() {
                _allClients = [];
                _filteredClients = [];
                _isLoading = false;
              });
            }
          }
        } catch (e) {
          print('Erreur parsing JSON: $e');
          if (mounted && !_disposed) {
            setState(() {
              _allClients = [];
              _filteredClients = [];
              _isLoading = false;
            });
          }
        }
      } else {
        print('Erreur : ${response.statusCode}');
        print('Corps de la réponse d\'erreur: "${response.body}"');
        if (mounted && !_disposed) {
          setState(() => _isLoading = false);
        }
      }
    } catch (error) {
      print('Erreur lors de la récupération des nouveaux clients: $error');
      if (mounted && !_disposed) {
        setState(() => _isLoading = false);
      }
    }
  }

  void _filterClients() {
    String searchTerm = _searchController.text.toLowerCase();

    if (mounted && !_disposed) {
      setState(() {
        _filteredClients = _allClients.where((client) {
          // Filtrage par texte de recherche
          bool matchesSearch = searchTerm.isEmpty ||
              client['nom_client']
                      ?.toString()
                      .toLowerCase()
                      .contains(searchTerm) ==
                  true ||
              client['prenom_client']
                      ?.toString()
                      .toLowerCase()
                      .contains(searchTerm) ==
                  true ||
              client['telephone_client']?.toString().contains(searchTerm) ==
                  true ||
              client['code_client']
                      ?.toString()
                      .toLowerCase()
                      .contains(searchTerm) ==
                  true;

          // Filtrage par date
          bool matchesDate = true;
          if (_selectedStartDate != null || _selectedEndDate != null) {
            try {
              // Utiliser date_ajout au lieu de date_creation
              String dateString = client['date_ajout'] ?? '';
              DateTime clientDate = DateTime.parse(dateString);

              if (_selectedStartDate != null && _selectedEndDate != null) {
                matchesDate = clientDate.isAfter(_selectedStartDate!
                        .subtract(const Duration(days: 1))) &&
                    clientDate.isBefore(
                        _selectedEndDate!.add(const Duration(days: 1)));
              } else if (_selectedStartDate != null) {
                matchesDate = clientDate.isAfter(
                    _selectedStartDate!.subtract(const Duration(days: 1)));
              } else if (_selectedEndDate != null) {
                matchesDate = clientDate
                    .isBefore(_selectedEndDate!.add(const Duration(days: 1)));
              }
            } catch (e) {
              matchesDate = false;
            }
          }

          return matchesSearch && matchesDate;
        }).toList();
      });
    }
  }

  String _formatDateForDisplay(DateTime date) {
    return DateFormat('dd/MM/yyyy').format(date);
  }

  Future<void> _selectDateRange() async {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Filtrer par période'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: _startDateController,
                decoration: InputDecoration(
                  labelText: 'Date de début',
                  hintText: 'JJ/MM/AAAA',
                  prefixIcon: GestureDetector(
                    onTap: () async {
                      final DateTime? picked = await showDatePicker(
                        context: context,
                        initialDate: _selectedStartDate ?? DateTime.now(),
                        firstDate: DateTime(2020),
                        lastDate: DateTime.now().add(const Duration(days: 365)),
                        locale: const Locale('fr', 'FR'),
                        helpText: 'Sélectionner la date de début',
                        cancelText: 'Annuler',
                        confirmText: 'Confirmer',
                      );
                      if (picked != null) {
                        if (mounted && !_disposed) {
                          setState(() {
                            _selectedStartDate = picked;
                            _startDateController.text =
                                _formatDateForDisplay(picked);
                          });
                        }
                      }
                    },
                    child: const Icon(Icons.calendar_today),
                  ),
                  border: const OutlineInputBorder(),
                ),
                readOnly: true,
              ),
              const SizedBox(height: 16),
              TextField(
                controller: _endDateController,
                decoration: InputDecoration(
                  labelText: 'Date de fin',
                  hintText: 'JJ/MM/AAAA',
                  prefixIcon: GestureDetector(
                    onTap: () async {
                      final DateTime? picked = await showDatePicker(
                        context: context,
                        initialDate: _selectedEndDate ?? DateTime.now(),
                        firstDate: DateTime(2020),
                        lastDate: DateTime.now().add(const Duration(days: 365)),
                        locale: const Locale('fr', 'FR'),
                        helpText: 'Sélectionner la date de fin',
                        cancelText: 'Annuler',
                        confirmText: 'Confirmer',
                      );
                      if (picked != null) {
                        if (mounted && !_disposed) {
                          setState(() {
                            _selectedEndDate = picked;
                            _endDateController.text =
                                _formatDateForDisplay(picked);
                          });
                        }
                      }
                    },
                    child: const Icon(Icons.calendar_today),
                  ),
                  border: const OutlineInputBorder(),
                ),
                readOnly: true,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('Annuler'),
            ),
            TextButton(
              onPressed: () {
                _fetchNewClients();
                Navigator.of(context).pop();
              },
              child: const Text('Appliquer'),
            ),
          ],
        );
      },
    );
  }

  void _clearDateFilter() {
    if (mounted && !_disposed) {
      setState(() {
        _selectedStartDate = null;
        _selectedEndDate = null;
        _startDateController.clear();
        _endDateController.clear();
      });
    }
    _filterClients();
  }

  String _formatDate(String? dateString) {
    if (dateString == null) return 'N/A';
    try {
      DateTime date = DateTime.parse(dateString);
      return DateFormat('dd/MM/yyyy').format(date);
    } catch (e) {
      return 'N/A';
    }
  }

  String _formatTime(String? timeString) {
    if (timeString == null) return 'N/A';
    try {
      // Si c'est déjà au format HH:mm, on le retourne tel quel
      if (timeString.contains(':') && timeString.length <= 8) {
        return timeString.substring(0, 5); // Prendre seulement HH:mm
      }
      // Sinon, essayer de parser comme DateTime
      DateTime time = DateTime.parse(timeString);
      return DateFormat('HH:mm').format(time);
    } catch (e) {
      return 'N/A';
    }
  }

  String _getAccountType(Map<String, dynamic> client) {
    // Vérifier les différents champs possibles pour le type de compte
    if (client['type_compte'] != null) {
      return client['type_compte'].toString().toUpperCase();
    }
    if (client['nom_compte'] != null) {
      return client['nom_compte'].toString().toUpperCase();
    }
    if (client['compte_type'] != null) {
      return client['compte_type'].toString().toUpperCase();
    }
    // Vérifier si c'est un compte boutique ou tontine basé sur d'autres champs
    if (client['boutique'] == '1' || client['is_boutique'] == true) {
      return 'BOUTIQUE';
    }
    if (client['tontine'] == '1' || client['is_tontine'] == true) {
      return 'TONTINE';
    }
    // Par défaut
    return 'NOUVEAU';
  }

  Color _getAccountTypeColor(Map<String, dynamic> client) {
    String accountType = _getAccountType(client);
    switch (accountType) {
      case 'BOUTIQUE':
        return const Color(0xFF2563EB); // Bleu moderne
      case 'TONTINE':
        return const Color(0xFF7C3AED); // Violet moderne
      case 'NOUVEAU':
      default:
        return Colors.green; // Vert pour nouveau client
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          'Nouveaux Clients',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color.fromARGB(255, 246, 196, 88),
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              if (mounted && !_disposed) {
                setState(() => _isLoading = true);
              }
              _fetchNewClients();
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Barre de recherche et filtres améliorée
          Container(
            padding: const EdgeInsets.all(20.0),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  spreadRadius: 0,
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              children: [
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: const Color.fromARGB(255, 246, 196, 88)
                          .withValues(alpha: 0.3),
                      width: 2,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: const Color.fromARGB(255, 246, 196, 88)
                            .withValues(alpha: 0.1),
                        spreadRadius: 0,
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      labelText: 'Rechercher un nouveau client...',
                      hintText: 'Nom, prénom, téléphone, code...',
                      prefixIcon: Container(
                        padding: const EdgeInsets.all(12),
                        child: Icon(
                          Icons.person_search_rounded,
                          color: const Color.fromARGB(255, 246, 196, 88),
                          size: 24,
                        ),
                      ),
                      suffixIcon: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          if (_searchController.text.isNotEmpty)
                            IconButton(
                              icon: Icon(
                                Icons.clear_rounded,
                                color: Colors.grey.shade600,
                                size: 20,
                              ),
                              onPressed: () {
                                _searchController.clear();
                                _filterClients();
                              },
                              tooltip: 'Effacer la recherche',
                            ),
                          Container(
                            margin: const EdgeInsets.only(right: 8),
                            decoration: BoxDecoration(
                              color: (_selectedStartDate != null &&
                                      _selectedEndDate != null)
                                  ? const Color.fromARGB(255, 246, 196, 88)
                                  : Colors.grey.shade100,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: IconButton(
                              icon: Icon(
                                Icons.date_range_rounded,
                                color: (_selectedStartDate != null &&
                                        _selectedEndDate != null)
                                    ? Colors.white
                                    : Colors.grey.shade600,
                                size: 20,
                              ),
                              onPressed: _selectDateRange,
                              tooltip: 'Filtrer par période',
                            ),
                          ),
                        ],
                      ),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(16),
                        borderSide: BorderSide.none,
                      ),
                      filled: true,
                      fillColor: Colors.grey.shade50,
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 20,
                        vertical: 16,
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    if (_selectedStartDate != null && _selectedEndDate != null)
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 8,
                        ),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              const Color.fromARGB(255, 246, 196, 88),
                              const Color.fromARGB(255, 246, 196, 88)
                                  .withValues(alpha: 0.8),
                            ],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                          borderRadius: BorderRadius.circular(20),
                          boxShadow: [
                            BoxShadow(
                              color: const Color.fromARGB(255, 246, 196, 88)
                                  .withValues(alpha: 0.3),
                              spreadRadius: 0,
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Icon(
                              Icons.date_range_rounded,
                              color: Colors.white,
                              size: 16,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              '${DateFormat('dd/MM').format(_selectedStartDate!)} - ${DateFormat('dd/MM').format(_selectedEndDate!)}',
                              style: const TextStyle(
                                fontSize: 12,
                                color: Colors.white,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            const SizedBox(width: 8),
                            GestureDetector(
                              onTap: _clearDateFilter,
                              child: Container(
                                padding: const EdgeInsets.all(2),
                                decoration: BoxDecoration(
                                  color: Colors.white.withValues(alpha: 0.2),
                                  borderRadius: BorderRadius.circular(10),
                                ),
                                child: const Icon(
                                  Icons.close_rounded,
                                  color: Colors.white,
                                  size: 14,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    const Spacer(),
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade100,
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.person_search_rounded,
                            size: 16,
                            color: Colors.grey.shade600,
                          ),
                          const SizedBox(width: 6),
                          Text(
                            '${_filteredClients.length} nouveau${_filteredClients.length > 1 ? 'x' : ''} client${_filteredClients.length > 1 ? 's' : ''}',
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                              color: Colors.grey.shade700,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          // Liste des clients
          Expanded(
            child: _isLoading
                ? const Center(
                    child: CircularProgressIndicator(
                      color: Color(0xFF3B82F6),
                    ),
                  )
                : _filteredClients.isEmpty
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.person_search,
                              size: 80,
                              color: Colors.grey[400],
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'Aucun client trouvé',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.w500,
                                color: Colors.grey[600],
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Essayez de modifier vos critères de recherche',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey[500],
                              ),
                            ),
                          ],
                        ),
                      )
                    : ListView.builder(
                        padding: const EdgeInsets.symmetric(horizontal: 20),
                        itemCount: _filteredClients.length,
                        itemBuilder: (context, index) {
                          final client = _filteredClients[index];
                          return _buildClientCard(client);
                        },
                      ),
          ),
        ],
      ),
    );
  }

  Widget _buildClientCard(Map<String, dynamic> client) {
    const Color clientColor = Color.fromARGB(255, 246, 196, 88);

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 6.0, horizontal: 16.0),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16.0),
        color: Colors.white,
        border: Border.all(color: clientColor.withValues(alpha: 0.3), width: 1),
        boxShadow: [
          BoxShadow(
            color: clientColor.withValues(alpha: 0.1),
            spreadRadius: 0,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Header avec fond coloré
          Container(
            padding: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
              color: clientColor.withValues(alpha: 0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16.0),
                topRight: Radius.circular(16.0),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: clientColor,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.person_add_rounded,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${client['nom_client'] ?? ''} ${client['prenom_client'] ?? ''}'
                            .trim(),
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: clientColor,
                        ),
                      ),
                      Text(
                        'Code: ${client['code_client'] ?? 'N/A'}',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade700,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                  decoration: BoxDecoration(
                    color: _getAccountTypeColor(client),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    _getAccountType(client),
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Contenu principal
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                // Téléphone
                if (client['telephone_client'] != null) ...[
                  _buildInfoRow(Icons.phone_rounded, 'Téléphone',
                      client['telephone_client']),
                  const SizedBox(height: 8),
                ],

                // Adresse
                if (client['domicile_client'] != null) ...[
                  _buildInfoRow(Icons.location_on_rounded, 'Adresse',
                      client['domicile_client']),
                  const SizedBox(height: 8),
                ],

                const SizedBox(height: 4),

                // Date et heure
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade50,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.calendar_month_rounded,
                              size: 16, color: Colors.grey.shade600),
                          const SizedBox(width: 6),
                          Text(
                            _formatDate(client['date_ajout']),
                            style: TextStyle(
                              fontSize: 13,
                              color: Colors.grey.shade700,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                      Row(
                        children: [
                          Icon(Icons.access_time_rounded,
                              size: 16, color: Colors.grey.shade600),
                          const SizedBox(width: 6),
                          Text(
                            _formatTime(client['heure_ajout']),
                            style: TextStyle(
                              fontSize: 13,
                              color: Colors.grey.shade700,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Row(
      children: [
        Icon(icon, size: 16, color: Colors.grey.shade600),
        const SizedBox(width: 8),
        Text(
          '$label: ',
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey.shade600,
            fontWeight: FontWeight.w500,
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade700,
              fontWeight: FontWeight.w600,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }
}
