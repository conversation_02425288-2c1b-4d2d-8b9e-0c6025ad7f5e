# Guide d'Installation des Deep Links pour Callitris

Ce guide vous explique comment installer et configurer les deep links pour l'application Callitris.

## 📋 Prérequis

- Flutter SDK installé
- Application Callitris existante
- Accès au serveur backend (`https://dev-mani.io/teams/api.callitris-distribution.com`)
- Comptes développeur Wave et CinetPay configurés

## 🚀 Installation

### 1. Configuration Android

1. **Modifier `android/app/src/main/AndroidManifest.xml`:**
   ```bash
   # Copier le contenu de deep_links_config/android/android_manifest_config.xml
   # et l'intégrer dans votre AndroidManifest.xml existant
   ```

2. **Vérifier les permissions:**
   ```xml
   <uses-permission android:name="android.permission.INTERNET" />
   <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
   ```

### 2. Configuration iOS

1. **Modifier `ios/Runner/Info.plist`:**
   ```bash
   # Copier le contenu de deep_links_config/ios/info_plist_config.xml
   # et l'intégrer dans votre Info.plist existant
   ```

2. **Vérifier les permissions dans Info.plist:**
   - CFBundleURLTypes configuré
   - Associated Domains ajoutés
   - NSAppTransportSecurity configuré

### 3. Configuration Flutter

1. **Vérifier les dépendances dans `pubspec.yaml`:**
   ```yaml
   dependencies:
     app_links: ^6.4.0  # Déjà présent
     shared_preferences: ^2.2.2  # Déjà présent
   ```

2. **Copier les services Flutter:**
   ```bash
   # Copier les fichiers suivants dans votre projet:
   cp deep_links_config/flutter/deep_link_service.dart lib/services/
   cp lib/services/deep_link_manager.dart lib/services/
   cp lib/services/payment_deep_link_integration.dart lib/services/
   ```

3. **Modifier `lib/main.dart`:**
   ```dart
   // Ajouter l'import
   import 'services/deep_link_manager.dart';
   
   // Dans _MainAppState, remplacer l'initialisation des deep links
   final DeepLinkManager _deepLinkManager = DeepLinkManager();
   
   @override
   void initState() {
     super.initState();
     WidgetsBinding.instance.addPostFrameCallback((_) {
       _deepLinkManager.initialize(context);
     });
   }
   ```

### 4. Configuration Backend

1. **Déployer les fichiers PHP:**
   ```bash
   # Copier les fichiers sur votre serveur:
   scp deep_links_config/backend/*.php user@server:/path/to/api.callitris-distribution.com/deep_links_config/backend/
   ```

2. **Créer le dossier logs:**
   ```bash
   mkdir -p /path/to/api.callitris-distribution.com/deep_links_config/backend/logs
   chmod 755 /path/to/api.callitris-distribution.com/deep_links_config/backend/logs
   ```

3. **Configurer les clés API:**
   
   **Dans `wave_callback.php`:**
   ```php
   $config = [
       'wave_api_key' => 'votre_cle_api_wave',
       'wave_secret' => 'votre_secret_wave',
       // ...
   ];
   ```
   
   **Dans `cinetpay_callback.php`:**
   ```php
   $config = [
       'cinetpay_api_key' => 'votre_cle_api_cinetpay',
       'cinetpay_site_id' => 'votre_site_id_cinetpay',
       'cinetpay_secret' => 'votre_secret_cinetpay',
       // ...
   ];
   ```

### 5. Configuration des Plateformes de Paiement

#### Wave Money
1. Connectez-vous à votre dashboard Wave
2. Configurez les URLs de callback:
   - **Notification URL:** `https://api.callitris-distribution.com/deep_links_config/backend/wave_callback.php`
   - **Success URL:** `https://api.callitris-distribution.com/deep_links_config/backend/wave_callback.php?status=success`
   - **Failure URL:** `https://api.callitris-distribution.com/deep_links_config/backend/wave_callback.php?status=failure`

#### CinetPay
1. Connectez-vous à votre dashboard CinetPay
2. Configurez les URLs de callback:
   - **Notification URL:** `https://api.callitris-distribution.com/deep_links_config/backend/cinetpay_callback.php`
   - **Return URL:** `https://api.callitris-distribution.com/deep_links_config/backend/cinetpay_callback.php?status=success`
   - **Cancel URL:** `https://api.callitris-distribution.com/deep_links_config/backend/cinetpay_callback.php?status=failure`

## 🔧 Intégration dans le Code Existant

### Modifier les Pages de Paiement

1. **Importer les services:**
   ```dart
   import '../services/payment_deep_link_integration.dart';
   ```

2. **Modifier les méthodes de paiement:**
   ```dart
   // Pour CinetPay
   requestBody = PaymentDeepLinkIntegration.enhanceCinetPayPaymentData(
     requestBody,
     commandId: commandId,
     clientId: clientId,
   );
   
   // Pour Wave
   requestBody = PaymentDeepLinkIntegration.enhanceWavePaymentData(
     requestBody,
     commandId: commandId,
     clientId: clientId,
   );
   ```

3. **Améliorer les URLs de paiement:**
   ```dart
   final enhancedUrl = PaymentDeepLinkIntegration.enhancePaymentUrl(
     paymentUrl,
     commandId: commandId,
     clientId: clientId,
     transactionId: transactionId,
   );
   ```

## ✅ Vérification de l'Installation

### 1. Vérifier la Configuration Android
```bash
# Vérifier que les intent-filters sont correctement configurés
adb shell dumpsys package com.callitris.pro | grep -A 10 "intent-filter"
```

### 2. Vérifier la Configuration iOS
```bash
# Vérifier que les URL schemes sont configurés
plutil -p ios/Runner/Info.plist | grep -A 5 CFBundleURLTypes
```

### 3. Vérifier les Fichiers Backend
```bash
# Tester l'accessibilité des fichiers PHP
curl -I https://api.callitris-distribution.com/deep_links_config/backend/payment_redirect.php
```

### 4. Vérifier les Services Flutter
```dart
// Ajouter ce code temporaire pour tester
final deepLinkManager = DeepLinkManager();
await deepLinkManager.initialize(context);
print('Deep Link Manager initialisé avec succès');
```

## 🐛 Dépannage

### Problèmes Courants

1. **Deep links ne fonctionnent pas sur Android:**
   - Vérifier que `android:autoVerify="true"` est présent
   - Vérifier les intent-filters dans AndroidManifest.xml
   - Tester avec `adb shell am start -W -a android.intent.action.VIEW -d "callitris://payment/success?transaction_id=test"`

2. **Deep links ne fonctionnent pas sur iOS:**
   - Vérifier CFBundleURLTypes dans Info.plist
   - Vérifier les Associated Domains
   - Tester avec le simulateur: `xcrun simctl openurl booted "callitris://payment/success?transaction_id=test"`

3. **Callbacks PHP ne fonctionnent pas:**
   - Vérifier les permissions des fichiers (755)
   - Vérifier les logs dans `/backend/logs/`
   - Vérifier la configuration des clés API

4. **L'app ne se lance pas après paiement:**
   - Vérifier que l'app est installée sur l'appareil
   - Vérifier les URLs de callback dans les dashboards des providers
   - Vérifier les logs du serveur

### Logs et Debugging

1. **Logs Flutter:**
   ```dart
   // Activer les logs détaillés
   debugPrint('Deep link reçu: $uri');
   ```

2. **Logs PHP:**
   ```bash
   # Consulter les logs
   tail -f /path/to/backend/logs/wave_callback.log
   tail -f /path/to/backend/logs/cinetpay_callback.log
   ```

3. **Logs Android:**
   ```bash
   adb logcat | grep -i "callitris\|deeplink"
   ```

4. **Logs iOS:**
   ```bash
   # Dans Xcode, consulter la console de debug
   ```

## 📞 Support

Si vous rencontrez des problèmes:

1. Vérifiez d'abord la section Dépannage
2. Consultez les logs pour identifier l'erreur
3. Testez avec les URLs de test fournies
4. Contactez l'équipe de développement avec les logs d'erreur

## 🔄 Mise à Jour

Pour mettre à jour les deep links:

1. Sauvegardez vos configurations actuelles
2. Téléchargez la nouvelle version des fichiers
3. Mettez à jour les configurations selon ce guide
4. Testez sur les environnements de développement avant la production
