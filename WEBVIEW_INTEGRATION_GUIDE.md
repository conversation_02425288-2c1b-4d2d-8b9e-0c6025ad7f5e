# Guide d'Intégration WebView pour CinetPay

## 🎯 Objectif

Intégrer une WebView dans l'application Callitris pour les paiements CinetPay, permettant aux utilisateurs de rester dans l'application tout en gardant la configuration des deep links.

## 🚀 Fonctionnalités Implémentées

### 1. WebView Intégrée pour CinetPay
- ✅ WebView dédiée pour les paiements CinetPay
- ✅ Bouton d'annulation et de retour
- ✅ Gestion des timeouts (10 minutes)
- ✅ Gestion des erreurs de chargement
- ✅ Interface utilisateur intuitive

### 2. Configuration Flexible
- ✅ Option pour activer/désactiver la WebView
- ✅ Option pour fermeture automatique après paiement
- ✅ Conservation de la configuration des deep links
- ✅ Fallback vers navigateur externe si nécessaire

### 3. Système de Vérification Conservé
- ✅ Même système de vérification que précédemment
- ✅ Deep links toujours fonctionnels
- ✅ Callbacks backend inchangés
- ✅ Intégration transparente

## 📁 Fichiers Créés/Modifiés

### Nouveaux Fichiers
```
lib/widgets/payment_webview.dart              # Widget WebView principal
lib/services/webview_payment_service.dart    # Service de gestion WebView
lib/services/payment_config_service.dart     # Service de configuration
lib/widgets/payment_settings_widget.dart     # Widget de paramètres
lib/pages/settings/payment_settings_page.dart # Page de paramètres
lib/pages/test/webview_test_page.dart        # Page de test
```

### Fichiers Modifiés
```
lib/pages/clients/payment_method_page.dart         # Intégration WebView
lib/pages/clients/boutique_payment_method_page.dart # Intégration WebView
pubspec.yaml                                       # Ajout webview_flutter
```

## 🔧 Configuration

### 1. Paramètres Disponibles

Les utilisateurs peuvent configurer :

- **WebView CinetPay** : Utiliser WebView intégrée ou navigateur externe
- **Fermeture Automatique** : Fermer la WebView après succès/échec
- **Deep Links** : Activer/désactiver les deep links

### 2. Valeurs par Défaut

```dart
useWebView: true          // Utiliser WebView par défaut
autoClose: true           // Fermer automatiquement
deepLinks: true           // Deep links activés
```

## 🎮 Utilisation

### 1. Paiement Normal

Quand un utilisateur initie un paiement CinetPay :

1. Le système vérifie la configuration
2. Si WebView activée → Ouvre dans l'application
3. Si WebView désactivée → Ouvre dans le navigateur externe
4. Les deep links fonctionnent dans les deux cas

### 2. Interface WebView

- **Barre d'outils** : Titre + boutons Actualiser/Annuler
- **Indicateur de chargement** : Pendant le chargement
- **Gestion d'erreurs** : Bouton réessayer si erreur
- **Barre inférieure** : ID transaction + bouton annuler
- **Timeout** : Fermeture automatique après 10 minutes

### 3. Gestion des Résultats

La WebView intercepte automatiquement :
- Les deep links `callitris://payment/*`
- Les URLs de callback backend
- Les redirections de succès/échec

## 🧪 Tests

### 1. Page de Test Intégrée

Accédez à `WebViewTestPage` pour :
- Tester la WebView avec des URLs personnalisées
- Vérifier la configuration actuelle
- Tester les deep links
- Modifier les paramètres

### 2. Tests Manuels

```dart
// Test WebView
await WebViewPaymentService.launchCinetPayWebView(
  context: context,
  paymentUrl: 'https://checkout.cinetpay.com/test',
  transactionId: 'test_123',
  autoCloseOnCompletion: true,
);
```

### 3. Tests Deep Links

```bash
# Android
adb shell am start -W -a android.intent.action.VIEW -d "callitris://payment/success?transaction_id=test123" com.callitris.pro

# iOS
xcrun simctl openurl booted "callitris://payment/success?transaction_id=test123"
```

## 🔄 Flux de Paiement

### Avec WebView (Nouveau)
```
Utilisateur → Paiement CinetPay → WebView Intégrée → Résultat → Fermeture Auto/Manuelle
```

### Avec Navigateur (Ancien)
```
Utilisateur → Paiement CinetPay → Navigateur Externe → Deep Link → Retour App
```

## ⚙️ Configuration Backend

Les callbacks backend restent inchangés :
- `cinetpay_callback.php` : Traite les notifications CinetPay
- `payment_redirect.php` : Gère les redirections
- Deep links générés automatiquement

## 🛠️ Maintenance

### Logs à Surveiller
```dart
AppLogger.info('WebView navigation: $url', 'PaymentWebView');
AppLogger.error('WebView error: $error', 'PaymentWebView');
```

### Métriques Importantes
- Taux d'utilisation WebView vs navigateur
- Taux de succès des paiements WebView
- Temps moyen de paiement
- Taux d'annulation

## 🔧 Personnalisation

### Modifier le Timeout
```dart
// Dans PaymentWebView
Timer(const Duration(minutes: 15), () { // Changer de 10 à 15 minutes
```

### Ajouter d'Autres Providers
```dart
// Dans _launchPaymentUrl
if (isWave || isMoov || isOrange) {
  await _launchProviderWebView(url, provider);
}
```

### Personnaliser l'Interface
Modifiez `PaymentWebView` pour :
- Changer les couleurs
- Ajouter des boutons
- Modifier les messages

## 🚨 Points d'Attention

1. **Dépendance** : `webview_flutter` ajoutée au projet
2. **Permissions** : WebView nécessite accès Internet
3. **Compatibilité** : Testé sur Android/iOS
4. **Fallback** : Navigateur externe si WebView échoue
5. **Deep Links** : Toujours fonctionnels même avec WebView

## 📱 Avantages

- ✅ Expérience utilisateur améliorée
- ✅ Pas de sortie de l'application
- ✅ Contrôle total sur l'interface
- ✅ Gestion des erreurs intégrée
- ✅ Configuration flexible
- ✅ Compatibilité avec l'existant

## 🔄 Migration

L'intégration est **rétrocompatible** :
- Anciens paiements continuent de fonctionner
- Deep links toujours actifs
- Configuration par défaut utilise WebView
- Possibilité de revenir au navigateur externe

## 🎉 Résultat

Les paiements CinetPay s'ouvrent maintenant dans une WebView intégrée avec :
- Bouton d'annulation
- Fermeture automatique configurable
- Conservation des deep links
- Interface cohérente avec l'application
