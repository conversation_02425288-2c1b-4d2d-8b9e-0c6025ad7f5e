import 'dart:async';
import 'dart:convert';

import 'package:callitris/pages/auth_provider.dart';
import 'package:callitris/pages/menu.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;
import 'package:provider/provider.dart';

class _DateInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    final text = newValue.text;

    final digitsOnly = text.replaceAll(RegExp(r'[^0-9]'), '');

    final limitedDigits =
        digitsOnly.length > 8 ? digitsOnly.substring(0, 8) : digitsOnly;

    String formatted = '';
    for (int i = 0; i < limitedDigits.length; i++) {
      if (i == 2 || i == 4) {
        formatted += '/';
      }
      formatted += limitedDigits[i];
    }

    return TextEditingValue(
      text: formatted,
      selection: TextSelection.collapsed(offset: formatted.length),
    );
  }
}

class VersementPage extends StatefulWidget {
  const VersementPage({super.key});

  @override
  _VersementPageState createState() => _VersementPageState();
}

class _VersementPageState extends State<VersementPage> {
  List<PaymentInfo> _payments = [];
  List<PaymentInfo> _filteredPayments = [];
  final TextEditingController _searchController = TextEditingController();
  DateTime? _startDate = DateTime.now();
  DateTime? _endDate = DateTime.now();
  bool _isLoading = false;
  Timer? _debounce;

  // Nouvelles variables pour la sélection de client
  int? _selectedClientId;
  int? _tempSelectedClientId;
  String? _selectedClientName;
  List<Map<String, dynamic>> _clients = [];
  bool _isLoadingClients = false;

  @override
  void initState() {
    super.initState();
    _searchController.addListener(_onSearchChanged);
    _loadClients();
    fetchAllPaymentsAndApplyFilters();
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    _debounce?.cancel();
    super.dispose();
  }

  void _onSearchChanged() {
    if (_debounce?.isActive ?? false) _debounce!.cancel();

    _debounce = Timer(const Duration(milliseconds: 500), () {
      if (_searchController.text.isNotEmpty ||
          (_startDate != null && _endDate != null)) {
        fetchAllPaymentsAndApplyFilters();
      } else {
        setState(() {
          _payments = [];
          _filteredPayments = [];
        });
      }
    });
  }

  String _formatDateForDisplay(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  DateTime? _parseDateFromInput(String input) {
    try {
      final parts = input.split('/');
      if (parts.length == 3) {
        final day = int.parse(parts[0]);
        final month = int.parse(parts[1]);
        final year = int.parse(parts[2]);
        return DateTime(year, month, day);
      }
    } catch (e) {
      print('Erreur de parsing de la date: $e');
    }
    return null;
  }

  void _clearDateFilters() {
    setState(() {
      _startDate = null;
      _endDate = null;
      _payments = [];
      _filteredPayments = [];
    });
  }

  Future<void> fetchPaymentsWithServerFilters() async {
    try {
      setState(() {
        _isLoading = true;
      });

      final provide = Provider.of<AuthProvider>(context, listen: false);
      final token = provide.token;
      final user = provide.user;

      String url = provide.getEndpoint(
          'products/getVersement.php?personnel_id=${user?['id_personnel']}');

      if (_searchController.text.isNotEmpty) {
        url += '&search=${Uri.encodeComponent(_searchController.text.trim())}';
      }

      if (_startDate != null && _endDate != null) {
        String start =
            '${_startDate!.year}-${_startDate!.month.toString().padLeft(2, '0')}-${_startDate!.day.toString().padLeft(2, '0')}';
        String end =
            '${_endDate!.year}-${_endDate!.month.toString().padLeft(2, '0')}-${_endDate!.day.toString().padLeft(2, '0')}';
        url += '&start_date=$start&end_date=$end';
      }

      final response = await http.get(
        Uri.parse(url),
        headers: {
          'Authorization': token!,
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> responseData = jsonDecode(response.body);
        List<PaymentInfo> payments =
            responseData.map((e) => PaymentInfo.fromMap(e)).toList();

        payments.sort((a, b) {
          String nameA = (a.client ?? '').toLowerCase();
          String nameB = (b.client ?? '').toLowerCase();
          return nameA.compareTo(nameB);
        });

        setState(() {
          _payments = payments;
          _filteredPayments = List.from(payments);
          _isLoading = false;
        });
      } else {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      print('❌ Erreur API: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> fetchPaymentsWithSearch(String searchTerm) async {
    try {
      setState(() {
        _isLoading = true;
      });

      final provide = Provider.of<AuthProvider>(context, listen: false);
      final token = provide.token;
      final user = provide.user;

      final response = await http.get(
        Uri.parse(provide.getEndpoint(
            'products/getVersement.php?personnel_id=${user?['id_personnel']}&search=$searchTerm')),
        headers: {'Authorization': token!, 'Content-Type': 'application/json'},
      );
      print(response.body);
      if (response.statusCode == 200) {
        final List<dynamic> responseData = jsonDecode(response.body);
        List<PaymentInfo> payments = [];

        for (var paymentData in responseData) {
          PaymentInfo payment = PaymentInfo.fromMap(paymentData);
          payments.add(payment);
        }

        payments.sort((a, b) {
          String nameA = (a.client ?? '').toLowerCase();
          String nameB = (b.client ?? '').toLowerCase();
          return nameA.compareTo(nameB);
        });

        setState(() {
          _payments = payments;
          _filteredPayments = List.from(_payments);
          _isLoading = false;
        });
      } else {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (error) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> fetchPaymentsByDateRange(
      DateTime startDate, DateTime endDate) async {
    try {
      setState(() {
        _isLoading = true;
      });

      final provide = Provider.of<AuthProvider>(context, listen: false);
      final token = provide.token;
      final user = provide.user;

      final startDateStr =
          '${startDate.year}-${startDate.month.toString().padLeft(2, '0')}-${startDate.day.toString().padLeft(2, '0')}';
      final endDateStr =
          '${endDate.year}-${endDate.month.toString().padLeft(2, '0')}-${endDate.day.toString().padLeft(2, '0')}';

      final response = await http.get(
        Uri.parse(provide.getEndpoint(
            'products/getVersement.php?personnel_id=${user?['id_personnel']}&start_date=$startDateStr&end_date=$endDateStr')),
        headers: {'Authorization': token!, 'Content-Type': 'application/json'},
      );
      print(response.body);
      if (response.statusCode == 200) {
        final List<dynamic> responseData = jsonDecode(response.body);
        List<PaymentInfo> payments = [];

        for (var paymentData in responseData) {
          PaymentInfo payment = PaymentInfo.fromMap(paymentData);
          payments.add(payment);
        }

        payments.sort((a, b) {
          String nameA = (a.client ?? '').toLowerCase();
          String nameB = (b.client ?? '').toLowerCase();
          return nameA.compareTo(nameB);
        });

        setState(() {
          _payments = payments;
          _filteredPayments = List.from(_payments);
          _isLoading = false;
        });
      } else {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (error) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> fetchAllPaymentsAndApplyFilters() async {
    try {
      setState(() {
        _isLoading = true;
      });

      final provide = Provider.of<AuthProvider>(context, listen: false);
      final token = provide.token;
      final user = provide.user;

      String url = provide.getEndpoint(
          'products/getVersement.php?personnel_id=${user?['id_personnel']}');

      // Ajouter le filtre client si sélectionné
      if (_selectedClientId != null) {
        url += '&client_id=$_selectedClientId';
      }

      if (_startDate != null && _endDate != null) {
        String start =
            '${_startDate!.year}-${_startDate!.month.toString().padLeft(2, '0')}-${_startDate!.day.toString().padLeft(2, '0')}';
        String end =
            '${_endDate!.year}-${_endDate!.month.toString().padLeft(2, '0')}-${_endDate!.day.toString().padLeft(2, '0')}';
        url += '&start_date=$start&end_date=$end';
      }

      final response = await http.get(
        Uri.parse(url),
        headers: {'Authorization': token!, 'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final List<dynamic> responseData = jsonDecode(response.body);
        List<PaymentInfo> payments = [];

        for (var paymentData in responseData) {
          PaymentInfo payment = PaymentInfo.fromMap(paymentData);
          payments.add(payment);
        }

        List<PaymentInfo> filteredPayments = payments.where((payment) {
          bool matchesSearch = true;
          bool matchesDate = true;
          bool matchesClient = true;

          // Filtre de recherche
          if (_searchController.text.isNotEmpty) {
            final searchValue = _searchController.text.toLowerCase().trim();
            final searchableText = [
              payment.amount.toString(),
              payment.date,
              payment.heure ?? '',
              payment.nomCompte.toLowerCase(),
              payment.pack?.toString() ?? '',
              payment.carnet?.toString() ?? '',
              payment.journalier?.toString() ?? '',
              payment.libelle?.toLowerCase() ?? '',
              payment.client?.toLowerCase() ?? '',
              payment.contact_client?.toString() ?? '',
            ].join(' ').toLowerCase();

            matchesSearch = searchableText.contains(searchValue);
          }

          // Filtre de date
          if (_startDate != null && _endDate != null) {
            try {
              final paymentDate = DateTime.parse(payment.date);
              matchesDate = paymentDate
                      .isAfter(_startDate!.subtract(const Duration(days: 1))) &&
                  paymentDate.isBefore(_endDate!.add(const Duration(days: 1)));
            } catch (e) {
              matchesDate = false;
            }
          }

          if (_selectedClientId != null) {
            matchesClient = payment.client
                    ?.toLowerCase()
                    .contains(_selectedClientName?.toLowerCase() ?? '') ??
                false;
          }

          return matchesSearch && matchesDate && matchesClient;
        }).toList();

        filteredPayments.sort((a, b) {
          String nameA = (a.client ?? '').toLowerCase();
          String nameB = (b.client ?? '').toLowerCase();
          return nameA.compareTo(nameB);
        });

        setState(() {
          _payments = filteredPayments;
          _filteredPayments = List.from(_payments);
          _isLoading = false;
        });
      } else {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (error) {
      print('Erreur: $error');
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> fetchAllPaymentsAndFilter() async {
    try {
      setState(() {
        _isLoading = true;
      });

      final provide = Provider.of<AuthProvider>(context, listen: false);
      final token = provide.token;
      final user = provide.user;

      final response = await http.get(
        Uri.parse(provide.getEndpoint(
            'products/getVersement.php?personnel_id=${user?['id_personnel']}')),
        headers: {'Authorization': token!, 'Content-Type': 'application/json'},
      );
      print(response.body);
      if (response.statusCode == 200) {
        final List<dynamic> responseData = jsonDecode(response.body);
        List<PaymentInfo> payments = [];

        for (var paymentData in responseData) {
          PaymentInfo payment = PaymentInfo.fromMap(paymentData);
          payments.add(payment);
        }

        List<PaymentInfo> filteredPayments = payments.where((payment) {
          if (_startDate != null && _endDate != null) {
            try {
              final paymentDate = DateTime.parse(payment.date);
              return paymentDate
                      .isAfter(_startDate!.subtract(const Duration(days: 1))) &&
                  paymentDate.isBefore(_endDate!.add(const Duration(days: 1)));
            } catch (e) {
              return false;
            }
          }
          return true;
        }).toList();

        filteredPayments.sort((a, b) {
          String nameA = (a.client ?? '').toLowerCase();
          String nameB = (b.client ?? '').toLowerCase();
          return nameA.compareTo(nameB);
        });

        setState(() {
          _payments = filteredPayments;
          _filteredPayments = List.from(_payments);
          _isLoading = false;
        });
      } else {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (error) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> fetchPayments() async {
    try {
      setState(() {
        _isLoading = true;
      });

      final provide = Provider.of<AuthProvider>(context, listen: false);
      final token = provide.token;
      final user = provide.user;

      final response = await http.get(
        Uri.parse(provide.getEndpoint(
            'products/getVersement.php?personnel_id=${user?['id_personnel']}')),
        headers: {'Authorization': token!, 'Content-Type': 'application/json'},
      );
      print(response.body);
      if (response.statusCode == 200) {
        final List<dynamic> responseData = jsonDecode(response.body);
        List<PaymentInfo> payments = [];

        for (var paymentData in responseData) {
          PaymentInfo payment = PaymentInfo.fromMap(paymentData);
          //print(payment);
          payments.add(payment);
        }

        payments.sort((a, b) {
          String nameA = (a.client ?? '').toLowerCase();
          String nameB = (b.client ?? '').toLowerCase();
          return nameA.compareTo(nameB);
        });

        setState(() {
          _payments = payments;
          _filteredPayments = List.from(_payments);
          _isLoading = false;
        });
      } else {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (error) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _loadClients() async {
    try {
      setState(() {
        _isLoadingClients = true;
      });

      final provide = Provider.of<AuthProvider>(context, listen: false);
      final token = provide.token;
      final user = provide.user;

      final response = await http.get(
        Uri.parse(provide.getEndpoint(
            'client/getAllPersClient.php?id_personnel=${user?['id_personnel']}')),
        headers: {'Authorization': token!, 'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final List<dynamic> responseData = jsonDecode(response.body);
        final List<Map<String, dynamic>> clients =
            List<Map<String, dynamic>>.from(responseData);

        // Trier par ordre alphabétique
        clients.sort((a, b) {
          final nameA =
              '${a['nom_client']} ${a['prenom_client'] ?? ''}'.toLowerCase();
          final nameB =
              '${b['nom_client']} ${b['prenom_client'] ?? ''}'.toLowerCase();
          return nameA.compareTo(nameB);
        });

        setState(() {
          _clients = clients;
          _isLoadingClients = false;
          print("resultat de la requete : $responseData".toString());
        });
      } else {
        print('Erreur lors du chargement des clients: ${response.body}');
        setState(() {
          _isLoadingClients = false;
        });
      }
    } catch (error) {
      print('Erreur lors du chargement des clients: $error');
      setState(() {
        _isLoadingClients = false;
      });
    }
  }

  void _showClientSelectionDialog() {
    final TextEditingController startDateController = TextEditingController();
    final TextEditingController endDateController = TextEditingController();
    final TextEditingController searchClientController =
        TextEditingController();

    int? tempSelectedClientId = _selectedClientId;
    String? tempSelectedClientName = _selectedClientName;
    List<Map<String, dynamic>> filteredClients = List.from(_clients);

    if (_startDate != null) {
      startDateController.text = _formatDateForDisplay(_startDate!);
    }
    if (_endDate != null) {
      endDateController.text = _formatDateForDisplay(_endDate!);
    }

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setDialogState) {
            return AlertDialog(
              title: Row(
                children: [
                  Icon(Icons.person, color: Colors.blue.shade600),
                  const SizedBox(width: 8),
                  const Text('Filtres', style: TextStyle(fontSize: 18)),
                ],
              ),
              content: SizedBox(
                width: double.maxFinite,
                height: 400,
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Sélectionner un client',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.grey.shade700,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade300),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: TextField(
                          controller: searchClientController,
                          decoration: InputDecoration(
                            hintText: 'Rechercher un client...',
                            hintStyle: TextStyle(color: Colors.grey[500]),
                            prefixIcon:
                                Icon(Icons.search, color: Colors.grey[500]),
                            suffixIcon: searchClientController.text.isNotEmpty
                                ? IconButton(
                                    icon: Icon(Icons.clear,
                                        color: Colors.grey[500]),
                                    onPressed: () {
                                      searchClientController.clear();
                                      setDialogState(() {
                                        filteredClients = List.from(_clients);
                                      });
                                    },
                                  )
                                : null,
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                              borderSide: BorderSide.none,
                            ),
                            filled: true,
                            fillColor: Colors.grey.shade50,
                            contentPadding: const EdgeInsets.symmetric(
                                horizontal: 16, vertical: 12),
                          ),
                          onChanged: (value) {
                            setDialogState(() {
                              if (value.isEmpty) {
                                filteredClients = List.from(_clients);
                              } else {
                                filteredClients = _clients.where((client) {
                                  final clientName =
                                      '${client['nom_client']} ${client['prenom_client'] ?? ''}'
                                          .toLowerCase();
                                  return clientName
                                      .contains(value.toLowerCase());
                                }).toList();

                                // Trier par ordre alphabétique
                                filteredClients.sort((a, b) {
                                  final nameA =
                                      '${a['nom_client']} ${a['prenom_client'] ?? ''}'
                                          .toLowerCase();
                                  final nameB =
                                      '${b['nom_client']} ${b['prenom_client'] ?? ''}'
                                          .toLowerCase();
                                  return nameA.compareTo(nameB);
                                });
                              }
                            });
                          },
                        ),
                      ),
                      const SizedBox(height: 8),
                      // Afficher la liste seulement si l'utilisateur a tapé quelque chose
                      if (searchClientController.text.isNotEmpty)
                        Container(
                          height: 220,
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey.shade300),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: _isLoadingClients
                              ? const Center(child: CircularProgressIndicator())
                              : filteredClients.isEmpty
                                  ? Center(
                                      child: Column(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          Icon(Icons.search_off,
                                              size: 40,
                                              color: Colors.grey[400]),
                                          const SizedBox(height: 8),
                                          Text(
                                            'Aucun client trouvé\npour "${searchClientController.text}"',
                                            style: TextStyle(
                                                color: Colors.grey.shade600,
                                                fontSize: 14),
                                            textAlign: TextAlign.center,
                                          ),
                                        ],
                                      ),
                                    )
                                  : ListView.builder(
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 4),
                                      itemCount: filteredClients.length,
                                      itemBuilder: (context, index) {
                                        final client = filteredClients[index];
                                        final clientName =
                                            '${client['nom_client']} ${client['prenom_client'] ?? ''}'
                                                .trim();
                                        final isSelected =
                                            tempSelectedClientName ==
                                                clientName;

                                        return Container(
                                          margin: const EdgeInsets.symmetric(
                                              horizontal: 6, vertical: 2),
                                          decoration: BoxDecoration(
                                            color: isSelected
                                                ? Colors.blue.shade50
                                                : null,
                                            borderRadius:
                                                BorderRadius.circular(6),
                                            border: isSelected
                                                ? Border.all(
                                                    color: Colors.blue.shade300)
                                                : null,
                                          ),
                                          child: ListTile(
                                            dense: true,
                                            contentPadding:
                                                const EdgeInsets.symmetric(
                                                    horizontal: 12,
                                                    vertical: 4),
                                            leading: CircleAvatar(
                                              radius: 18,
                                              backgroundColor: isSelected
                                                  ? Colors.blue.shade600
                                                  : Colors.grey.shade400,
                                              child: Text(
                                                clientName.isNotEmpty
                                                    ? clientName[0]
                                                        .toUpperCase()
                                                    : 'C',
                                                style: const TextStyle(
                                                  color: Colors.white,
                                                  fontSize: 14,
                                                  fontWeight: FontWeight.bold,
                                                ),
                                              ),
                                            ),
                                            title: Text(
                                              clientName.isNotEmpty
                                                  ? clientName
                                                  : 'Client sans nom',
                                              style: TextStyle(
                                                fontSize: 15,
                                                fontWeight: isSelected
                                                    ? FontWeight.w600
                                                    : FontWeight.normal,
                                                color: isSelected
                                                    ? Colors.blue.shade700
                                                    : Colors.black87,
                                              ),
                                            ),
                                            subtitle: client[
                                                        'contact_client'] !=
                                                    null
                                                ? Text(
                                                    client['contact_client'],
                                                    style: TextStyle(
                                                      fontSize: 12,
                                                      color:
                                                          Colors.grey.shade600,
                                                    ),
                                                  )
                                                : null,
                                            trailing: isSelected
                                                ? Icon(Icons.check_circle,
                                                    color: Colors.blue.shade600,
                                                    size: 20)
                                                : null,
                                            onTap: () {
                                              setDialogState(() {
                                                tempSelectedClientId =
                                                    client['id_client'];
                                                tempSelectedClientName =
                                                    clientName;
                                              });
                                            },
                                          ),
                                        );
                                      },
                                    ),
                        )
                      else
                        // Message d'instruction quand aucune recherche n'est effectuée
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.grey.shade50,
                            border: Border.all(color: Colors.grey.shade300),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(Icons.search,
                                  size: 20, color: Colors.grey.shade500),
                              const SizedBox(width: 8),
                              Flexible(
                                child: Text(
                                  'Tapez le nom d\'un client pour rechercher',
                                  style: TextStyle(
                                    color: Colors.grey.shade600,
                                    fontSize: 12,
                                    fontStyle: FontStyle.italic,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ],
                          ),
                        ),
                      const SizedBox(height: 16),
                      Text(
                        'Filtrer par période (optionnel)',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.grey.shade700,
                        ),
                      ),
                      const SizedBox(height: 10),
                      TextField(
                        controller: startDateController,
                        decoration: InputDecoration(
                          labelText: "Date de début",
                          hintText: "JJ/MM/AAAA",
                          prefixIcon: GestureDetector(
                            onTap: () async {
                              final DateTime? picked = await showDatePicker(
                                context: context,
                                initialDate: _startDate ?? DateTime.now(),
                                firstDate: DateTime(2020),
                                lastDate: DateTime.now()
                                    .add(const Duration(days: 365)),
                                locale: const Locale('fr', 'FR'),
                              );
                              if (picked != null) {
                                startDateController.text =
                                    _formatDateForDisplay(picked);
                              }
                            },
                            child: const Icon(Icons.calendar_today, size: 20),
                          ),
                          border: const OutlineInputBorder(),
                          contentPadding: const EdgeInsets.symmetric(
                              horizontal: 12, vertical: 8),
                        ),
                        keyboardType: TextInputType.number,
                        inputFormatters: [_DateInputFormatter()],
                        style: const TextStyle(fontSize: 14),
                      ),
                      const SizedBox(height: 10),
                      TextField(
                        controller: endDateController,
                        decoration: InputDecoration(
                          labelText: "Date de fin",
                          hintText: "JJ/MM/AAAA",
                          prefixIcon: GestureDetector(
                            onTap: () async {
                              final DateTime? picked = await showDatePicker(
                                context: context,
                                initialDate: _endDate ?? DateTime.now(),
                                firstDate: DateTime(2020),
                                lastDate: DateTime.now()
                                    .add(const Duration(days: 365)),
                                locale: const Locale('fr', 'FR'),
                              );
                              if (picked != null) {
                                endDateController.text =
                                    _formatDateForDisplay(picked);
                              }
                            },
                            child: const Icon(Icons.calendar_today, size: 20),
                          ),
                          border: const OutlineInputBorder(),
                          contentPadding: const EdgeInsets.symmetric(
                              horizontal: 12, vertical: 8),
                        ),
                        keyboardType: TextInputType.number,
                        inputFormatters: [_DateInputFormatter()],
                        style: const TextStyle(fontSize: 14),
                      ),
                    ],
                  ),
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    setState(() {
                      _startDate = null;
                      _endDate = null;
                      _selectedClientId = null;
                      _selectedClientName = null;
                    });
                    Navigator.of(context).pop();
                    fetchAllPaymentsAndApplyFilters();
                  },
                  child: Text(
                    'TOUS LES VERSEMENTS',
                    style: TextStyle(color: Colors.red.shade600, fontSize: 12),
                  ),
                ),
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: Text(
                    'Annuler',
                    style: TextStyle(color: Colors.grey.shade600),
                  ),
                ),
                ElevatedButton(
                  onPressed: () {
                    DateTime? startDate;
                    DateTime? endDate;

                    if (startDateController.text.isNotEmpty) {
                      try {
                        final parts = startDateController.text.split('/');
                        if (parts.length == 3) {
                          startDate = DateTime(
                            int.parse(parts[2]),
                            int.parse(parts[1]),
                            int.parse(parts[0]),
                          );
                        }
                      } catch (e) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('Format de date de début invalide'),
                            backgroundColor: Colors.red,
                          ),
                        );
                        return;
                      }
                    }

                    if (endDateController.text.isNotEmpty) {
                      try {
                        final parts = endDateController.text.split('/');
                        if (parts.length == 3) {
                          endDate = DateTime(
                            int.parse(parts[2]),
                            int.parse(parts[1]),
                            int.parse(parts[0]),
                          );
                        }
                      } catch (e) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('Format de date de fin invalide'),
                            backgroundColor: Colors.red,
                          ),
                        );
                        return;
                      }
                    }

                    if (startDate != null &&
                        endDate != null &&
                        startDate.isAfter(endDate)) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text(
                              'La date de début doit être antérieure à la date de fin'),
                          backgroundColor: Colors.red,
                        ),
                      );
                      return;
                    }

                    setState(() {
                      _startDate = startDate;
                      _endDate = endDate;
                      _selectedClientId = tempSelectedClientId;
                      _selectedClientName = tempSelectedClientName;
                    });

                    Navigator.of(context).pop();
                    fetchAllPaymentsAndApplyFilters();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue.shade600,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('Appliquer'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  void _clearAllFilters() {
    setState(() {
      _startDate = null;
      _endDate = null;
      _selectedClientId = null;
      _selectedClientName = null;
      _searchController.clear();
      _payments = [];
      _filteredPayments = [];
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Suivi des Encaissements'),
        backgroundColor: const Color.fromARGB(255, 249, 221, 175),
        actions: buildAppBarActions(context),
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    labelText: 'Rechercher ...',
                    hintText: 'Nom, montant, date, compte, pack, carnet...',
                    prefixIcon: const Icon(Icons.search),
                    suffixIcon: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        if (_startDate != null && _endDate != null)
                          IconButton(
                            icon: const Icon(
                              Icons.close,
                              size: 30,
                              color: Colors.black54,
                            ),
                            onPressed: _clearDateFilters,
                            tooltip: 'Effacer les filtres de date',
                            padding: EdgeInsets.zero,
                            constraints: const BoxConstraints(
                              minWidth: 32,
                              minHeight: 32,
                            ),
                          ),
                        Container(
                          decoration: BoxDecoration(
                            color: _selectedClientId != null
                                ? const Color.fromARGB(255, 0, 0, 0)
                                    .withValues(alpha: 0.1)
                                : const Color.fromARGB(255, 253, 18, 18)
                                    .withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(10),
                            border: Border.all(
                              color: _selectedClientId != null
                                  ? Colors.grey
                                  : Colors.grey,
                              width: 2,
                            ),
                          ),
                          child: IconButton(
                            iconSize: 32,
                            icon: Icon(
                              Icons.person,
                              color: _selectedClientId != null
                                  ? const Color.fromARGB(255, 0, 0, 0)
                                  : const Color.fromARGB(255, 0, 0, 0),
                            ),
                            onPressed: _showClientSelectionDialog,
                            tooltip: _selectedClientName ??
                                'Cliquez pour sélectionner un client',
                          ),
                        ),
                      ],
                    ),
                    border: const OutlineInputBorder(),
                  ),
                ),
                if (_startDate != null && _endDate != null ||
                    _selectedClientId != null)
                  Padding(
                    padding: const EdgeInsets.only(top: 8.0),
                    child: Wrap(
                      spacing: 8.0,
                      children: [
                        if (_startDate != null && _endDate != null)
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 6,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.blue,
                              borderRadius: BorderRadius.circular(20),
                              border: Border.all(
                                color: const Color.fromARGB(255, 0, 0, 0),
                                width: 1,
                              ),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                const Icon(
                                  Icons.date_range,
                                  color: Colors.white,
                                  size: 16,
                                ),
                                const SizedBox(width: 6),
                                Text(
                                  '${_formatDateForDisplay(_startDate!)} - ${_formatDateForDisplay(_endDate!)}',
                                  style: const TextStyle(
                                    fontSize: 12,
                                    color: Colors.white,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                const SizedBox(width: 8),
                                GestureDetector(
                                  onTap: _clearDateFilters,
                                  child: const Icon(
                                    Icons.close,
                                    color: Colors.white,
                                    size: 16,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        if (_selectedClientId != null)
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 6,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.green,
                              borderRadius: BorderRadius.circular(20),
                              border: Border.all(
                                color: const Color.fromARGB(255, 0, 0, 0),
                                width: 1,
                              ),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                const Icon(
                                  Icons.person,
                                  color: Colors.white,
                                  size: 16,
                                ),
                                const SizedBox(width: 6),
                                Text(
                                  _selectedClientName!,
                                  style: const TextStyle(
                                    fontSize: 12,
                                    color: Colors.white,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                const SizedBox(width: 8),
                                GestureDetector(
                                  onTap: () {
                                    setState(() {
                                      _selectedClientId = null;
                                      _selectedClientName = null;
                                    });
                                    fetchAllPaymentsAndApplyFilters();
                                  },
                                  child: const Icon(
                                    Icons.close,
                                    color: Colors.white,
                                    size: 16,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        if ((_startDate != null && _endDate != null) ||
                            _selectedClientId != null)
                          GestureDetector(
                            onTap: _clearAllFilters,
                            child: Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 6,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.red,
                                borderRadius: BorderRadius.circular(20),
                              ),
                              child: const Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    Icons.clear_all,
                                    color: Colors.white,
                                    size: 16,
                                  ),
                                  SizedBox(width: 6),
                                  Text(
                                    'Effacer tout',
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Colors.white,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
              ],
            ),
          ),
          Expanded(
            child: buildPaymentList(_filteredPayments),
          ),
        ],
      ),
    );
  }

  Widget buildPaymentList(List<PaymentInfo> payments) {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (payments.isEmpty &&
        _searchController.text.isEmpty &&
        _startDate == null &&
        _endDate == null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search,
              size: 80,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 20),
            Text(
              'Recherchez vos encaissements',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 10),
            Text(
              'Utilisez la barre de recherche ou\nsélectionnez une période',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[500],
              ),
            ),
          ],
        ),
      );
    }

    if (payments.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.inbox_outlined,
              size: 80,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 20),
            Text(
              'Aucun résultat trouvé',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 10),
            Text(
              'Essayez avec d\'autres critères de recherche',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[500],
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: payments.length,
      itemBuilder: (context, index) {
        return buildPaymentCard(payments[index]);
      },
    );
  }

  Widget buildPaymentCard(PaymentInfo payment) {
    Color accountColor = payment.nomCompte == 'BOUTIQUE'
        ? Colors.orange
        : payment.nomCompte == 'TONTINE'
            ? Colors.purple
            : Colors.blue;

    String formattedAmount = payment.amount.toStringAsFixed(0).replaceAllMapped(
        RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'), (Match m) => '${m[1]} ');

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 6.0, horizontal: 16.0),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16.0),
        color: Colors.white,
        border: Border.all(color: accountColor.withOpacity(0.3), width: 1),
        boxShadow: [
          BoxShadow(
            color: accountColor.withOpacity(0.1),
            spreadRadius: 0,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
              color: accountColor.withOpacity(0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16.0),
                topRight: Radius.circular(16.0),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: accountColor,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    payment.nomCompte == 'BOUTIQUE'
                        ? Icons.store
                        : payment.nomCompte == 'TONTINE'
                            ? Icons.group
                            : Icons.account_balance_wallet,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        payment.nomCompte,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: accountColor,
                        ),
                      ),
                      Text(
                        payment.client ?? 'Client non spécifié',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[700],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.green,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    '$formattedAmount F',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                if (payment.contact_client != null) ...[
                  Row(
                    children: [
                      Icon(Icons.phone, size: 16, color: Colors.grey[600]),
                      const SizedBox(width: 8),
                      Text(
                        payment.contact_client!,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[700],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                ],
                if (payment.nomCompte == 'BOUTIQUE') ...[
                  _buildInfoRow(
                      Icons.inventory_2, 'Pack', payment.pack ?? 'N/A'),
                  const SizedBox(height: 8),
                  _buildInfoRow(Icons.book, 'Carnet', payment.carnet ?? 'N/A'),
                  const SizedBox(height: 8),
                  _buildInfoRow(Icons.calendar_today, 'Journalier',
                      '${payment.journalier} F'),
                ],
                if (payment.nomCompte == 'TONTINE') ...[
                  _buildInfoRow(Icons.calendar_today, 'Journalier',
                      '${payment.journalier} F'),
                  const SizedBox(height: 8),
                  _buildInfoRow(
                      Icons.description, 'Libellé', payment.libelle ?? 'N/A'),
                ],
                const SizedBox(height: 12),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey[50],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.calendar_month,
                              size: 16, color: Colors.grey[600]),
                          const SizedBox(width: 6),
                          Text(
                            _formatDate(payment.date),
                            style: TextStyle(
                              fontSize: 13,
                              color: Colors.grey[700],
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                      Row(
                        children: [
                          Icon(Icons.access_time,
                              size: 16, color: Colors.grey[600]),
                          const SizedBox(width: 6),
                          Text(
                            payment.heure,
                            style: TextStyle(
                              fontSize: 13,
                              color: Colors.grey[700],
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Row(
      children: [
        Icon(icon, size: 16, color: Colors.grey[600]),
        const SizedBox(width: 8),
        Text(
          '$label: ',
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey[600],
            fontWeight: FontWeight.w500,
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              color: Colors.black87,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }

  String _formatDate(String dateStr) {
    try {
      DateTime date = DateTime.parse(dateStr);
      return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
    } catch (e) {
      return dateStr;
    }
  }
}

class PaymentInfo {
  final double amount;
  final String date;
  final String heure;
  final String nomCompte;
  final String? pack;
  final String? carnet;
  final String? journalier;
  final String? libelle;
  final String? client;
  final String? contact_client;

  PaymentInfo(
    this.amount,
    this.date,
    this.heure,
    this.nomCompte,
    this.pack,
    this.carnet,
    this.journalier,
    this.libelle,
    this.client,
    this.contact_client,
  );

  factory PaymentInfo.fromMap(Map<String, dynamic> map) {
    return PaymentInfo(
      double.parse(map['montant'].toString()),
      map['date'].toString(),
      map['heure'].toString(),
      map['compte'].toString(),
      map['pack']?.toString(),
      map['carnet']?.toString(),
      map['journalier']?.toString(),
      map['libelle']?.toString(),
      map['client']?.toString(),
      map['contact_client']?.toString(),
    );
  }
}
