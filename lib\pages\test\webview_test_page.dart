import 'package:flutter/material.dart';
import 'package:callitris/services/webview_payment_service.dart';
import 'package:callitris/services/payment_config_service.dart';
import 'package:callitris/widgets/payment_settings_widget.dart';

/// Page de test pour la WebView de paiement
class WebViewTestPage extends StatefulWidget {
  const WebViewTestPage({Key? key}) : super(key: key);

  @override
  State<WebViewTestPage> createState() => _WebViewTestPageState();
}

class _WebViewTestPageState extends State<WebViewTestPage> {
  final TextEditingController _urlController = TextEditingController();
  final TextEditingController _transactionController = TextEditingController();
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    // URL de test par défaut
    _urlController.text = 'https://checkout.cinetpay.com/test';
    _transactionController.text = 'test_${DateTime.now().millisecondsSinceEpoch}';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Test WebView Paiement'),
        backgroundColor: const Color.fromARGB(255, 249, 221, 175),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Configuration actuelle
            _buildCurrentConfig(),
            
            const SizedBox(height: 24),
            
            // Formulaire de test
            _buildTestForm(),
            
            const SizedBox(height: 24),
            
            // Boutons de test
            _buildTestButtons(),
            
            const SizedBox(height: 24),
            
            // Widget de paramètres
            const PaymentSettingsWidget(),
          ],
        ),
      ),
    );
  }

  Widget _buildCurrentConfig() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.settings,
                  color: Colors.blue,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Configuration Actuelle',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            FutureBuilder<Map<String, bool>>(
              future: PaymentConfigService.getAllSettings(),
              builder: (context, snapshot) {
                if (snapshot.hasData) {
                  final settings = snapshot.data!;
                  return Column(
                    children: [
                      _buildConfigRow('WebView CinetPay', settings['useWebView'] ?? false),
                      _buildConfigRow('Fermeture Auto', settings['autoClose'] ?? false),
                      _buildConfigRow('Deep Links', settings['deepLinks'] ?? false),
                    ],
                  );
                }
                return const CircularProgressIndicator();
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildConfigRow(String label, bool value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(
            value ? Icons.check_circle : Icons.cancel,
            color: value ? Colors.green : Colors.red,
            size: 16,
          ),
          const SizedBox(width: 8),
          Text(label),
          const Spacer(),
          Text(
            value ? 'Activé' : 'Désactivé',
            style: TextStyle(
              color: value ? Colors.green : Colors.red,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTestForm() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Paramètres de Test',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _urlController,
              decoration: const InputDecoration(
                labelText: 'URL de Paiement',
                hintText: 'https://checkout.cinetpay.com/...',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _transactionController,
              decoration: const InputDecoration(
                labelText: 'ID Transaction',
                hintText: 'test_123456',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTestButtons() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Tests Disponibles',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            // Test WebView
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isLoading ? null : _testWebView,
                icon: _isLoading 
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Icon(Icons.web),
                label: Text(_isLoading ? 'Test en cours...' : 'Tester WebView'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
            
            const SizedBox(height: 8),
            
            // Test Deep Link Success
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: () => _testDeepLink('success'),
                icon: const Icon(Icons.check_circle),
                label: const Text('Test Deep Link Succès'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: Colors.green,
                  side: BorderSide(color: Colors.green),
                ),
              ),
            ),
            
            const SizedBox(height: 8),
            
            // Test Deep Link Failure
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: () => _testDeepLink('failure'),
                icon: const Icon(Icons.error),
                label: const Text('Test Deep Link Échec'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: Colors.red,
                  side: BorderSide(color: Colors.red),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _testWebView() async {
    if (_urlController.text.isEmpty || _transactionController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Veuillez remplir tous les champs'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      await WebViewPaymentService.launchCinetPayWebView(
        context: context,
        paymentUrl: _urlController.text,
        transactionId: _transactionController.text,
        commandId: 'test_command',
        clientId: 'test_client',
        onPaymentCompleted: () {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Test WebView - Paiement simulé réussi'),
              backgroundColor: Colors.green,
            ),
          );
        },
        onPaymentFailed: (reason) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Test WebView - Échec: $reason'),
              backgroundColor: Colors.red,
            ),
          );
        },
        onPaymentCancelled: () {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Test WebView - Annulé'),
              backgroundColor: Colors.orange,
            ),
          );
        },
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Erreur test WebView: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _testDeepLink(String status) {
    final transactionId = _transactionController.text;
    final deepLinkUrl = 'callitris://payment/$status?transaction_id=$transactionId&command_id=test_cmd&client_id=test_client';
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Test Deep Link - ${status.toUpperCase()}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('URL générée :'),
            const SizedBox(height: 8),
            SelectableText(
              deepLinkUrl,
              style: const TextStyle(
                fontSize: 12,
                fontFamily: 'monospace',
                color: Colors.blue,
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'Pour tester sur Android :\n'
              'adb shell am start -W -a android.intent.action.VIEW -d "[URL]" com.callitris.pro\n\n'
              'Pour tester sur iOS :\n'
              'xcrun simctl openurl booted "[URL]"',
              style: TextStyle(fontSize: 12),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Fermer'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _urlController.dispose();
    _transactionController.dispose();
    super.dispose();
  }
}
