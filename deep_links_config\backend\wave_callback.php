<?php
/**
 * Callback handler pour Wave Money
 * Ce fichier gère les notifications de paiement de Wave et redirige vers l'app
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Gestion des requêtes OPTIONS pour CORS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Configuration
$config = [
    'wave_api_key' => 'your_wave_api_key', // À remplacer par votre clé API Wave
    'wave_secret' => 'your_wave_secret',   // À remplacer par votre secret Wave
    'app_scheme' => 'callitris',
    'log_file' => __DIR__ . '/logs/wave_callback.log'
];

// Fonction de logging
function logMessage($message, $logFile) {
    $timestamp = date('Y-m-d H:i:s');
    $logEntry = "[$timestamp] $message" . PHP_EOL;
    
    // Créer le dossier logs s'il n'existe pas
    $logDir = dirname($logFile);
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
}

// Fonction pour valider la signature Wave
function validateWaveSignature($payload, $signature, $secret) {
    $expectedSignature = hash_hmac('sha256', $payload, $secret);
    return hash_equals($expectedSignature, $signature);
}

// Fonction pour générer le deep link
function generateDeepLink($status, $params = []) {
    global $config;
    
    $baseUrl = $config['app_scheme'] . '://payment/' . $status;
    
    if (!empty($params)) {
        $queryString = http_build_query($params);
        $baseUrl .= '?' . $queryString;
    }
    
    return $baseUrl;
}

// Fonction pour envoyer une notification push (optionnel)
function sendPushNotification($transactionId, $status, $message) {
    // Ici vous pouvez intégrer votre service de notifications push
    // Par exemple Firebase Cloud Messaging
    logMessage("Push notification: Transaction $transactionId - Status: $status - Message: $message", $GLOBALS['config']['log_file']);
}

try {
    // Log de la requête entrante
    $method = $_SERVER['REQUEST_METHOD'];
    $rawInput = file_get_contents('php://input');
    
    logMessage("Wave callback received - Method: $method", $config['log_file']);
    logMessage("Raw input: $rawInput", $config['log_file']);
    
    // Traitement selon la méthode HTTP
    if ($method === 'POST') {
        // Callback de notification de Wave
        $headers = getallheaders();
        $signature = $headers['X-Wave-Signature'] ?? '';
        
        // Valider la signature si configurée
        if (!empty($config['wave_secret']) && !empty($signature)) {
            if (!validateWaveSignature($rawInput, $signature, $config['wave_secret'])) {
                logMessage("Invalid Wave signature", $config['log_file']);
                http_response_code(401);
                echo json_encode(['error' => 'Invalid signature']);
                exit();
            }
        }
        
        $data = json_decode($rawInput, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            logMessage("Invalid JSON in Wave callback", $config['log_file']);
            http_response_code(400);
            echo json_encode(['error' => 'Invalid JSON']);
            exit();
        }
        
        // Extraire les informations du paiement
        $transactionId = $data['id'] ?? $data['transaction_id'] ?? '';
        $status = $data['status'] ?? '';
        $amount = $data['amount'] ?? 0;
        $currency = $data['currency'] ?? 'XOF';
        $clientReference = $data['client_reference'] ?? '';
        $waveReference = $data['wave_reference'] ?? '';
        
        logMessage("Wave payment notification - Transaction: $transactionId, Status: $status, Amount: $amount", $config['log_file']);
        
        // Déterminer le statut pour le deep link
        $deepLinkStatus = '';
        $deepLinkParams = [
            'transaction_id' => $transactionId,
            'amount' => $amount,
            'currency' => $currency,
            'wave_reference' => $waveReference
        ];
        
        // Extraire les informations de commande et client depuis client_reference si disponible
        if (!empty($clientReference)) {
            $referenceData = json_decode($clientReference, true);
            if (is_array($referenceData)) {
                if (isset($referenceData['command_id'])) {
                    $deepLinkParams['command_id'] = $referenceData['command_id'];
                }
                if (isset($referenceData['client_id'])) {
                    $deepLinkParams['client_id'] = $referenceData['client_id'];
                }
            }
        }
        
        switch (strtolower($status)) {
            case 'successful':
            case 'success':
            case 'completed':
                $deepLinkStatus = 'success';
                sendPushNotification($transactionId, 'success', 'Paiement effectué avec succès');
                break;
                
            case 'failed':
            case 'failure':
            case 'cancelled':
                $deepLinkStatus = 'failure';
                $deepLinkParams['reason'] = $data['failure_reason'] ?? 'Paiement échoué';
                sendPushNotification($transactionId, 'failure', 'Paiement échoué');
                break;
                
            case 'pending':
            case 'processing':
                $deepLinkStatus = 'pending';
                sendPushNotification($transactionId, 'pending', 'Paiement en cours de traitement');
                break;
                
            default:
                logMessage("Unknown Wave payment status: $status", $config['log_file']);
                $deepLinkStatus = 'failure';
                $deepLinkParams['reason'] = 'Statut de paiement inconnu';
        }
        
        // Générer le deep link
        $deepLink = generateDeepLink($deepLinkStatus, $deepLinkParams);
        
        logMessage("Generated deep link: $deepLink", $config['log_file']);
        
        // Répondre à Wave
        http_response_code(200);
        echo json_encode([
            'status' => 'success',
            'message' => 'Callback processed',
            'deep_link' => $deepLink
        ]);
        
    } elseif ($method === 'GET') {
        // Redirection depuis Wave vers l'app
        $transactionId = $_GET['transaction_id'] ?? $_GET['id'] ?? '';
        $status = $_GET['status'] ?? '';
        $commandId = $_GET['command_id'] ?? '';
        $clientId = $_GET['client_id'] ?? '';
        
        logMessage("Wave redirect - Transaction: $transactionId, Status: $status", $config['log_file']);
        
        // Déterminer le statut pour le deep link
        $deepLinkStatus = '';
        $deepLinkParams = ['transaction_id' => $transactionId];
        
        if (!empty($commandId)) {
            $deepLinkParams['command_id'] = $commandId;
        }
        if (!empty($clientId)) {
            $deepLinkParams['client_id'] = $clientId;
        }
        
        switch (strtolower($status)) {
            case 'successful':
            case 'success':
            case 'completed':
                $deepLinkStatus = 'success';
                break;
                
            case 'failed':
            case 'failure':
            case 'cancelled':
                $deepLinkStatus = 'failure';
                $deepLinkParams['reason'] = $_GET['reason'] ?? 'Paiement échoué';
                break;
                
            case 'pending':
            case 'processing':
                $deepLinkStatus = 'pending';
                break;
                
            default:
                $deepLinkStatus = 'failure';
                $deepLinkParams['reason'] = 'Statut de paiement inconnu';
        }
        
        // Générer le deep link
        $deepLink = generateDeepLink($deepLinkStatus, $deepLinkParams);
        
        logMessage("Redirecting to deep link: $deepLink", $config['log_file']);
        
        // Redirection vers l'app
        header("Location: $deepLink");
        exit();
        
    } else {
        // Méthode non supportée
        http_response_code(405);
        echo json_encode(['error' => 'Method not allowed']);
    }
    
} catch (Exception $e) {
    logMessage("Error in Wave callback: " . $e->getMessage(), $config['log_file']);
    
    http_response_code(500);
    echo json_encode([
        'error' => 'Internal server error',
        'message' => $e->getMessage()
    ]);
}
?>
