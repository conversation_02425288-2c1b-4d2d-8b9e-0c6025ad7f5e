import 'package:flutter/material.dart';
import 'package:callitris/services/webview_payment_service.dart';
import 'package:callitris/services/app_logger.dart';

/// Page de test pour vérifier l'extraction et le lancement des URLs de callback
class CallbackUrlTestPage extends StatefulWidget {
  const CallbackUrlTestPage({Key? key}) : super(key: key);

  @override
  State<CallbackUrlTestPage> createState() => _CallbackUrlTestPageState();
}

class _CallbackUrlTestPageState extends State<CallbackUrlTestPage> {
  // URL d'exemple fournie par l'utilisateur
  final String _testUrl = 'https://checkout.cinetpay.com/payment/171cdaf31c51e17e8729c1a060fa06f2d6871888577b0fe2f5de8fd639be4dddfba87378d7474bf7b50b71d35f6fc41b1751d2a50dff8a?command_id=54855&client_id=576&transaction_id=unknown&success_url=https%3A%2F%2Fapi.callitris-distribution.com%2Fdeep_links_config%2Fbackend%2Fpayment_redirect.php%3Fprovider%3Dgeneric%26status%3Dsuccess%26command_id%3D54855%26client_id%3D576%26transaction_id%3Dunknown&failure_url=https%3A%2F%2Fapi.callitris-distribution.com%2Fdeep_links_config%2Fbackend%2Fpayment_redirect.php%3Fprovider%3Dgeneric%26status%3Dfailure%26command_id%3D54855%26client_id%3D576%26transaction_id%3Dunknown&pending_url=https%3A%2F%2Fapi.callitris-distribution.com%2Fdeep_links_config%2Fbackend%2Fpayment_redirect.php%3Fprovider%3Dgeneric%26status%3Dpending%26command_id%3D54855%26client_id%3D576%26transaction_id%3Dunknown';

  Map<String, String?> _extractedUrls = {};
  bool _isLoading = false;
  String _testResult = '';

  @override
  void initState() {
    super.initState();
    _extractUrlsFromTestUrl();
  }

  void _extractUrlsFromTestUrl() {
    try {
      final uri = Uri.parse(_testUrl);
      final successUrl = uri.queryParameters['success_url'];
      final failureUrl = uri.queryParameters['failure_url'];
      final pendingUrl = uri.queryParameters['pending_url'];

      setState(() {
        _extractedUrls = {
          'success_url': successUrl != null ? Uri.decodeComponent(successUrl) : null,
          'failure_url': failureUrl != null ? Uri.decodeComponent(failureUrl) : null,
          'pending_url': pendingUrl != null ? Uri.decodeComponent(pendingUrl) : null,
        };
      });
    } catch (e) {
      setState(() {
        _testResult = 'Erreur lors de l\'extraction: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Test URLs de Callback'),
        backgroundColor: const Color.fromARGB(255, 249, 221, 175),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildExplanationCard(),
            const SizedBox(height: 16),
            _buildUrlExtractionCard(),
            const SizedBox(height: 16),
            _buildTestButtonsCard(),
            const SizedBox(height: 16),
            _buildResultCard(),
          ],
        ),
      ),
    );
  }

  Widget _buildExplanationCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info_outline, color: Colors.blue, size: 20),
                const SizedBox(width: 8),
                Text(
                  'Test d\'Extraction des URLs',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            const Text(
              'Cette page teste l\'extraction et le lancement des URLs de callback depuis l\'URL de paiement CinetPay.\n\n'
              'Fonctionnement :\n'
              '• Extraction des success_url, failure_url, pending_url\n'
              '• Décodage des URLs encodées\n'
              '• Lancement dans le navigateur selon le résultat\n'
              '• Test des différents scénarios de paiement',
              style: TextStyle(fontSize: 14, height: 1.4),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUrlExtractionCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'URLs Extraites',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            
            _buildUrlRow('Success URL', _extractedUrls['success_url'], Colors.green),
            const SizedBox(height: 8),
            _buildUrlRow('Failure URL', _extractedUrls['failure_url'], Colors.red),
            const SizedBox(height: 8),
            _buildUrlRow('Pending URL', _extractedUrls['pending_url'], Colors.orange),
          ],
        ),
      ),
    );
  }

  Widget _buildUrlRow(String label, String? url, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.link, color: color, size: 16),
            const SizedBox(width: 8),
            Text(
              label,
              style: TextStyle(
                fontWeight: FontWeight.w600,
                color: color,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(4),
            border: Border.all(color: color.withValues(alpha: 0.3)),
          ),
          child: Text(
            url ?? 'Non disponible',
            style: const TextStyle(
              fontSize: 11,
              fontFamily: 'monospace',
            ),
            maxLines: 3,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Widget _buildTestButtonsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Tests de Scénarios',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            
            // Test WebView complète
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isLoading ? null : _testCompleteWebView,
                icon: _isLoading 
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Icon(Icons.web),
                label: const Text('Test WebView Complète'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
            
            const SizedBox(height: 8),
            
            // Test simulation succès
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: _testSuccessScenario,
                icon: const Icon(Icons.check_circle),
                label: const Text('Simuler Succès (URL Success)'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: Colors.green,
                ),
              ),
            ),
            
            const SizedBox(height: 8),
            
            // Test simulation échec
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: _testFailureScenario,
                icon: const Icon(Icons.error),
                label: const Text('Simuler Échec (URL Failure)'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: Colors.red,
                ),
              ),
            ),
            
            const SizedBox(height: 8),
            
            // Test simulation en attente
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: _testPendingScenario,
                icon: const Icon(Icons.hourglass_empty),
                label: const Text('Simuler En Attente (URL Pending)'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: Colors.orange,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildResultCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Résultats des Tests',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Container(
              width: double.infinity,
              height: 150,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: SingleChildScrollView(
                child: Text(
                  _testResult.isEmpty ? 'Aucun test effectué' : _testResult,
                  style: const TextStyle(
                    fontFamily: 'monospace',
                    fontSize: 11,
                  ),
                ),
              ),
            ),
            const SizedBox(height: 8),
            SizedBox(
              width: double.infinity,
              child: TextButton.icon(
                onPressed: () {
                  setState(() {
                    _testResult = '';
                  });
                },
                icon: const Icon(Icons.clear),
                label: const Text('Effacer'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _testCompleteWebView() async {
    setState(() {
      _isLoading = true;
      _testResult += '\n=== TEST WEBVIEW COMPLÈTE ===\n';
      _testResult += 'URL: ${_testUrl.substring(0, 100)}...\n';
      _testResult += 'Heure: ${DateTime.now()}\n\n';
    });

    try {
      await WebViewPaymentService.launchCinetPayWebView(
        context: context,
        paymentUrl: _testUrl,
        transactionId: 'test_${DateTime.now().millisecondsSinceEpoch}',
        commandId: '54855',
        clientId: '576',
        onPaymentCompleted: () {
          setState(() {
            _testResult += 'RÉSULTAT: ✅ Paiement complété\n';
            _testResult += 'ACTION: URL de succès lancée dans le navigateur\n';
          });
        },
        onPaymentFailed: (reason) {
          setState(() {
            _testResult += 'RÉSULTAT: ❌ Paiement échoué - $reason\n';
            _testResult += 'ACTION: URL d\'échec lancée dans le navigateur\n';
          });
        },
        onPaymentCancelled: () {
          setState(() {
            _testResult += 'RÉSULTAT: ⚠️ Paiement annulé\n';
            _testResult += 'ACTION: Aucune URL lancée\n';
          });
        },
      );
    } catch (e) {
      setState(() {
        _testResult += 'ERREUR: $e\n';
      });
    } finally {
      setState(() {
        _isLoading = false;
        _testResult += '\n=== TEST TERMINÉ ===\n\n';
      });
    }
  }

  void _testSuccessScenario() {
    setState(() {
      _testResult += '\n=== SIMULATION SUCCÈS ===\n';
      _testResult += 'URL qui serait lancée: ${_extractedUrls['success_url']}\n';
      _testResult += 'Status: success\n';
      _testResult += 'Command ID: 54855\n';
      _testResult += 'Client ID: 576\n\n';
    });
  }

  void _testFailureScenario() {
    setState(() {
      _testResult += '\n=== SIMULATION ÉCHEC ===\n';
      _testResult += 'URL qui serait lancée: ${_extractedUrls['failure_url']}\n';
      _testResult += 'Status: failure\n';
      _testResult += 'Command ID: 54855\n';
      _testResult += 'Client ID: 576\n\n';
    });
  }

  void _testPendingScenario() {
    setState(() {
      _testResult += '\n=== SIMULATION EN ATTENTE ===\n';
      _testResult += 'URL qui serait lancée: ${_extractedUrls['pending_url']}\n';
      _testResult += 'Status: pending\n';
      _testResult += 'Command ID: 54855\n';
      _testResult += 'Client ID: 576\n\n';
    });
  }
}
