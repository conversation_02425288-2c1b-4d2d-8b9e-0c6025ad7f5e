import 'package:flutter/material.dart';
import 'package:callitris/widgets/payment_webview.dart';
import 'package:callitris/services/app_logger.dart';
import 'package:callitris/services/payment_deep_link_integration.dart';
import 'package:callitris/services/payment_config_service.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Service pour gérer les paiements via WebView intégrée
class WebViewPaymentService {
  static final WebViewPaymentService _instance =
      WebViewPaymentService._internal();
  factory WebViewPaymentService() => _instance;
  WebViewPaymentService._internal();

  /// Lance un paiement CinetPay dans une WebView intégrée
  /// Retourne true si le paiement a réussi, false sinon
  static Future<bool> launchCinetPayWebView({
    required BuildContext context,
    required String paymentUrl,
    required String transactionId,
    String? commandId,
    String? clientId,
    bool? autoCloseOnCompletion,
    VoidCallback? onPaymentCompleted,
    Function(String)? onPaymentFailed,
    VoidCallback? onPaymentCancelled,
  }) async {
    try {
      // Récupérer les paramètres de configuration
      final shouldAutoClose = autoCloseOnCompletion ??
          await PaymentConfigService.shouldAutoCloseWebView();
      final deepLinksEnabled = await PaymentConfigService.areDeepLinksEnabled();

      // Améliorer l'URL de paiement avec les paramètres de deep link si activés
      String finalUrl = paymentUrl;
      if (deepLinksEnabled) {
        finalUrl = PaymentDeepLinkIntegration.enhancePaymentUrl(
          paymentUrl,
          commandId: commandId,
          clientId: clientId,
          transactionId: transactionId,
        );
      }

      AppLogger.info(
          'Lancement WebView CinetPay: $finalUrl', 'WebViewPaymentService');

      // Sauvegarder les informations de paiement
      await _savePaymentInfo(transactionId, commandId, clientId);

      // Vérifier que le context est encore valide
      if (!context.mounted) return false;

      // Naviguer vers la WebView
      final result = await Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => PaymentWebView(
            paymentUrl: finalUrl,
            transactionId: transactionId,
            commandId: commandId,
            clientId: clientId,
            autoCloseOnCompletion: shouldAutoClose,
            onPaymentCompleted: () {
              AppLogger.info('Paiement WebView complété avec succès',
                  'WebViewPaymentService');
              _handlePaymentSuccess(
                  context, transactionId, commandId, clientId);
              if (onPaymentCompleted != null) {
                onPaymentCompleted();
              }
            },
            onPaymentFailed: (reason) {
              AppLogger.warning(
                  'Paiement WebView échoué: $reason', 'WebViewPaymentService');
              _handlePaymentFailure(context, transactionId, reason);
              if (onPaymentFailed != null) {
                onPaymentFailed(reason);
              }
            },
            onPaymentCancelled: () {
              AppLogger.info(
                  'Paiement WebView annulé', 'WebViewPaymentService');
              _handlePaymentCancellation(context, transactionId);
              if (onPaymentCancelled != null) {
                onPaymentCancelled();
              }
            },
          ),
          fullscreenDialog: true,
        ),
      );

      AppLogger.info(
          'WebView fermée avec résultat: $result', 'WebViewPaymentService');

      // Retourner le résultat (true si succès, false sinon)
      return result == true;
    } catch (e) {
      AppLogger.error('Erreur lors du lancement de la WebView: $e',
          'WebViewPaymentService');

      // Fallback vers le navigateur externe si la WebView échoue
      if (onPaymentFailed != null) {
        onPaymentFailed('Erreur de chargement de la WebView');
      }

      // Retourner false en cas d'erreur
      return false;
    }
  }

  /// Sauvegarde les informations de paiement
  static Future<void> _savePaymentInfo(
      String transactionId, String? commandId, String? clientId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('current_payment_transaction_id', transactionId);
      if (commandId != null) {
        await prefs.setString('current_payment_command_id', commandId);
      }
      if (clientId != null) {
        await prefs.setString('current_payment_client_id', clientId);
      }
      await prefs.setInt(
          'payment_start_timestamp', DateTime.now().millisecondsSinceEpoch);
    } catch (e) {
      AppLogger.error('Erreur lors de la sauvegarde des infos de paiement: $e',
          'WebViewPaymentService');
    }
  }

  /// Gère le succès du paiement
  static void _handlePaymentSuccess(BuildContext context, String transactionId,
      String? commandId, String? clientId) {
    _showPaymentResultSnackBar(
      context,
      'Paiement réussi !',
      Colors.green,
      Icons.check_circle,
    );
    _clearPaymentInfo();
  }

  /// Gère l'échec du paiement
  static void _handlePaymentFailure(
      BuildContext context, String transactionId, String reason) {
    _showPaymentResultSnackBar(
      context,
      'Paiement échoué: $reason',
      Colors.red,
      Icons.error,
    );
    _clearPaymentInfo();
  }

  /// Gère l'annulation du paiement
  static void _handlePaymentCancellation(
      BuildContext context, String transactionId) {
    _showPaymentResultSnackBar(
      context,
      'Paiement annulé',
      Colors.orange,
      Icons.cancel,
    );
    _clearPaymentInfo();
  }

  /// Affiche un SnackBar avec le résultat du paiement
  static void _showPaymentResultSnackBar(
    BuildContext context,
    String message,
    Color color,
    IconData icon,
  ) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(icon, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: color,
        duration: const Duration(seconds: 4),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// Nettoie les informations de paiement sauvegardées
  static Future<void> _clearPaymentInfo() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('current_payment_transaction_id');
      await prefs.remove('current_payment_command_id');
      await prefs.remove('current_payment_client_id');
      await prefs.remove('payment_start_timestamp');
    } catch (e) {
      AppLogger.error('Erreur lors du nettoyage des infos de paiement: $e',
          'WebViewPaymentService');
    }
  }

  /// Récupère les informations de paiement en cours
  static Future<Map<String, String?>> getCurrentPaymentInfo() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return {
        'transaction_id': prefs.getString('current_payment_transaction_id'),
        'command_id': prefs.getString('current_payment_command_id'),
        'client_id': prefs.getString('current_payment_client_id'),
        'start_timestamp': prefs.getInt('payment_start_timestamp')?.toString(),
      };
    } catch (e) {
      AppLogger.error(
          'Erreur lors de la récupération des infos de paiement: $e',
          'WebViewPaymentService');
      return {};
    }
  }

  /// Vérifie si un paiement est en cours
  static Future<bool> hasActivePayment() async {
    final info = await getCurrentPaymentInfo();
    return info['transaction_id'] != null;
  }

  /// Configuration pour différents types de paiement WebView
  static Map<String, dynamic> getWebViewConfig({
    required String paymentMethod,
    bool autoCloseOnCompletion = true,
  }) {
    switch (paymentMethod.toLowerCase()) {
      case 'cinetpay':
        return {
          'autoCloseOnCompletion': autoCloseOnCompletion,
          'timeoutMinutes': 10,
          'allowedDomains': [
            'checkout.cinetpay.com',
            'api-checkout.cinetpay.com',
            'dev-mani.io',
            'api.callitris-distribution.com',
          ],
        };
      default:
        return {
          'autoCloseOnCompletion': autoCloseOnCompletion,
          'timeoutMinutes': 10,
          'allowedDomains': [],
        };
    }
  }
}
