import 'package:callitris/pages/auth_provider.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:callitris/pages/app_bar_navigation.dart';
import 'package:provider/provider.dart';
import 'package:flutter_svg/svg.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  HomePageState createState() => HomePageState();
}

class Dash {
  final String total_versement;
  final String total_encaissement;
  final String total_mobile_money;
  final String dernier_passage;
  final String derniere_somme;
  final String total_reliquat;
  final String total_retrait;
  final String total_boutique;
  final String total_courant;
  final String total_pret;

  Dash({
    required this.total_mobile_money,
    required this.total_versement,
    required this.total_encaissement,
    required this.dernier_passage,
    required this.derniere_somme,
    required this.total_reliquat,
    required this.total_retrait,
    required this.total_boutique,
    required this.total_courant,
    required this.total_pret,
  });
}

class HomePageState extends State<HomePage> {
  Dash? dash;
  bool isLoading = false;
  @override
  void initState() {
    super.initState();
    fetchDash();
  }

  Future<void> fetchDash() async {
    setState(() {
      isLoading = true;
    });
    try {
      final provide = Provider.of<AuthProvider>(context, listen: false);
      final user = Provider.of<AuthProvider>(context, listen: false).user;
      final token = Provider.of<AuthProvider>(context, listen: false).token;

      String idPersonnel = user!['id_personnel'].toString();

      final response = await http.get(
        Uri.parse(provide
            .getEndpoint('client/getDash.php?personnel_id=$idPersonnel')),
        headers: {'Authorization': token!, 'Content-Type': 'application/json'},
      );

      final List<dynamic> responsData = jsonDecode(response.body);

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = responsData[0];
        String totalVersement = responseData['total_versement'].toString();
        String dernierPassage = responseData['dernier_passage'].toString();
        String derniereSomme = responseData['derniere_somme'].toString();
        String totalReliquat = responseData['total_reliquat'].toString();
        String totalRetrait = responseData['total_retrait'].toString();
        String totalBoutique = responseData['total_boutique'].toString();
        String totalCourant = responseData['total_courant'].toString();
        String totalPret = responseData['total_pret'].toString();
        String totalEncaissement =
            responseData['total_encaissement'].toString();
        String totalMobileMoney =
            responseData['total_mobile_money'].toString();

        setState(() {
          dash = Dash(
            total_mobile_money: totalMobileMoney,
            total_encaissement: totalEncaissement,
            total_versement: totalVersement,
            dernier_passage: dernierPassage,
            derniere_somme: derniereSomme,
            total_reliquat: totalReliquat,
            total_retrait: totalRetrait,
            total_boutique: totalBoutique,
            total_courant: totalCourant,
            total_pret: totalPret,
          );
          isLoading = false;
        });
      } else {
        print('Erreur : ${response.statusCode}');
        setState(() {
          isLoading = false;
        });
      }
    } catch (error) {
      print('Erreur lors de la récupération des clients: $error');
      setState(() {
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final userDetail = Provider.of<AuthProvider>(context, listen: false).user;
    return MyAppBarNavigation(
      currentIndex: 0,
      onDestinationSelected: (int index) {
        switch (index) {
          case 0:
            break;
          case 1:
            Navigator.pushNamed(context, '/client');
            break;
          case 2:
            Navigator.pushNamed(context, '/services');
            break;
          case 3:
            Navigator.pushNamed(context, '/profile');
            break;
        }
      },
      body: Scaffold(
        backgroundColor: const Color(0xFFF8F9FA),
        body: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: double.infinity,
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Color(0xFF1976D2),
                      Color(0xFF42A5F5),
                    ],
                  ),
                  borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(20),
                    bottomRight: Radius.circular(20),
                  ),
                ),
                child: SafeArea(
                  child: Padding(
                    padding: const EdgeInsets.all(14),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(6),
                              decoration: BoxDecoration(
                                color: Colors.white.withOpacity(0.2),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: const Icon(
                                Icons.dashboard,
                                color: Colors.white,
                                size: 18,
                              ),
                            ),
                            const SizedBox(width: 10),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const Text(
                                    'Tableau de bord',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 12,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  Text(
                                    '${userDetail?['nom_pers']} ${userDetail?['prenom_pers']}',
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(10),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.1),
                                blurRadius: 6,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Row(
                            children: [
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const Text(
                                      'Versement Total',
                                      style: TextStyle(
                                        fontSize: 11,
                                        color: Colors.grey,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                    const SizedBox(height: 2),
                                    isLoading
                                        ? CircularProgressIndicator(
                                            strokeWidth: 1,
                                          )
                                        : Text(
                                            '${dash?.total_versement ?? '0'}CFA',
                                            style: const TextStyle(
                                              fontSize: 18,
                                              fontWeight: FontWeight.bold,
                                              color: Color(0xFF1976D2),
                                            ),
                                          ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 8),
                      ],
                    ),
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Mes Statistiques',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 20),
                    Row(
                      children: [
                        Expanded(
                          child: ModernCard(
                            color: const Color(0xFF4CAF50),
                            icon: Icons.attach_money,
                            title: 'Espece Boutique',
                            value: dash?.total_encaissement ?? "0 F",
                            onTap: () {},
                            isLoading: isLoading,
                          ),
                        ),
                        const SizedBox(width: 15),
                        Expanded(
                          child: ModernCard(
                            color: const Color(0xFF9C27B0),
                            icon: Icons.phone_android,
                            title: 'Mobile Money',
                            value: dash?.total_mobile_money ?? '0 F',
                            onTap: () {},
                            isLoading: isLoading,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 15),
                    Row(
                      children: [
                        Expanded(
                          child: ModernCard(
                            color: const Color(0xFFFF9800),
                            icon: Icons.store,
                            title: 'Total boutique',
                            value: dash?.total_boutique ?? '0 F',
                            onTap: () {},
                            isLoading: isLoading,
                          ),
                        ),
                        const SizedBox(width: 15),
                        Expanded(
                          child: ModernCard(
                            color: const Color(0xFF2196F3),
                            icon: Icons.group,
                            title: 'Tontine',
                            value: dash?.total_courant ?? '0 F',
                            onTap: () {},
                            isLoading: isLoading,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 15),
                    Row(
                      children: [
                        Expanded(
                          child: ModernCard(
                            color: const Color(0xFFF44336),
                            icon: Icons.account_balance_wallet_outlined,
                            title: 'Reliquat',
                            value: dash?.total_reliquat ?? '0 F',
                            onTap: () {},
                            isLoading: isLoading,
                          ),
                        ),
                        const SizedBox(width: 15),
                        Expanded(
                          child: ModernCard(
                            color: const Color(0xFF607D8B),
                            icon: Icons.arrow_upward,
                            title: 'Retrait',
                            value: dash?.total_retrait ?? '0 F',
                            onTap: () {},
                            isLoading: isLoading,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 15),
                    Row(
                      children: [
                        Expanded(
                          child: ModernCard(
                            color: const Color(0xFF795548),
                            icon: Icons.access_time,
                            title: 'Dernier Passage',
                            value: dash?.dernier_passage ?? 'AUCUN',
                            onTap: () {},
                            isLoading: isLoading,
                          ),
                        ),
                        const SizedBox(width: 15),
                        Expanded(
                          child: ModernCard(
                            color: const Color(0xFF009688),
                            icon: Icons.monetization_on,
                            title: 'Dernière Somme',
                            value: '${dash?.derniere_somme ?? '0'} F',
                            onTap: () {},
                            isLoading: isLoading,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class ModernCard extends StatelessWidget {
  final Color color;
  final IconData icon;
  final String title;
  final String value;
  final VoidCallback onTap;
  final bool isLoading;

  const ModernCard(
      {super.key,
      required this.color,
      required this.icon,
      required this.title,
      required this.value,
      required this.onTap,
      required this.isLoading});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 6,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: color.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    icon,
                    color: color,
                    size: 18,
                  ),
                ),
                const Spacer(),
                Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.grey.shade400,
                  size: 12,
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: TextStyle(
                fontSize: 11,
                color: Colors.grey.shade600,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 2),
            isLoading
                ? CircularProgressIndicator(
                    strokeWidth: 1,
                  )
                : Text(
                    value,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
          ],
        ),
      ),
    );
  }
}

class CustomCard extends StatelessWidget {
  final String svgPath;
  final String title;
  final String value;
  final Color color;
  final VoidCallback onTap;

  const CustomCard({
    super.key,
    required this.svgPath,
    required this.title,
    required this.value,
    required this.color,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 150,
        height: 150,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(15),
          boxShadow: [
            BoxShadow(
              color: Colors.grey,
              spreadRadius: 3,
              blurRadius: 7,
              offset: const Offset(0, 3),
            ),
          ],
          gradient: const LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [Colors.white, Colors.blue],
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SvgPicture.asset(
              svgPath,
              width: 40,
              height: 40,
              colorFilter: ColorFilter.mode(color, BlendMode.srcIn),
            ),
            const SizedBox(height: 10),
            Text(
              title,
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 5),
            Text(
              value,
              style: const TextStyle(fontSize: 18),
            ),
          ],
        ),
      ),
    );
  }
}
