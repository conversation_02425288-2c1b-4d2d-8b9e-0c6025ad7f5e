import 'package:flutter/material.dart';
import 'package:callitris/services/webview_payment_service.dart';
import 'package:callitris/services/app_logger.dart';

/// Page de test spécifique pour les échecs CinetPay
class CinetPayFailureTestPage extends StatefulWidget {
  const CinetPayFailureTestPage({Key? key}) : super(key: key);

  @override
  State<CinetPayFailureTestPage> createState() => _CinetPayFailureTestPageState();
}

class _CinetPayFailureTestPageState extends State<CinetPayFailureTestPage> {
  final List<String> _testUrls = [
    'https://checkout.cinetpay.com/payment/status/failed',
    'https://checkout.cinetpay.com/payment/status/success',
    'https://api.cinetpay.com/payment/failed',
    'https://api.cinetpay.com/payment/success',
    'https://checkout.cinetpay.com/payment/error',
    'https://checkout.cinetpay.com/payment/completed',
  ];

  String _selectedUrl = '';
  bool _isLoading = false;
  String _lastResult = '';

  @override
  void initState() {
    super.initState();
    _selectedUrl = _testUrls.first;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Test Échecs CinetPay'),
        backgroundColor: const Color.fromARGB(255, 249, 221, 175),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Explication
            _buildExplanationCard(),
            
            const SizedBox(height: 16),
            
            // Sélection d'URL
            _buildUrlSelector(),
            
            const SizedBox(height: 16),
            
            // Boutons de test
            _buildTestButtons(),
            
            const SizedBox(height: 16),
            
            // Résultats
            _buildResultsCard(),
          ],
        ),
      ),
    );
  }

  Widget _buildExplanationCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: Colors.blue,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Test de Détection d\'Échecs',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            const Text(
              'Cette page teste la détection automatique des URLs d\'échec CinetPay.\n\n'
              '• URLs d\'ÉCHEC → WebView se ferme et ouvre le navigateur\n'
              '• URLs de SUCCÈS → WebView se ferme normalement\n\n'
              'Patterns détectés :\n'
              '- checkout.cinetpay.com/payment/status/failed\n'
              '- cinetpay.com + "failed" ou "error"\n'
              '- cinetpay.com + "success" ou "completed"',
              style: TextStyle(fontSize: 14, height: 1.4),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUrlSelector() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'URL de Test',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            DropdownButtonFormField<String>(
              value: _selectedUrl,
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                labelText: 'Sélectionner une URL',
              ),
              items: _testUrls.map((url) {
                final isFailure = url.contains('failed') || url.contains('error');
                final isSuccess = url.contains('success') || url.contains('completed');
                
                return DropdownMenuItem(
                  value: url,
                  child: Row(
                    children: [
                      Icon(
                        isFailure ? Icons.error : (isSuccess ? Icons.check_circle : Icons.help),
                        color: isFailure ? Colors.red : (isSuccess ? Colors.green : Colors.grey),
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          url,
                          style: const TextStyle(fontSize: 12),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _selectedUrl = value;
                  });
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTestButtons() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Actions de Test',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isLoading ? null : _testWebView,
                icon: _isLoading 
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Icon(Icons.play_arrow),
                label: Text(_isLoading ? 'Test en cours...' : 'Tester WebView'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
            
            const SizedBox(height: 8),
            
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: _clearResults,
                icon: const Icon(Icons.clear),
                label: const Text('Effacer Résultats'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: Colors.grey,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildResultsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Résultats du Test',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: Text(
                _lastResult.isEmpty ? 'Aucun test effectué' : _lastResult,
                style: const TextStyle(
                  fontFamily: 'monospace',
                  fontSize: 12,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _testWebView() async {
    setState(() {
      _isLoading = true;
      _lastResult = 'Test démarré avec URL: $_selectedUrl\n';
    });

    try {
      final transactionId = 'test_${DateTime.now().millisecondsSinceEpoch}';
      
      await WebViewPaymentService.launchCinetPayWebView(
        context: context,
        paymentUrl: _selectedUrl,
        transactionId: transactionId,
        commandId: 'test_command',
        clientId: 'test_client',
        onPaymentCompleted: () {
          setState(() {
            _lastResult += 'RÉSULTAT: Paiement complété avec succès\n';
            _lastResult += 'COMPORTEMENT: WebView fermée normalement\n';
          });
          AppLogger.info('Test WebView - Succès détecté', 'CinetPayFailureTest');
        },
        onPaymentFailed: (reason) {
          setState(() {
            _lastResult += 'RÉSULTAT: Paiement échoué - $reason\n';
            if (reason.contains('Redirection vers navigateur')) {
              _lastResult += 'COMPORTEMENT: WebView fermée + Navigateur ouvert ✓\n';
            } else {
              _lastResult += 'COMPORTEMENT: WebView fermée normalement\n';
            }
          });
          AppLogger.info('Test WebView - Échec détecté: $reason', 'CinetPayFailureTest');
        },
        onPaymentCancelled: () {
          setState(() {
            _lastResult += 'RÉSULTAT: Paiement annulé par l\'utilisateur\n';
            _lastResult += 'COMPORTEMENT: WebView fermée par annulation\n';
          });
          AppLogger.info('Test WebView - Annulation détectée', 'CinetPayFailureTest');
        },
      );
    } catch (e) {
      setState(() {
        _lastResult += 'ERREUR: $e\n';
      });
      AppLogger.error('Erreur test WebView: $e', 'CinetPayFailureTest');
    } finally {
      setState(() {
        _isLoading = false;
        _lastResult += 'Test terminé à ${DateTime.now()}\n';
      });
    }
  }

  void _clearResults() {
    setState(() {
      _lastResult = '';
    });
  }
}
