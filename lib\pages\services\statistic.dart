import 'package:callitris/pages/menu.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'dart:convert';
import 'package:callitris/pages/auth_provider.dart';
import 'package:provider/provider.dart';
import 'package:http/http.dart' as http;
import 'package:syncfusion_flutter_charts/charts.dart';

class StatisticPage extends StatelessWidget {
  const StatisticPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: const Text(
          'Statistiques',
          style: TextStyle(
            color: Colors.black87,
            fontWeight: FontWeight.bold,
            fontSize: 20,
          ),
        ),
        iconTheme: const IconThemeData(color: Colors.black87),
        actions: buildAppBarActions(context),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [Color(0xFF667eea), Color(0xFF764ba2)],
                  ),
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFF667eea).withValues(alpha: 0.3),
                      blurRadius: 20,
                      offset: const Offset(0, 10),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.white.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Icon(
                            Icons.analytics_outlined,
                            color: Colors.white,
                            size: 24,
                          ),
                        ),
                        const SizedBox(width: 16),
                        const Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Vue d\'ensemble',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              Text(
                                'Suivez vos performances en temps réel',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 14,
                                  fontWeight: FontWeight.w300,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 30),
              const Text(
                'États Généraux',
                style: TextStyle(
                  fontSize: 22,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: ModernStatCard(
                      title: 'Clients',
                      value: 'à 80%',
                      icon: Icons.people_outline,
                      color: const Color(0xFF10B981),
                      onTap: () {
                        Navigator.pushNamed(context, '/client_as');
                      },
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ModernStatCard(
                      title: 'Carnets',
                      value: 'Actifs',
                      icon: Icons.book_outlined,
                      color: const Color(0xFF3B82F6),
                      onTap: () {
                        Navigator.pushNamed(context, '/carnet');
                      },
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 30),
              const Text(
                'Mes Performances',
                style: TextStyle(
                  fontSize: 22,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 16),
              const LineDefaultChart(),
            ],
          ),
        ),
      ),
    );
  }
}

class ModernStatCard extends StatelessWidget {
  final String title;
  final String value;
  final IconData icon;
  final Color color;
  final VoidCallback onTap;

  const ModernStatCard({
    super.key,
    required this.title,
    required this.value,
    required this.icon,
    required this.color,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 15,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(icon, color: color, size: 24),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.grey[400],
                  size: 16,
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              title,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              value,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class StatisticTab extends StatelessWidget {
  final String title;
  final String icon;
  final String value;
  final VoidCallback onTap;

  const StatisticTab({
    super.key,
    required this.title,
    required this.icon,
    required this.value,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        height: 120,
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(15),
          color: const Color.fromARGB(255, 252, 166, 38),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.5),
              spreadRadius: 1,
              blurRadius: 6,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SvgPicture.asset(icon, width: 40, height: 40, color: Colors.white),
            const SizedBox(height: 10),
            Text(
              title,
              style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 5),
            Text(
              value,
              style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
          ],
        ),
      ),
    );
  }
}

class OtherTab extends StatelessWidget {
  final String title;
  final IconData icon;
  final String value;
  final VoidCallback onTap;

  const OtherTab({
    super.key,
    required this.title,
    required this.icon,
    required this.value,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        width: 110,
        height: 120,
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(15),
          color: const Color.fromARGB(255, 238, 64, 122),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.5),
              spreadRadius: 1,
              blurRadius: 6,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Icon(icon, size: 40, color: Colors.white),
            const SizedBox(height: 10),
            Text(
              title,
              style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 5),
            Text(
              value,
              style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
          ],
        ),
      ),
    );
  }
}

class LineDefaultChart extends StatefulWidget {
  const LineDefaultChart({super.key});

  @override
  _LineDefaultState createState() => _LineDefaultState();
}

class Stat {
  final String client;
  final String commande;
  final String tontine;
  final String mission;

  Stat({
    required this.client,
    required this.commande,
    required this.tontine,
    required this.mission,
  });
}

class CourbeValeur {
  final String jour;
  final int boutique;
  final int tontine;

  CourbeValeur({
    required this.jour,
    required this.boutique,
    required this.tontine,
  });
}

class _LineDefaultState extends State<LineDefaultChart> {
  List<CourbeValeur>? courbeValeurs;
  bool _disposed = false;

  @override
  void initState() {
    super.initState();
    // Délai pour s'assurer que le widget est complètement monté
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted && !_disposed) {
        fetchCourbeValeur();
        fetchStats();
      }
    });
  }

  @override
  void dispose() {
    _disposed = true;
    super.dispose();
  }

  // Méthode utilitaire pour setState sécurisé
  void _safeSetState(VoidCallback fn) {
    if (mounted && !_disposed) {
      setState(fn);
    }
  }

  Stat? stats;

  Future<void> fetchStats() async {
    try {
      final provide = Provider.of<AuthProvider>(context, listen: false);
      final user = Provider.of<AuthProvider>(context, listen: false).user;
      final token = Provider.of<AuthProvider>(context, listen: false).token;

      String idPersonnel = user!['id_personnel'].toString();

      final response = await http.get(
        Uri.parse(
          provide.getEndpoint(
            'products/getStatCom.php?personnel_id=$idPersonnel',
          ),
        ),
        headers: {'Authorization': token!, 'Content-Type': 'application/json'},
      );
      //print(response.body);
      if (response.statusCode == 200) {
        final Map<String, dynamic> salaireData = jsonDecode(response.body);

        String client = salaireData['nouveaux_clients'].toString();
        String commande = salaireData['nouvelles_commandes'].toString();
        String tontine = salaireData['nouvelles_tontines'].toString();
        String mission = salaireData['nouvelles_missions'].toString();

        setState(() {
          stats = Stat(
            client: client,
            commande: commande,
            tontine: tontine,
            mission: mission,
          );
        });
      } else {
        print('Erreur : ${response.statusCode}');
      }
    } catch (error) {
      print('Erreur lors de la récupération des clients: $error');
    }
  }

  Future<void> fetchCourbeValeur() async {
    try {
      final provide = Provider.of<AuthProvider>(context, listen: false);
      final user = Provider.of<AuthProvider>(context, listen: false).user;
      final token = Provider.of<AuthProvider>(context, listen: false).token;

      String idPersonnel = user!['id_personnel'].toString();

      final response = await http.get(
        Uri.parse(
          provide.getEndpoint(
            'products/getStatistic.php?personnel_id=$idPersonnel',
          ),
        ),
        headers: {'Authorization': token!, 'Content-Type': 'application/json'},
      );

      print('Response status: ${response.statusCode}');
      print('Response body: ${response.body}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = jsonDecode(response.body);
        List<CourbeValeur> values = [];

        print('JSON Data: $jsonData');

        jsonData.forEach((key, value) {
          if (value is Map<String, dynamic>) {
            values.add(
              CourbeValeur(
                jour: key,
                boutique: (value['boutique'] ?? 0) as int,
                tontine: (value['tontine'] ?? 0) as int,
              ),
            );
          }
        });

        print('Parsed values: ${values.length} items');
        for (var val in values) {
          print(
            '${val.jour}: Boutique=${val.boutique}, Tontine=${val.tontine}',
          );
        }

        _safeSetState(() {
          courbeValeurs = values;
        });

        // Si pas de données, créer des données de test
        if (values.isEmpty) {
          _createTestData();
        }
      } else {
        print('Erreur HTTP : ${response.statusCode}');
        print('Response body: ${response.body}');
      }
    } catch (error) {
      print('Erreur lors de la récupération des valeurs de la courbe : $error');
    }
  }

  void _createTestData() {
    // Créer des données de test pour vérifier le graphique
    List<CourbeValeur> testData = [
      CourbeValeur(jour: 'Lun', boutique: 15000, tontine: 12000),
      CourbeValeur(jour: 'Mar', boutique: 18000, tontine: 14000),
      CourbeValeur(jour: 'Mer', boutique: 22000, tontine: 16000),
      CourbeValeur(jour: 'Jeu', boutique: 19000, tontine: 13000),
      CourbeValeur(jour: 'Ven', boutique: 25000, tontine: 18000),
      CourbeValeur(jour: 'Sam', boutique: 28000, tontine: 20000),
      CourbeValeur(jour: 'Dim', boutique: 16000, tontine: 11000),
    ];

    if (mounted && !_disposed) {
      setState(() {
        courbeValeurs = testData;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        GridView.count(
          crossAxisCount: 2,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          mainAxisSpacing: 16,
          crossAxisSpacing: 16,
          childAspectRatio: 1.0,
          children: [
            ModernPerformanceCard(
              title: 'Missions',
              value: '${stats?.mission ?? 0}',
              icon: Icons.assignment_outlined,
              color: const Color(0xFF8B5CF6),
              onTap: () {
                Navigator.pushNamed(context, '/mission');
              },
            ),
            ModernPerformanceCard(
              title: 'Nouv. Tontines',
              value: '${stats?.tontine ?? 0}',
              icon: Icons.account_balance_wallet_outlined,
              color: const Color(0xFFF59E0B),
              onTap: () {},
            ),
            ModernPerformanceCard(
              title: 'Nouv. Clients',
              value: '${stats?.client ?? 0}',
              icon: Icons.person_add_outlined,
              color: const Color(0xFF3B82F6),
              onTap: () {
                Navigator.pushNamed(context, '/new_client');
              },
            ),
            ModernPerformanceCard(
              title: 'Nouv. Commandes',
              value: '${stats?.commande ?? 0}',
              icon: Icons.shopping_cart_outlined,
              color: const Color(0xFF10B981),
              onTap: () {
                Navigator.pushNamed(context, '/new_commande');
              },
            ),
          ],
        ),
        const SizedBox(height: 30),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: const Color(0xFF667eea).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.show_chart,
                      color: Color(0xFF667eea),
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  const Text(
                    'Évolution des Versements',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),
              SizedBox(height: 300, child: _buildDefaultLineChart()),
            ],
          ),
        ),
      ],
    );
  }

  SfCartesianChart _buildDefaultLineChart() {
    // Vérifier si les données sont disponibles
    if (courbeValeurs == null || courbeValeurs!.isEmpty) {
      return SfCartesianChart(
        backgroundColor: Colors.transparent,
        plotAreaBorderWidth: 0,
        title: const ChartTitle(
          text: 'Chargement des données...',
          textStyle: TextStyle(
            fontSize: 16,
            color: Colors.grey,
            fontWeight: FontWeight.w500,
          ),
        ),
        primaryXAxis: const CategoryAxis(),
        primaryYAxis: const NumericAxis(),
      );
    }

    return SfCartesianChart(
      backgroundColor: Colors.transparent,
      plotAreaBorderWidth: 0,
      title: const ChartTitle(
        text: 'Versements par jour',
        textStyle: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
          color: Colors.black87,
        ),
      ),
      legend: const Legend(
        isVisible: true,
        overflowMode: LegendItemOverflowMode.wrap,
        position: LegendPosition.bottom,
        textStyle: TextStyle(fontSize: 12, color: Colors.black87),
      ),
      primaryXAxis: CategoryAxis(
        edgeLabelPlacement: EdgeLabelPlacement.shift,
        majorGridLines: const MajorGridLines(width: 0),
        axisLine: const AxisLine(width: 1, color: Colors.grey),
        labelStyle: const TextStyle(fontSize: 10, color: Colors.black54),
        title: const AxisTitle(
          text: 'Jours',
          textStyle: TextStyle(
            fontSize: 12,
            color: Colors.black87,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
      primaryYAxis: NumericAxis(
        labelFormat: '{value}',
        axisLine: const AxisLine(width: 1, color: Colors.grey),
        majorTickLines: const MajorTickLines(size: 4, color: Colors.grey),
        majorGridLines: const MajorGridLines(
          width: 0.5,
          color: Color(0xFFE5E7EB),
        ),
        labelStyle: const TextStyle(fontSize: 10, color: Colors.black54),
        title: const AxisTitle(
          text: 'Montants (FCFA)',
          textStyle: TextStyle(
            fontSize: 12,
            color: Colors.black87,
            fontWeight: FontWeight.w500,
          ),
        ),
        minimum: 0,
      ),
      series: _getDefaultBarSeries(),
      tooltipBehavior: TooltipBehavior(
        enable: true,
        color: Colors.black87,
        textStyle: const TextStyle(color: Colors.white, fontSize: 12),
        format: 'point.x\npoint.seriesName: point.y FCFA',
      ),
      zoomPanBehavior: ZoomPanBehavior(
        enablePinching: true,
        enablePanning: true,
        zoomMode: ZoomMode.x,
      ),
    );
  }

  List<ColumnSeries<CourbeValeur, String>> _getDefaultBarSeries() {
    if (courbeValeurs == null || courbeValeurs!.isEmpty) {
      return [];
    }

    return <ColumnSeries<CourbeValeur, String>>[
      ColumnSeries<CourbeValeur, String>(
        color: const Color(0xFFEF4444), // Rouge moderne
        dataSource: courbeValeurs!,
        xValueMapper: (CourbeValeur sales, _) => sales.jour,
        yValueMapper: (CourbeValeur sales, _) => sales.boutique,
        name: 'Boutique',
        width: 0.7, // Largeur augmentée
        spacing: 0.1, // Espacement réduit
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(6),
          topRight: Radius.circular(6),
        ),
        dataLabelSettings: const DataLabelSettings(
          isVisible: true,
          labelAlignment: ChartDataLabelAlignment.top,
          textStyle: TextStyle(fontSize: 10, color: Colors.black54),
        ),
        gradient: const LinearGradient(
          colors: [Color(0xFFEF4444), Color(0xFFDC2626)],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
      ),
      ColumnSeries<CourbeValeur, String>(
        color: const Color(0xFF3B82F6), // Bleu moderne
        dataSource: courbeValeurs!,
        xValueMapper: (CourbeValeur sales, _) => sales.jour,
        yValueMapper: (CourbeValeur sales, _) => sales.tontine,
        name: 'Tontine',
        width: 0.7, // Largeur augmentée
        spacing: 0.1, // Espacement réduit
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(6),
          topRight: Radius.circular(6),
        ),
        dataLabelSettings: const DataLabelSettings(
          isVisible: true,
          labelAlignment: ChartDataLabelAlignment.top,
          textStyle: TextStyle(fontSize: 10, color: Colors.black54),
        ),
        gradient: const LinearGradient(
          colors: [Color(0xFF3B82F6), Color(0xFF2563EB)],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
      ),
    ];
  }
}

List<Map<String, dynamic>> groupVersementsByDate(
  List<Map<String, dynamic>> data,
) {
  final Map<String, Map<String, double>> result = {};

  for (var v in data) {
    final date = v['date'] ?? '';
    final type = v['type'] ?? '';
    final montant = double.tryParse(v['montant'].toString()) ?? 0;

    if (!result.containsKey(date)) {
      result[date] = {'boutique': 0, 'tontine': 0};
    }

    if (type == 'boutique') {
      result[date]!['boutique'] = result[date]!['boutique']! + montant;
    } else if (type == 'tontine') {
      result[date]!['tontine'] = result[date]!['tontine']! + montant;
    }
  }

  return result.entries
      .map(
        (e) => {
          'date': e.key,
          'boutique': e.value['boutique']!,
          'tontine': e.value['tontine']!,
        },
      )
      .toList();
}

class ModernPerformanceCard extends StatelessWidget {
  final String title;
  final String value;
  final IconData icon;
  final Color color;
  final VoidCallback onTap;

  const ModernPerformanceCard({
    super.key,
    required this.title,
    required this.value,
    required this.icon,
    required this.color,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [color, color.withValues(alpha: 0.8)],
          ),
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: color.withValues(alpha: 0.3),
              blurRadius: 15,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Icon(icon, color: Colors.white, size: 18),
                ),
                const Icon(Icons.trending_up, color: Colors.white, size: 14),
              ],
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Text(
                  value,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class PerformanceTab extends StatelessWidget {
  final String title;
  final String icon;
  final String value;
  final Color color;
  final VoidCallback onTap;

  const PerformanceTab({
    super.key,
    required this.title,
    required this.icon,
    required this.value,
    required this.color,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        height: 120,
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(15),
          color: color,
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.5),
              spreadRadius: 1,
              blurRadius: 6,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SvgPicture.asset(icon, width: 40, height: 40, color: Colors.white),
            const SizedBox(height: 10),
            Text(
              title,
              style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 5),
            Text(
              value,
              style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
          ],
        ),
      ),
    );
  }
}

class StatCard extends StatelessWidget {
  final String title;
  final String amount;
  final IconData icon;
  final List<Color> gradient;

  const StatCard({
    super.key,
    required this.title,
    required this.amount,
    required this.icon,
    required this.gradient,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 5,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(15),
          gradient: LinearGradient(
            colors: gradient,
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Icon(icon, size: 25, color: Colors.white),
            const SizedBox(height: 5),
            Text(
              title,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 5),
            Text(
              amount,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.white,
                shadows: [
                  Shadow(
                    blurRadius: 2,
                    color: Colors.black,
                    offset: Offset(1, 1),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
