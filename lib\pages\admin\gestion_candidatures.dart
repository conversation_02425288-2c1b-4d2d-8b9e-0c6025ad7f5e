import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:provider/provider.dart';
import '../auth_provider.dart';

class GestionCandidaturesPage extends StatefulWidget {
  const GestionCandidaturesPage({super.key});

  @override
  State<GestionCandidaturesPage> createState() => _GestionCandidaturesPageState();
}

class _GestionCandidaturesPageState extends State<GestionCandidaturesPage> {
  List<Map<String, dynamic>> candidatures = [];
  bool _isLoading = true;
  String _selectedFilter = 'tous';

  final List<Map<String, dynamic>> _filters = [
    {'value': 'tous', 'label': 'Toutes', 'color': Colors.grey},
    {'value': 'en_attente', 'label': 'En attente', 'color': Colors.orange},
    {'value': 'en_cours_examen', 'label': 'En cours', 'color': Colors.blue},
    {'value': 'accepte', 'label': 'Acceptées', 'color': Colors.green},
    {'value': 'refuse', 'label': 'Refusées', 'color': Colors.red},
  ];

  @override
  void initState() {
    super.initState();
    _fetchCandidatures();
  }

  Future<void> _fetchCandidatures() async {
    try {
      final provide = Provider.of<AuthProvider>(context, listen: false);
      final token = provide.token;

      final response = await http.get(
        Uri.parse(provide.getEndpoint('candidature/get_candidatures.php')),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (mounted) {
          setState(() {
            candidatures = List<Map<String, dynamic>>.from(data['candidatures'] ?? []);
            _isLoading = false;
          });
        }
      } else {
        throw Exception('Erreur ${response.statusCode}');
      }
    } catch (error) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        _showErrorMessage('Erreur lors du chargement: $error');
      }
    }
  }

  List<Map<String, dynamic>> get _filteredCandidatures {
    if (_selectedFilter == 'tous') {
      return candidatures;
    }
    return candidatures.where((c) => c['statut'] == _selectedFilter).toList();
  }

  Future<void> _updateStatut(int candidatureId, String newStatut) async {
    try {
      final provide = Provider.of<AuthProvider>(context, listen: false);
      final token = provide.token;

      final response = await http.post(
        Uri.parse(provide.getEndpoint('candidature/update_statut.php')),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
        body: json.encode({
          'candidature_id': candidatureId,
          'statut': newStatut,
        }),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success']) {
          _fetchCandidatures(); // Recharger la liste
          _showSuccessMessage('Statut mis à jour avec succès');
        } else {
          _showErrorMessage(data['message'] ?? 'Erreur lors de la mise à jour');
        }
      }
    } catch (error) {
      _showErrorMessage('Erreur: $error');
    }
  }

  void _showCandidatureDetails(Map<String, dynamic> candidature) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('${candidature['prenom']} ${candidature['nom']}'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailRow('Email', candidature['email']),
              _buildDetailRow('Téléphone', candidature['telephone']),
              _buildDetailRow('Adresse', candidature['adresse']),
              _buildDetailRow('Genre', candidature['genre']),
              _buildDetailRow('Niveau d\'étude', candidature['niveau_etude']),
              _buildDetailRow('Date de candidature', candidature['date_candidature']),
              const SizedBox(height: 16),
              const Text('Expérience:', style: TextStyle(fontWeight: FontWeight.bold)),
              Text(candidature['experience'] ?? 'Non renseignée'),
              const SizedBox(height: 16),
              const Text('Motivation:', style: TextStyle(fontWeight: FontWeight.bold)),
              Text(candidature['motivation']),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Fermer'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _showStatutDialog(candidature);
            },
            child: const Text('Modifier statut'),
          ),
        ],
      ),
    );
  }

  void _showStatutDialog(Map<String, dynamic> candidature) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Modifier le statut'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            'en_attente',
            'en_cours_examen',
            'accepte',
            'refuse'
          ].map((statut) => ListTile(
            title: Text(_getStatutLabel(statut)),
            leading: Icon(
              Icons.circle,
              color: _getStatutColor(statut),
            ),
            onTap: () {
              Navigator.pop(context);
              _updateStatut(candidature['id'], statut);
            },
          )).toList(),
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  String _getStatutLabel(String statut) {
    switch (statut) {
      case 'en_attente': return 'En attente';
      case 'en_cours_examen': return 'En cours d\'examen';
      case 'accepte': return 'Acceptée';
      case 'refuse': return 'Refusée';
      default: return statut;
    }
  }

  Color _getStatutColor(String statut) {
    switch (statut) {
      case 'en_attente': return Colors.orange;
      case 'en_cours_examen': return Colors.blue;
      case 'accepte': return Colors.green;
      case 'refuse': return Colors.red;
      default: return Colors.grey;
    }
  }

  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.green),
    );
  }

  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.red),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Gestion des Candidatures'),
        backgroundColor: Colors.orange,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _fetchCandidatures,
          ),
        ],
      ),
      body: Column(
        children: [
          // Filtres
          Container(
            height: 60,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _filters.length,
              itemBuilder: (context, index) {
                final filter = _filters[index];
                final isSelected = _selectedFilter == filter['value'];
                
                return Padding(
                  padding: const EdgeInsets.only(right: 8),
                  child: FilterChip(
                    label: Text(filter['label']),
                    selected: isSelected,
                    onSelected: (selected) {
                      setState(() {
                        _selectedFilter = filter['value'];
                      });
                    },
                    backgroundColor: filter['color'].withOpacity(0.1),
                    selectedColor: filter['color'].withOpacity(0.3),
                  ),
                );
              },
            ),
          ),
          
          // Liste des candidatures
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _filteredCandidatures.isEmpty
                    ? const Center(
                        child: Text(
                          'Aucune candidature trouvée',
                          style: TextStyle(fontSize: 16, color: Colors.grey),
                        ),
                      )
                    : ListView.builder(
                        itemCount: _filteredCandidatures.length,
                        itemBuilder: (context, index) {
                          final candidature = _filteredCandidatures[index];
                          return Card(
                            margin: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 4,
                            ),
                            child: ListTile(
                              leading: CircleAvatar(
                                backgroundColor: _getStatutColor(candidature['statut']),
                                child: Text(
                                  '${candidature['prenom'][0]}${candidature['nom'][0]}',
                                  style: const TextStyle(color: Colors.white),
                                ),
                              ),
                              title: Text(
                                '${candidature['prenom']} ${candidature['nom']}',
                                style: const TextStyle(fontWeight: FontWeight.bold),
                              ),
                              subtitle: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(candidature['email']),
                                  Text(
                                    _getStatutLabel(candidature['statut']),
                                    style: TextStyle(
                                      color: _getStatutColor(candidature['statut']),
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                              trailing: const Icon(Icons.arrow_forward_ios),
                              onTap: () => _showCandidatureDetails(candidature),
                            ),
                          );
                        },
                      ),
          ),
        ],
      ),
    );
  }
}
