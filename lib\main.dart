import 'package:callitris/pages/clients/new_client_page.dart';
import 'package:callitris/pages/not_autorise.dart';
import 'package:callitris/pages/payment_success_page.dart';
import 'package:callitris/pages/services/new_commande_page.dart';
import 'package:callitris/pages/services/scanner_encaissement.dart';
import 'package:callitris/pages/services/banque.dart';
import 'package:callitris/pages/services/client_boutique.dart';
import 'package:callitris/pages/services/client_tontine.dart';
import 'package:callitris/pages/services/commandes.dart';
import 'package:callitris/pages/services/edit_bank.dart';
import 'package:callitris/pages/services/graph.dart';
import 'package:callitris/pages/services/client_as.dart';
import 'package:callitris/pages/services/help.dart';
import 'package:callitris/pages/services/mission.dart';
import 'package:callitris/pages/services/indicateur.dart';
import 'package:callitris/pages/services/salaire.dart';
import 'package:callitris/pages/services/statistic.dart';
import 'package:flutter/material.dart';
import 'package:callitris/pages/services/livraison.dart';
import 'package:callitris/pages/services/versement.dart';
import 'package:callitris/pages/auth_provider.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:callitris/pages/clients/add_client.dart';
import 'package:callitris/pages/clients/client_payment_page.dart';
import 'package:callitris/pages/services/tontine.dart';
import 'package:callitris/pages/home.dart';
import 'package:callitris/pages/login.dart';
import 'package:callitris/pages/services.dart';
import 'package:callitris/pages/client.dart';
import 'package:callitris/pages/logo_animation.dart';
import 'package:callitris/pages/profile.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:app_links/app_links.dart';
import 'dart:async';
import 'services/deep_link_manager.dart';

void main() {
  runApp(
    ChangeNotifierProvider(
      create: (context) =>
          AuthProvider(), // fournisseur pour gérer l'authentification
      child: MainApp(),
    ),
  );
}

class MonTheme {
  static final ThemeData themeData = ThemeData(
    fontFamily: GoogleFonts.ubuntu(fontWeight: FontWeight.w400).fontFamily,
    primaryColor: Colors.blue,
    // Autres propriétés de thème peuvent être ajoutées ici
  );
}

bool isAuthenticated(BuildContext context) {
  final authProvider = Provider.of<AuthProvider>(context, listen: false).token;
  return authProvider != null; // Vérifie si le token est présent
}

class MainApp extends StatefulWidget {
  const MainApp({super.key});

  @override
  State<MainApp> createState() => _MainAppState();
}

class _MainAppState extends State<MainApp> {
  final _navigatorKey = GlobalKey<NavigatorState>();
  final DeepLinkManager _deepLinkManager = DeepLinkManager();

  @override
  void initState() {
    super.initState();
    // Initialiser le gestionnaire de deep links après que le widget soit construit
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _deepLinkManager.initialize(context);
    });
  }

  @override
  void dispose() {
    _deepLinkManager.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
        debugShowCheckedModeBanner: false,
        theme: MonTheme.themeData,
        localizationsDelegates: const [
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        supportedLocales: const [
          Locale('fr', 'FR'), // Français
          Locale('en', 'US'), // Anglais
        ],
        initialRoute: '/',
        onGenerateRoute: (settings) {
          if (isAuthenticated(context)) {
            switch (settings.name) {
              case '/':
                return PageRouteBuilder(
                  pageBuilder: (context, animation, secondaryAnimation) =>
                      const LogoAnimationPage(),
                  transitionsBuilder:
                      (context, animation, secondaryAnimation, child) {
                    return FadeTransition(
                      opacity: animation,
                      child: child,
                    );
                  },
                );
              case '/login':
                return PageRouteBuilder(
                  pageBuilder: (context, animation, secondaryAnimation) =>
                      const LoginPage(),
                  transitionsBuilder:
                      (context, animation, secondaryAnimation, child) {
                    return FadeTransition(
                      opacity: animation,
                      child: child,
                    );
                  },
                );
              case '/home':
                return PageRouteBuilder(
                  pageBuilder: (context, animation, secondaryAnimation) =>
                      const HomePage(),
                  transitionsBuilder:
                      (context, animation, secondaryAnimation, child) {
                    return FadeTransition(
                      opacity: animation,
                      child: child,
                    );
                  },
                );
              /* case '/test':
                return PageRouteBuilder(
                  pageBuilder: (context, animation, secondaryAnimation) =>
                      MyApp(),
                  transitionsBuilder:
                      (context, animation, secondaryAnimation, child) {
                    return FadeTransition(
                      opacity: animation,
                      child: child,
                    );
                  },
                ); */
              case '/indicateur':
                return PageRouteBuilder(
                  pageBuilder: (context, animation, secondaryAnimation) =>
                      const IndicateurPage(),
                  transitionsBuilder:
                      (context, animation, secondaryAnimation, child) {
                    return FadeTransition(
                      opacity: animation,
                      child: child,
                    );
                  },
                );
              case '/statistic':
                return PageRouteBuilder(
                  pageBuilder: (context, animation, secondaryAnimation) =>
                      const StatisticPage(),
                  transitionsBuilder:
                      (context, animation, secondaryAnimation, child) {
                    return FadeTransition(
                      opacity: animation,
                      child: child,
                    );
                  },
                );
              case '/banque':
                return PageRouteBuilder(
                  pageBuilder: (context, animation, secondaryAnimation) =>
                      const BanquePage(),
                  transitionsBuilder:
                      (context, animation, secondaryAnimation, child) {
                    return FadeTransition(
                      opacity: animation,
                      child: child,
                    );
                  },
                );
              case '/salaire':
                return PageRouteBuilder(
                  pageBuilder: (context, animation, secondaryAnimation) =>
                      const SalairePage(),
                  transitionsBuilder:
                      (context, animation, secondaryAnimation, child) {
                    return FadeTransition(
                      opacity: animation,
                      child: child,
                    );
                  },
                );
              case '/graph':
                return PageRouteBuilder(
                  pageBuilder: (context, animation, secondaryAnimation) =>
                      const LineDefault(),
                  transitionsBuilder:
                      (context, animation, secondaryAnimation, child) {
                    return FadeTransition(
                      opacity: animation,
                      child: child,
                    );
                  },
                );
              case '/help':
                return PageRouteBuilder(
                  pageBuilder: (context, animation, secondaryAnimation) =>
                      const HelpPage(),
                  transitionsBuilder:
                      (context, animation, secondaryAnimation, child) {
                    return FadeTransition(
                      opacity: animation,
                      child: child,
                    );
                  },
                );
              case '/mission':
                return PageRouteBuilder(
                  pageBuilder: (context, animation, secondaryAnimation) =>
                      const MissionPage(),
                  transitionsBuilder:
                      (context, animation, secondaryAnimation, child) {
                    return FadeTransition(
                      opacity: animation,
                      child: child,
                    );
                  },
                );
              case '/client_as':
                return PageRouteBuilder(
                  pageBuilder: (context, animation, secondaryAnimation) =>
                      const ClientSatisfairePage(),
                  transitionsBuilder:
                      (context, animation, secondaryAnimation, child) {
                    return FadeTransition(
                      opacity: animation,
                      child: child,
                    );
                  },
                );

              case '/carnet':
                return PageRouteBuilder(
                  pageBuilder: (context, animation, secondaryAnimation) =>
                      const CommandePage(),
                  transitionsBuilder:
                      (context, animation, secondaryAnimation, child) {
                    return FadeTransition(
                      opacity: animation,
                      child: child,
                    );
                  },
                );
              case '/edit_bank':
                return PageRouteBuilder(
                  pageBuilder: (context, animation, secondaryAnimation) =>
                      EditBankPage(),
                  transitionsBuilder:
                      (context, animation, secondaryAnimation, child) {
                    return FadeTransition(
                      opacity: animation,
                      child: child,
                    );
                  },
                );
              case '/services':
                return PageRouteBuilder(
                  pageBuilder: (context, animation, secondaryAnimation) =>
                      const ServicesPage(),
                  transitionsBuilder:
                      (context, animation, secondaryAnimation, child) {
                    return FadeTransition(
                      opacity: animation,
                      child: child,
                    );
                  },
                );
              case '/client':
                return PageRouteBuilder(
                  pageBuilder: (context, animation, secondaryAnimation) =>
                      ClientPage(),
                  transitionsBuilder:
                      (context, animation, secondaryAnimation, child) {
                    return FadeTransition(
                      opacity: animation,
                      child: child,
                    );
                  },
                );
              case '/profile':
                return PageRouteBuilder(
                  pageBuilder: (context, animation, secondaryAnimation) =>
                      const ProfilePage(),
                  transitionsBuilder:
                      (context, animation, secondaryAnimation, child) {
                    return FadeTransition(
                      opacity: animation,
                      child: child,
                    );
                  },
                );
              case '/add_client':
                return PageRouteBuilder(
                  pageBuilder: (context, animation, secondaryAnimation) =>
                      const NewClientRegisterPage(),
                  transitionsBuilder:
                      (context, animation, secondaryAnimation, child) {
                    return FadeTransition(
                      opacity: animation,
                      child: child,
                    );
                  },
                );
              case '/tontine':
                return PageRouteBuilder(
                  pageBuilder: (context, animation, secondaryAnimation) =>
                      const TontinePage(),
                  transitionsBuilder:
                      (context, animation, secondaryAnimation, child) {
                    return FadeTransition(
                      opacity: animation,
                      child: child,
                    );
                  },
                );
              case '/client_boutique':
                return PageRouteBuilder(
                  pageBuilder: (context, animation, secondaryAnimation) =>
                      const ClientBoutiquePage(),
                  transitionsBuilder:
                      (context, animation, secondaryAnimation, child) {
                    return FadeTransition(
                      opacity: animation,
                      child: child,
                    );
                  },
                );
              case '/client_tontine':
                return PageRouteBuilder(
                  pageBuilder: (context, animation, secondaryAnimation) =>
                      const ClientTontinePage(),
                  transitionsBuilder:
                      (context, animation, secondaryAnimation, child) {
                    return FadeTransition(
                      opacity: animation,
                      child: child,
                    );
                  },
                );
              case '/livraison':
                return PageRouteBuilder(
                  pageBuilder: (context, animation, secondaryAnimation) =>
                      const LivraisonPage(),
                  transitionsBuilder:
                      (context, animation, secondaryAnimation, child) {
                    return FadeTransition(
                      opacity: animation,
                      child: child,
                    );
                  },
                );
              case '/versement':
                return PageRouteBuilder(
                  pageBuilder: (context, animation, secondaryAnimation) =>
                      const VersementPage(),
                  transitionsBuilder:
                      (context, animation, secondaryAnimation, child) {
                    return FadeTransition(
                      opacity: animation,
                      child: child,
                    );
                  },
                );
              case '/scanner_encaissement':
                return PageRouteBuilder(
                  pageBuilder: (context, animation, secondaryAnimation) =>
                      const ScannerEncaissement(),
                  transitionsBuilder:
                      (context, animation, secondaryAnimation, child) {
                    return FadeTransition(opacity: animation, child: child);
                  },
                );
              case '/client_payment':
                final args = settings.arguments as Map;
                return PageRouteBuilder(
                  pageBuilder: (context, animation, secondaryAnimation) =>
                      ClientPaymentPage(
                    clientData: args['clientData'],
                    id: args['commandeId'],
                  ),
                  transitionsBuilder:
                      (context, animation, secondaryAnimation, child) {
                    return SlideTransition(
                      position: animation.drive(
                        Tween(begin: const Offset(1.0, 0.0), end: Offset.zero)
                            .chain(CurveTween(curve: Curves.easeInOut)),
                      ),
                      child: child,
                    );
                  },
                );
              case '/not_autorise':
                return PageRouteBuilder(
                  pageBuilder: (context, animation, secondaryAnimation) =>
                      NotAuthorise(),
                  transitionsBuilder:
                      (context, animation, secondaryAnimation, child) {
                    return FadeTransition(
                      opacity: animation,
                      child: child,
                    );
                  },
                );
              case '/payment_result':
                final args = settings.arguments as Map<String, dynamic>?;
                return PageRouteBuilder(
                  pageBuilder: (context, animation, secondaryAnimation) =>
                      PaymentSuccessPage(),
                  transitionsBuilder:
                      (context, animation, secondaryAnimation, child) {
                    return FadeTransition(opacity: animation, child: child);
                  },
                );
              case '/new_client':
                return PageRouteBuilder(
                  pageBuilder: (context, animation, secondaryAnimation) =>
                      const NewClientPage(),
                  transitionsBuilder:
                      (context, animation, secondaryAnimation, child) {
                    return FadeTransition(
                      opacity: animation,
                      child: child,
                    );
                  },
                );
              case '/new_commande':
                final args = settings.arguments as Map<String, dynamic>?;
                return PageRouteBuilder(
                  pageBuilder: (context, animation, secondaryAnimation) =>
                      const NewCommandePage(),
                  transitionsBuilder:
                      (context, animation, secondaryAnimation, child) {
                    return FadeTransition(opacity: animation, child: child);
                  },
                );
              default:
                return null;
            }
          } else {
            return MaterialPageRoute(builder: (_) => const LoginPage());
          }
        });
  }
}
