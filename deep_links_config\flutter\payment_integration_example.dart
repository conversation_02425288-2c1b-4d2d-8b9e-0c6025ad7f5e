import 'package:callitris/pages/auth_provider.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:provider/provider.dart';

// Imports à adapter selon votre structure
// import 'package:callitris/pages/auth_provider.dart';
// import 'package:callitris/services/payment_deep_link_integration.dart';

/// Exemple d'intégration des deep links dans une page de paiement
/// Ce fichier montre comment modifier les méthodes de paiement existantes
class PaymentIntegrationExample {
  
  /// Exemple de modification de _processPayment pour CinetPay avec deep links
  static Future<void> processPaymentWithDeepLinks({
    required BuildContext context,
    required String amount,
    required String phone,
    required String commandId,
    required String clientId,
    required String paymentMethod,
  }) async {
    try {
      // Récupérer le provider d'authentification
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final token = authProvider.token;

      if (token == null) {
        _showSnackBar(context, 'Session expirée. Veuillez vous reconnecter.');
        return;
      }

      // Préparer les données de base
      Map<String, dynamic> requestBody = {
        'amount': amount,
        'phone': phone,
        'command_id': commandId,
        'client_id': clientId,
        'channels': _getChannels(paymentMethod),
      };

      // Améliorer les données avec les deep links selon le provider
      if (paymentMethod == 'cinetpay') {
        requestBody = PaymentDeepLinkIntegration.enhanceCinetPayPaymentData(
          requestBody,
          commandId: commandId,
          clientId: clientId,
        );
      } else {
        // Pour les autres providers, utiliser l'amélioration générique
        // requestBody = PaymentDeepLinkIntegration.enhancePaymentData(
        //   requestBody,
        //   commandId: commandId,
        //   clientId: clientId,
        // );
      }

      print('Données de paiement avec deep links: $requestBody');

      // Effectuer la requête de paiement
      final response = await http.post(
        Uri.parse(authProvider.getEndpoint('products/addTransaction.php')),
        body: json.encode(requestBody),
        headers: {
          'Authorization': token,
          'Content-Type': 'application/json'
        },
      );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        print('Réponse paiement: $responseData');

        if (responseData['success'] == true) {
          final paymentUrl = responseData['payment_url']?.toString().replaceAll('\\', '') ?? '';
          final transactionId = responseData['transaction_id'];

          // Sauvegarder les informations de paiement
          await _savePaymentData(commandId, clientId, transactionId);

          if (paymentUrl.isNotEmpty) {
            // Améliorer l'URL de paiement avec les paramètres de deep link
            final enhancedUrl = PaymentDeepLinkIntegration.enhancePaymentUrl(
              paymentUrl,
              commandId: commandId,
              clientId: clientId,
              transactionId: transactionId,
            );

            print('URL de paiement améliorée: $enhancedUrl');
            await _launchPaymentUrl(context, enhancedUrl);
          }
        } else {
          _showSnackBar(context, responseData['message']?.toString() ?? 'Erreur de paiement');
        }
      } else {
        _showSnackBar(context, 'Erreur serveur: ${response.statusCode}');
      }
    } catch (e) {
      print('Erreur lors du paiement: $e');
      _showSnackBar(context, 'Erreur lors du paiement: $e');
    }
  }

  /// Exemple de modification de _processWavePayment avec deep links
  static Future<void> processWavePaymentWithDeepLinks({
    required BuildContext context,
    required String amount,
    required String phone,
    required String commandId,
    required String clientId,
  }) async {
    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final token = authProvider.token;

      if (token == null) {
        _showSnackBar(context, 'Session expirée. Veuillez vous reconnecter.');
        return;
      }

      // Préparer les données de base pour Wave
      Map<String, dynamic> requestBody = {
        'amount': amount,
        'phone': phone,
        'command_id': commandId,
        'client_id': clientId,
      };

      // Améliorer les données avec les deep links pour Wave
      requestBody = PaymentDeepLinkIntegration.enhanceWavePaymentData(
        requestBody,
        commandId: commandId,
        clientId: clientId,
      );

      print('Données Wave avec deep links: $requestBody');

      final response = await http.post(
        Uri.parse(authProvider.getEndpoint('products/wave_init_pay.php')),
        body: json.encode(requestBody),
        headers: {
          'Authorization': token,
          'Content-Type': 'application/json'
        },
      );

      if (response.statusCode == 200) {
        final responseBody = response.body.trim();
        
        if (responseBody.startsWith('{') || responseBody.startsWith('[')) {
          final responseData = jsonDecode(responseBody);

          if (responseData.containsKey('wave_launch_url')) {
            final waveLaunchUrl = responseData['wave_launch_url']?.toString() ?? '';
            final paymentId = responseData['id']?.toString() ?? '';

            if (paymentId.isNotEmpty) {
              await _savePaymentData(commandId, clientId, paymentId);
            }

            if (waveLaunchUrl.isNotEmpty) {
              // Pour Wave, l'URL est déjà configurée avec les callbacks
              await _launchPaymentUrl(context, waveLaunchUrl);
            }
          } else {
            _showSnackBar(context, 'Erreur: URL de paiement Wave non trouvée');
          }
        } else {
          _showSnackBar(context, 'Erreur de format de réponse du serveur');
        }
      } else {
        _showSnackBar(context, 'Erreur serveur: ${response.statusCode}');
      }
    } catch (e) {
      print('Erreur lors du paiement Wave: $e');
      _showSnackBar(context, 'Erreur lors du paiement Wave: $e');
    }
  }

  /// Fonction utilitaire pour obtenir les canaux de paiement
  static List<String> _getChannels(String paymentMethod) {
    switch (paymentMethod) {
      case 'orange_money':
        return ['ORANGE_MONEY_CI'];
      case 'mtn_momo':
        return ['MTN_MOMO_CI'];
      case 'moov_money':
        return ['MOOV_MONEY_CI'];
      case 'wave_ci':
        return ['WAVE_CI'];
      default:
        return ['ORANGE_MONEY_CI', 'MTN_MOMO_CI', 'MOOV_MONEY_CI'];
    }
  }

  /// Fonction utilitaire pour sauvegarder les données de paiement
  static Future<void> _savePaymentData(String commandId, String clientId, String transactionId) async {
    try {
      // Utiliser SharedPreferences pour sauvegarder
      // final prefs = await SharedPreferences.getInstance();
      // await prefs.setString('payment_code', transactionId);
      // await prefs.setString('commande_id', commandId);
      // await prefs.setString('client_id', clientId);
      print('Données de paiement sauvegardées: Command=$commandId, Client=$clientId, Transaction=$transactionId');
    } catch (e) {
      print('Erreur lors de la sauvegarde des données de paiement: $e');
    }
  }

  /// Fonction utilitaire pour lancer l'URL de paiement
  static Future<void> _launchPaymentUrl(BuildContext context, String url) async {
    try {
      // Utiliser url_launcher pour ouvrir l'URL
      // await launch(url);
      print('Lancement de l\'URL de paiement: $url');
      
      // Afficher un message à l'utilisateur
      _showSnackBar(context, 'Redirection vers la plateforme de paiement...');
    } catch (e) {
      print('Erreur lors du lancement de l\'URL: $e');
      _showSnackBar(context, 'Erreur lors de l\'ouverture de la plateforme de paiement');
    }
  }

  /// Fonction utilitaire pour afficher un SnackBar
  static void _showSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: const Duration(seconds: 3),
      ),
    );
  }

  /// Widget pour tester les deep links (à ajouter dans les pages de développement)
  static Widget buildDeepLinkTestWidget(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Test Deep Links',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton(
                  onPressed: () {
                    PaymentDeepLinkIntegration.testDeepLink(
                      context,
                      status: 'success',
                      commandId: 'test_cmd_123',
                      clientId: 'test_client_456',
                    );
                  },
                  style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
                  child: const Text('Test Succès'),
                ),
                ElevatedButton(
                  onPressed: () {
                    PaymentDeepLinkIntegration.testDeepLink(
                      context,
                      status: 'failure',
                      commandId: 'test_cmd_123',
                      clientId: 'test_client_456',
                    );
                  },
                  style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                  child: const Text('Test Échec'),
                ),
                ElevatedButton(
                  onPressed: () {
                    PaymentDeepLinkIntegration.testDeepLink(
                      context,
                      status: 'pending',
                      commandId: 'test_cmd_123',
                      clientId: 'test_client_456',
                    );
                  },
                  style: ElevatedButton.styleFrom(backgroundColor: Colors.orange),
                  child: const Text('Test Attente'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

/// Classe pour les constantes de deep links
class DeepLinkConstants {
  static const String scheme = 'callitris';
  static const String paymentHost = 'payment';
  static const String callbackHost = 'callback';
  
  static const String successPath = '/success';
  static const String failurePath = '/failure';
  static const String pendingPath = '/pending';
  
  static const String baseCallbackUrl = 'https://dev-mani.io/teams/api.callitris-distribution.com/deep_links_config/backend';
  static const String waveCallbackUrl = '$baseCallbackUrl/wave_callback.php';
  static const String cinetPayCallbackUrl = '$baseCallbackUrl/cinetpay_callback.php';
  static const String genericCallbackUrl = '$baseCallbackUrl/payment_redirect.php';
}
