<?php
/**
 * Gestionnaire de redirection générique pour les paiements
 * Ce fichier peut être utilisé comme point d'entrée unique pour tous les callbacks de paiement
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Gestion des requêtes OPTIONS pour CORS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Configuration
$config = [
    'app_scheme' => 'callitris',
    'log_file' => __DIR__ . '/logs/payment_redirect.log',
    'allowed_providers' => ['wave', 'cinetpay', 'orange_money', 'mtn_momo', 'moov_money']
];

// Fonction de logging
function logMessage($message, $logFile) {
    $timestamp = date('Y-m-d H:i:s');
    $logEntry = "[$timestamp] $message" . PHP_EOL;
    
    // Créer le dossier logs s'il n'existe pas
    $logDir = dirname($logFile);
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
}

// Fonction pour générer le deep link
function generateDeepLink($status, $params = []) {
    global $config;
    
    $baseUrl = $config['app_scheme'] . '://payment/' . $status;
    
    if (!empty($params)) {
        $queryString = http_build_query($params);
        $baseUrl .= '?' . $queryString;
    }
    
    return $baseUrl;
}

// Fonction pour normaliser les statuts de paiement
function normalizePaymentStatus($status, $provider) {
    $status = strtolower(trim($status));
    
    switch ($provider) {
        case 'wave':
            switch ($status) {
                case 'successful':
                case 'success':
                case 'completed':
                    return 'success';
                case 'failed':
                case 'failure':
                case 'cancelled':
                    return 'failure';
                case 'pending':
                case 'processing':
                    return 'pending';
            }
            break;
            
        case 'cinetpay':
            switch ($status) {
                case '00':
                case 'accepted':
                case 'success':
                    return 'success';
                case '01':
                case '02':
                case 'failed':
                case 'refused':
                case 'cancelled':
                    return 'failure';
                case 'pending':
                case 'processing':
                    return 'pending';
            }
            break;
            
        case 'orange_money':
        case 'mtn_momo':
        case 'moov_money':
            switch ($status) {
                case 'success':
                case 'successful':
                case 'completed':
                case '200':
                    return 'success';
                case 'failed':
                case 'failure':
                case 'error':
                case 'cancelled':
                    return 'failure';
                case 'pending':
                case 'processing':
                    return 'pending';
            }
            break;
    }
    
    // Statut par défaut si non reconnu
    return 'failure';
}

// Fonction pour extraire les paramètres selon le provider
function extractPaymentParams($data, $provider) {
    $params = [];
    
    switch ($provider) {
        case 'wave':
            $params = [
                'transaction_id' => $data['id'] ?? $data['transaction_id'] ?? '',
                'amount' => $data['amount'] ?? 0,
                'currency' => $data['currency'] ?? 'XOF',
                'reference' => $data['wave_reference'] ?? '',
                'provider' => 'wave'
            ];
            
            // Extraire les données custom
            if (!empty($data['client_reference'])) {
                $customData = json_decode($data['client_reference'], true);
                if (is_array($customData)) {
                    $params = array_merge($params, $customData);
                }
            }
            break;
            
        case 'cinetpay':
            $params = [
                'transaction_id' => $data['cpm_trans_id'] ?? $data['transaction_id'] ?? '',
                'amount' => $data['cpm_amount'] ?? $data['amount'] ?? 0,
                'currency' => $data['cpm_currency'] ?? $data['currency'] ?? 'XOF',
                'reference' => $data['cpm_payid'] ?? '',
                'provider' => 'cinetpay'
            ];
            
            // Extraire les données custom
            if (!empty($data['cpm_custom'])) {
                $customData = json_decode($data['cpm_custom'], true);
                if (is_array($customData)) {
                    $params = array_merge($params, $customData);
                }
            }
            break;
            
        default:
            // Extraction générique
            $params = [
                'transaction_id' => $data['transaction_id'] ?? $data['id'] ?? '',
                'amount' => $data['amount'] ?? 0,
                'currency' => $data['currency'] ?? 'XOF',
                'reference' => $data['reference'] ?? '',
                'provider' => $provider
            ];
            
            // Ajouter command_id et client_id s'ils existent
            if (isset($data['command_id'])) {
                $params['command_id'] = $data['command_id'];
            }
            if (isset($data['client_id'])) {
                $params['client_id'] = $data['client_id'];
            }
    }
    
    return $params;
}

try {
    // Déterminer le provider de paiement
    $provider = $_GET['provider'] ?? $_POST['provider'] ?? '';
    
    // Si pas de provider spécifié, essayer de le détecter
    if (empty($provider)) {
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        $referer = $_SERVER['HTTP_REFERER'] ?? '';
        
        if (strpos($userAgent, 'Wave') !== false || strpos($referer, 'wave') !== false) {
            $provider = 'wave';
        } elseif (strpos($userAgent, 'CinetPay') !== false || strpos($referer, 'cinetpay') !== false) {
            $provider = 'cinetpay';
        } elseif (strpos($referer, 'orange') !== false) {
            $provider = 'orange_money';
        } elseif (strpos($referer, 'mtn') !== false) {
            $provider = 'mtn_momo';
        } elseif (strpos($referer, 'moov') !== false) {
            $provider = 'moov_money';
        }
    }
    
    if (empty($provider) || !in_array($provider, $config['allowed_providers'])) {
        $provider = 'unknown';
    }
    
    $method = $_SERVER['REQUEST_METHOD'];
    logMessage("Payment redirect - Provider: $provider, Method: $method", $config['log_file']);
    
    if ($method === 'POST') {
        // Callback de notification
        $rawInput = file_get_contents('php://input');
        $data = json_decode($rawInput, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            // Essayer de traiter comme des données de formulaire
            parse_str($rawInput, $data);
            if (empty($data)) {
                $data = $_POST;
            }
        }
        
        logMessage("Payment callback data: " . json_encode($data), $config['log_file']);
        
        // Extraire les paramètres selon le provider
        $params = extractPaymentParams($data, $provider);
        
        // Déterminer le statut
        $rawStatus = '';
        switch ($provider) {
            case 'wave':
                $rawStatus = $data['status'] ?? '';
                break;
            case 'cinetpay':
                $rawStatus = $data['cpm_result'] ?? $data['status'] ?? '';
                break;
            default:
                $rawStatus = $data['status'] ?? '';
        }
        
        $normalizedStatus = normalizePaymentStatus($rawStatus, $provider);
        
        // Ajouter des informations d'erreur si échec
        if ($normalizedStatus === 'failure') {
            $params['reason'] = $data['error_message'] ?? $data['cpm_error_message'] ?? $data['failure_reason'] ?? 'Paiement échoué';
        }
        
        // Générer le deep link
        $deepLink = generateDeepLink($normalizedStatus, $params);
        
        logMessage("Generated deep link: $deepLink", $config['log_file']);
        
        // Répondre au provider
        http_response_code(200);

        // Si c'est une requête AJAX ou API, retourner JSON
        $contentType = $_SERVER['HTTP_ACCEPT'] ?? '';
        if (strpos($contentType, 'application/json') !== false ||
            isset($_SERVER['HTTP_X_REQUESTED_WITH']) &&
            strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest') {
            echo json_encode([
                'status' => 'success',
                'message' => 'Callback processed',
                'provider' => $provider,
                'deep_link' => $deepLink
            ]);
        } else {
            // Sinon, rediriger directement vers l'app
            header("Location: $deepLink");
            exit();
        }
        
    } elseif ($method === 'GET') {
        // Redirection depuis le provider vers l'app
        $data = $_GET;
        
        logMessage("Payment redirect data: " . json_encode($data), $config['log_file']);
        
        // Extraire les paramètres selon le provider
        $params = extractPaymentParams($data, $provider);
        
        // Déterminer le statut
        $rawStatus = '';
        switch ($provider) {
            case 'wave':
                $rawStatus = $data['status'] ?? '';
                break;
            case 'cinetpay':
                $rawStatus = $data['cpm_result'] ?? $data['status'] ?? '';
                break;
            default:
                $rawStatus = $data['status'] ?? '';
        }
        
        $normalizedStatus = normalizePaymentStatus($rawStatus, $provider);
        
        // Ajouter des informations d'erreur si échec
        if ($normalizedStatus === 'failure') {
            $params['reason'] = $data['error_message'] ?? $data['reason'] ?? 'Paiement échoué';
        }
        
        // Générer le deep link
        $deepLink = generateDeepLink($normalizedStatus, $params);
        
        logMessage("Redirecting to deep link: $deepLink", $config['log_file']);
        
        // Page de redirection avec JavaScript pour gérer les cas où le deep link ne fonctionne pas
        ?>
        <!DOCTYPE html>
        <html>
        <head>
            <title>Redirection vers Callitris</title>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1">
            <style>
                body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
                .container { max-width: 400px; margin: 0 auto; }
                .status { font-size: 24px; margin: 20px 0; }
                .success { color: #4CAF50; }
                .failure { color: #f44336; }
                .pending { color: #ff9800; }
                .button { 
                    background-color: #2196F3; 
                    color: white; 
                    padding: 10px 20px; 
                    text-decoration: none; 
                    border-radius: 5px; 
                    display: inline-block; 
                    margin: 10px;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>Callitris Payment</h1>
                <div class="status <?php echo $normalizedStatus; ?>">
                    <?php
                    switch ($normalizedStatus) {
                        case 'success':
                            echo '✓ Paiement réussi';
                            break;
                        case 'failure':
                            echo '✗ Paiement échoué';
                            break;
                        case 'pending':
                            echo '⏳ Paiement en attente';
                            break;
                    }
                    ?>
                </div>
                <p>Redirection vers l'application...</p>
                <button onclick="manualOpen()" class="button">Ouvrir l'application</button>
                <br><br>
                <a href="<?php echo htmlspecialchars($deepLink); ?>" class="button" style="background-color: #4CAF50;">Lien direct</a>
            </div>
            
            <script>
                // Fonction pour ouvrir l'application
                function openApp() {
                    const deepLink = '<?php echo addslashes($deepLink); ?>';

                    // Méthode 1: Redirection directe
                    window.location.href = deepLink;

                    // Méthode 2: Créer un lien invisible et le cliquer
                    const link = document.createElement('a');
                    link.href = deepLink;
                    link.style.display = 'none';
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);

                    // Méthode 3: Utiliser window.open comme fallback
                    setTimeout(function() {
                        try {
                            window.open(deepLink, '_self');
                        } catch(e) {
                            console.log('Erreur ouverture app:', e);
                        }
                    }, 500);
                }

                // Tentative immédiate
                openApp();

                // Retry après 1 seconde
                setTimeout(openApp, 1000);

                // Message de fallback
                setTimeout(function() {
                    document.body.innerHTML += '<p><small>Si l\'application ne s\'ouvre pas, cliquez sur le bouton ci-dessus ou assurez-vous qu\'elle est installée.</small></p>';
                }, 3000);

                // Fonction pour le bouton manuel
                function manualOpen() {
                    openApp();
                }
            </script>
        </body>
        </html>
        <?php
        exit();
        
    } else {
        // Méthode non supportée
        http_response_code(405);
        echo json_encode(['error' => 'Method not allowed']);
    }
    
} catch (Exception $e) {
    logMessage("Error in payment redirect: " . $e->getMessage(), $config['log_file']);
    
    http_response_code(500);
    echo json_encode([
        'error' => 'Internal server error',
        'message' => $e->getMessage()
    ]);
}
?>
