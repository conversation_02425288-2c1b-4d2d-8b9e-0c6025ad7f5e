import 'package:shared_preferences/shared_preferences.dart';

/// Service pour gérer la configuration des paiements
class PaymentConfigService {
  static const String _useWebViewKey = 'use_webview_cinetpay';
  static const String _autoCloseKey = 'auto_close_webview';
  static const String _deepLinksKey = 'enable_deep_links';

  /// Vérifie si on doit utiliser WebView pour CinetPay
  static Future<bool> shouldUseWebViewForCinetPay() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_useWebViewKey) ?? true;
    } catch (e) {
      return true; // Par défaut, utiliser WebView
    }
  }

  /// Vérifie si on doit fermer automatiquement la WebView
  static Future<bool> shouldAutoCloseWebView() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_autoCloseKey) ?? true;
    } catch (e) {
      return true; // Par défaut, fermer automatiquement
    }
  }

  /// Vérifie si les deep links sont activés
  static Future<bool> areDeepLinksEnabled() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_deepLinksKey) ?? true;
    } catch (e) {
      return true; // Par défaut, activer les deep links
    }
  }

  /// Sauvegarde la préférence WebView pour CinetPay
  static Future<void> setUseWebViewForCinetPay(bool value) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_useWebViewKey, value);
    } catch (e) {
      print('Erreur lors de la sauvegarde de la préférence WebView: $e');
    }
  }

  /// Sauvegarde la préférence de fermeture automatique
  static Future<void> setAutoCloseWebView(bool value) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_autoCloseKey, value);
    } catch (e) {
      print('Erreur lors de la sauvegarde de la préférence auto-close: $e');
    }
  }

  /// Sauvegarde la préférence des deep links
  static Future<void> setDeepLinksEnabled(bool value) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_deepLinksKey, value);
    } catch (e) {
      print('Erreur lors de la sauvegarde de la préférence deep links: $e');
    }
  }

  /// Récupère toutes les configurations
  static Future<Map<String, bool>> getAllSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return {
        'useWebView': prefs.getBool(_useWebViewKey) ?? true,
        'autoClose': prefs.getBool(_autoCloseKey) ?? true,
        'deepLinks': prefs.getBool(_deepLinksKey) ?? true,
      };
    } catch (e) {
      return {
        'useWebView': true,
        'autoClose': true,
        'deepLinks': true,
      };
    }
  }

  /// Remet les paramètres par défaut
  static Future<void> resetToDefaults() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_useWebViewKey, true);
      await prefs.setBool(_autoCloseKey, true);
      await prefs.setBool(_deepLinksKey, true);
    } catch (e) {
      print('Erreur lors de la remise à zéro des paramètres: $e');
    }
  }
}
