import 'package:callitris/pages/auth_provider.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;

// Import des pages nécessaires (à adapter selon votre structure)
// import 'package:callitris/pages/auth_provider.dart';
// import 'package:callitris/pages/payment_success_page.dart';
// import 'package:callitris/pages/clients/code_verification_page.dart';

/// Gestionnaire spécialisé pour les deep links de paiement
class PaymentDeepLinkHandler {
  static final PaymentDeepLinkHandler _instance = PaymentDeepLinkHandler._internal();
  factory PaymentDeepLinkHandler() => _instance;
  PaymentDeepLinkHandler._internal();

  /// Gère les deep links de paiement et navigue vers la bonne page
  Future<void> handlePaymentDeepLink(
    BuildContext context,
    String status,
    Map<String, String> params,
  ) async {
    switch (status) {
      case 'success':
        await _handlePaymentSuccess(context, params);
        break;
      case 'failure':
        await _handlePaymentFailure(context, params);
        break;
      case 'pending':
        await _handlePaymentPending(context, params);
        break;
      default:
        _showErrorDialog(context, 'Statut de paiement non reconnu: $status');
    }
  }

  /// Gère le succès de paiement
  Future<void> _handlePaymentSuccess(BuildContext context, Map<String, String> params) async {
    final transactionId = params['transaction_id'];
    final commandId = params['command_id'];
    final clientId = params['client_id'];

    if (transactionId == null) {
      _showErrorDialog(context, 'ID de transaction manquant');
      return;
    }

    try {
      // Vérifier le statut du paiement côté serveur
      final paymentStatus = await _verifyPaymentStatus(context, transactionId);
      
      if (paymentStatus['success'] == true) {
        // Nettoyer les données de paiement temporaires
        await _clearTemporaryPaymentData();
        
        // Afficher le message de succès
        _showSuccessDialog(context, 'Paiement effectué avec succès!', () {
          // Navigation vers la page appropriée
          if (commandId != null && clientId != null) {
            _navigateToVerificationPage(context, {
              'clientId': clientId,
              'commandeId': commandId,
              'transactionId': transactionId,
            });
          } else {
            _navigateToPaymentSuccessPage(context);
          }
        });
      } else {
        _showErrorDialog(context, paymentStatus['message'] ?? 'Erreur lors de la vérification du paiement');
      }
    } catch (e) {
      print('Erreur lors de la vérification du paiement: $e');
      _showErrorDialog(context, 'Erreur lors de la vérification du paiement');
    }
  }

  /// Gère l'échec de paiement
  Future<void> _handlePaymentFailure(BuildContext context, Map<String, String> params) async {
    final transactionId = params['transaction_id'];
    final reason = params['reason'] ?? 'Raison non spécifiée';

    // Nettoyer les données de paiement temporaires
    await _clearTemporaryPaymentData();

    _showErrorDialog(context, 'Paiement échoué: $reason', () {
      // Retourner à la page de paiement
      Navigator.of(context).popUntil((route) => route.isFirst);
    });
  }

  /// Gère le paiement en attente
  Future<void> _handlePaymentPending(BuildContext context, Map<String, String> params) async {
    final transactionId = params['transaction_id'];

    _showInfoDialog(context, 'Paiement en cours de traitement...', 
      'Votre paiement est en cours de vérification. Vous recevrez une notification une fois le traitement terminé.');
  }

  /// Vérifie le statut du paiement côté serveur
  Future<Map<String, dynamic>> _verifyPaymentStatus(BuildContext context, String transactionId) async {
    try {
      // Récupérer le provider d'authentification
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final token = authProvider.token;

      if (token == null) {
        throw Exception('Token d\'authentification manquant');
      }

      final response = await http.post(
        Uri.parse(authProvider.getEndpoint('products/verify_payment_status.php')),
        headers: {
          'Authorization': token,
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'transaction_id': transactionId,
        }),
      );

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Erreur serveur: ${response.statusCode}');
      }
    } catch (e) {
      print('Erreur lors de la vérification du statut: $e');
      return {'success': false, 'message': 'Erreur de connexion'};
    }
  }

  /// Nettoie les données de paiement temporaires
  Future<void> _clearTemporaryPaymentData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('payment_code');
      await prefs.remove('boutique_payment_code');
      await prefs.remove('commande_id');
      await prefs.remove('client_id');
    } catch (e) {
      print('Erreur lors du nettoyage des données temporaires: $e');
    }
  }

  /// Affiche un dialog de succès
  void _showSuccessDialog(BuildContext context, String message, VoidCallback onOk) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(
                Icons.check_circle,
                color: Colors.green,
                size: 60,
              ),
              const SizedBox(height: 16),
              Text(
                'Succès',
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.green,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                message,
                textAlign: TextAlign.center,
                style: const TextStyle(fontSize: 16),
              ),
            ],
          ),
          actions: [
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                onOk();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
              ),
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }

  /// Affiche un dialog d'erreur
  void _showErrorDialog(BuildContext context, String message, [VoidCallback? onOk]) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(
                Icons.error,
                color: Colors.red,
                size: 60,
              ),
              const SizedBox(height: 16),
              const Text(
                'Erreur',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.red,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                message,
                textAlign: TextAlign.center,
                style: const TextStyle(fontSize: 16),
              ),
            ],
          ),
          actions: [
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                if (onOk != null) onOk();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }

  /// Affiche un dialog d'information
  void _showInfoDialog(BuildContext context, String title, String message) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(
                Icons.info,
                color: Colors.blue,
                size: 60,
              ),
              const SizedBox(height: 16),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                message,
                textAlign: TextAlign.center,
                style: const TextStyle(fontSize: 16),
              ),
            ],
          ),
          actions: [
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
              ),
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }

  /// Navigation vers la page de vérification de code
  void _navigateToVerificationPage(BuildContext context, Map<String, String> data) {
    // À adapter selon votre structure de navigation
    /*
    Navigator.pushReplacement(
      context,
      MaterialPageRoute(
        builder: (context) => CodeVerificationPage(
          clientId: data['clientId']!,
          commandeId: data['commandeId']!,
          transactionId: data['transactionId']!,
          clientData: {}, // À récupérer si nécessaire
        ),
      ),
    );
    */
    print('Navigation vers CodeVerificationPage avec: $data');
  }

  /// Navigation vers la page de succès de paiement
  void _navigateToPaymentSuccessPage(BuildContext context) {
    // À adapter selon votre structure de navigation
    /*
    Navigator.pushReplacement(
      context,
      MaterialPageRoute(
        builder: (context) => const PaymentSuccessPage(),
      ),
    );
    */
    print('Navigation vers PaymentSuccessPage');
  }
}
