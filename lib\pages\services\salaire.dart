import 'package:callitris/pages/auth_provider.dart';
import 'package:callitris/pages/menu.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

class SalairePage extends StatefulWidget {
  const SalairePage({super.key});

  @override
  _SalairePageState createState() => _SalairePageState();
}

class Salaire {
  final String boutique;
  final String tontine;
  final String brut;
  final String total;
  final String total_scan;
  final String total_no_scan;

  Salaire({
    required this.boutique,
    required this.tontine,
    required this.brut,
    required this.total,
    required this.total_scan,
    required this.total_no_scan,
  });
}

class Dash {
  final String total_versement;
  final String total_encaissement;
  final String dernier_passage;
  final String derniere_somme;
  final String total_reliquat;
  final String total_boutique;
  final String total_retrait;
  final String total_courant;
  final String total_pret;

  Dash({
    required this.total_versement,
    required this.total_encaissement,
    required this.dernier_passage,
    required this.derniere_somme,
    required this.total_reliquat,
    required this.total_boutique,
    required this.total_retrait,
    required this.total_courant,
    required this.total_pret,
  });
}

class _SalairePageState extends State<SalairePage> {
  Salaire? salaires;
  Dash? dash;

  @override
  void initState() {
    super.initState();
    fetchSalaires();
    fetchDashboard();
  }

  Future<void> fetchSalaires() async {
    try {
      final provide = Provider.of<AuthProvider>(context, listen: false);
      final user = Provider.of<AuthProvider>(context, listen: false).user;
      final token = Provider.of<AuthProvider>(context, listen: false).token;

      String idPersonnel = user!['id_personnel'].toString();

      final response = await http.get(
        Uri.parse(provide
            .getEndpoint('/products/getSalaire.php?personnel_id=$idPersonnel')),
        headers: {'Authorization': token!, 'Content-Type': 'application/json'},
      );
      //print(response.body);
      if (response.statusCode == 200) {
        final Map<String, dynamic> salaireData = jsonDecode(response.body);

        String boutique = salaireData['boutique'].toString();
        String tontine = salaireData['tontine'].toString();
        String brut = salaireData['brut'].toString();
        String total = salaireData['total'].toString();
        String total_scan = salaireData['total_scan'].toString();
        String total_no_scan = salaireData['total_no_scan'].toString();

        setState(() {
          salaires = Salaire(
            boutique: boutique,
            tontine: tontine,
            brut: brut,
            total: total,
            total_scan: total_scan,
            total_no_scan: total_no_scan,
          );
        });
      } else {
        print('Erreur : ${response.statusCode}');
      }
    } catch (error) {
      print('Erreur lors de la récupération des salaires: $error');
    }
  }

  Future<void> fetchDashboard() async {
    try {
      final provide = Provider.of<AuthProvider>(context, listen: false);
      final user = Provider.of<AuthProvider>(context, listen: false).user;
      final token = Provider.of<AuthProvider>(context, listen: false).token;

      String idPersonnel = user!['id_personnel'].toString();

      final response = await http.get(
        Uri.parse(provide
            .getEndpoint('client/getDash.php?personnel_id=$idPersonnel')),
        headers: {'Authorization': token!, 'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final dynamic responseData = jsonDecode(response.body);

        // Vérifier si c'est une liste ou un objet
        Map<String, dynamic> dashData;
        if (responseData is List && responseData.isNotEmpty) {
          dashData = responseData[0]; // Prendre le premier élément de la liste
        } else if (responseData is Map<String, dynamic>) {
          dashData = responseData;
        } else {
          print('Format de réponse inattendu: $responseData');
          return;
        }

        setState(() {
          dash = Dash(
            total_versement: dashData['total_versement']?.toString() ?? '0 F',
            total_encaissement:
                dashData['total_encaissement']?.toString() ?? '0 F',
            dernier_passage: dashData['dernier_passage']?.toString() ?? '',
            derniere_somme: dashData['derniere_somme']?.toString() ?? '0 F',
            total_reliquat: dashData['total_reliquat']?.toString() ?? '0 F',
            total_boutique: dashData['total_boutique']?.toString() ?? '0 F',
            total_retrait: dashData['total_retrait']?.toString() ?? '0 F',
            total_courant: dashData['total_courant']?.toString() ?? '0 F',
            total_pret: dashData['total_pret']?.toString() ?? '0 F',
          );
        });
      } else {
        print('Erreur dashboard : ${response.statusCode}');
      }
    } catch (error) {
      print('Erreur lors de la récupération du dashboard: $error');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: const Color.fromARGB(255, 249, 221, 175),
        title: const Text('Bilan Salaire'),
        actions: buildAppBarActions(context),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: SalaryCard(
                    title: 'Salaire Boutique ',
                    amount: '${salaires?.boutique}',
                    icon: Icons.shopping_bag,
                    gradient: [
                      Colors.blue.shade400,
                      const Color.fromARGB(255, 9, 29, 49)
                    ],
                  ),
                ),
                const SizedBox(width: 20),
                Expanded(
                  child: SalaryCard(
                    title: 'Tontine',
                    amount: '${salaires?.tontine}',
                    icon: Icons.account_balance_wallet,
                    gradient: [Colors.green.shade400, Colors.green.shade700],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: SalaryCard(
                    title: 'Salaire Boutique QR code',
                    amount: salaires?.total_scan ?? '0 F',
                    icon: Icons.qr_code_scanner,
                    gradient: [
                      const Color.fromARGB(255, 11, 88, 230),
                      const Color.fromARGB(255, 11, 88, 230)
                    ],
                  ),
                ),
                const SizedBox(width: 20),
                Expanded(
                  child: SalaryCard(
                    title: 'Salaire Boutique Espèce',
                    amount: salaires?.total_no_scan ?? '0 F',
                    icon: Icons.account_balance_wallet_outlined,
                    gradient: [
                      const Color.fromARGB(255, 64, 168, 232),
                      const Color.fromARGB(255, 186, 223, 246)
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: SalaryCard(
                    title: 'Salaire Net',
                    amount: '${salaires?.total}',
                    icon: Icons.attach_money,
                    gradient: [Colors.orange.shade400, Colors.orange.shade700],
                  ),
                ),
                const SizedBox(width: 20),
                Expanded(
                  child: SalaryCard(
                    title: 'Prêt',
                    amount: dash?.total_pret ?? '0 F',
                    icon: Icons.monetization_on,
                    gradient: [
                      const Color.fromARGB(255, 234, 6, 6),
                      const Color.fromARGB(255, 234, 6, 6)
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class SalaryCard extends StatelessWidget {
  final String title;
  final String amount;
  final IconData icon;
  final List<Color> gradient;

  const SalaryCard({
    super.key,
    required this.title,
    required this.amount,
    required this.icon,
    required this.gradient,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.grey.shade200,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(18),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: gradient,
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: gradient[0].withValues(alpha: 0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Icon(
                    icon,
                    size: 24,
                    color: Colors.white,
                  ),
                ),
                const Spacer(),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: gradient[0].withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    'FCFA',
                    style: TextStyle(
                      color: gradient[0],
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              title,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 6),
            Text(
              amount,
              style: TextStyle(
                fontSize: 22,
                fontWeight: FontWeight.bold,
                color: Colors.grey[800],
              ),
            ),
            const SizedBox(height: 8),
            Container(
              height: 4,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    gradient[0].withValues(alpha: 0.3),
                    gradient[0],
                  ],
                ),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
