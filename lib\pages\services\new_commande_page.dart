import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:provider/provider.dart';
import 'package:callitris/pages/auth_provider.dart';
import 'package:intl/intl.dart';

class NewCommandePage extends StatefulWidget {
  const NewCommandePage({super.key});

  @override
  State<NewCommandePage> createState() => _NewCommandePageState();
}

class _NewCommandePageState extends State<NewCommandePage> {
  final TextEditingController _searchController = TextEditingController();
  final TextEditingController _startDateController = TextEditingController();
  final TextEditingController _endDateController = TextEditingController();
  List<Map<String, dynamic>> _allCommandes = [];
  List<Map<String, dynamic>> _filteredCommandes = [];
  bool _isLoading = true;
  bool _disposed = false;
  DateTime? _selectedStartDate;
  DateTime? _selectedEndDate;

  @override
  void initState() {
    super.initState();
    _startDateController.text = _formatDateForDisplay(DateTime.now());
    _endDateController.text = _formatDateForDisplay(DateTime.now());
    print(_startDateController.text);
    print(_endDateController.text);
    _fetchNewCommandes();
    _searchController.addListener(_filterCommandes);
  }

  @override
  void dispose() {
    _disposed = true;
    _searchController.dispose();
    _startDateController.dispose();
    _endDateController.dispose();
    super.dispose();
  }

  Future<void> _fetchNewCommandes() async {
    try {
      final provide = Provider.of<AuthProvider>(context, listen: false);
      final user = Provider.of<AuthProvider>(context, listen: false).user;
      final token = Provider.of<AuthProvider>(context, listen: false).token;

      if (token == null || user == null) {
        if (mounted && !_disposed) {
          setState(() => _isLoading = false);
        }
        return;
      }

      String idPersonnel = user['id_personnel'].toString();

      final response = await http.get(
        Uri.parse(provide.getEndpoint(
            'products/getNewCommandes.php?personnel_id=$idPersonnel&debut=${_startDateController.text.toString()}&fin=${_endDateController.text.toString()}')),
        headers: {'Authorization': token, 'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        if (response.body.trim().isEmpty) {
          if (mounted && !_disposed) {
            setState(() {
              _allCommandes = [];
              _filteredCommandes = [];
              _isLoading = false;
            });
          }
          return;
        }

        try {
          final Map<String, dynamic> responseData = jsonDecode(response.body);

          if (responseData.containsKey('data') &&
              responseData['data'] is List) {
            final List<dynamic> commandesData = responseData['data'];
            if (mounted && !_disposed) {
              setState(() {
                _allCommandes = commandesData.cast<Map<String, dynamic>>();
                _filteredCommandes = List.from(_allCommandes);
                _isLoading = false;
              });
            }
          } else {
            if (mounted && !_disposed) {
              setState(() {
                _allCommandes = [];
                _filteredCommandes = [];
                _isLoading = false;
              });
            }
          }
        } catch (e) {
          if (mounted && !_disposed) {
            setState(() {
              _allCommandes = [];
              _filteredCommandes = [];
              _isLoading = false;
            });
          }
        }
      } else {
        if (mounted && !_disposed) {
          setState(() => _isLoading = false);
        }
      }
    } catch (error) {
      if (mounted && !_disposed) {
        setState(() => _isLoading = false);
      }
    }
  }

  void _filterCommandes() {
    String searchTerm = _searchController.text.toLowerCase();

    if (mounted && !_disposed) {
      setState(() {
        _filteredCommandes = _allCommandes.where((commande) {
          // Filtrage par texte de recherche
          bool matchesSearch = searchTerm.isEmpty ||
              commande['nom_client']
                      ?.toString()
                      .toLowerCase()
                      .contains(searchTerm) ==
                  true ||
              commande['prenom_client']
                      ?.toString()
                      .toLowerCase()
                      .contains(searchTerm) ==
                  true ||
              commande['code_client']
                      ?.toString()
                      .toLowerCase()
                      .contains(searchTerm) ==
                  true ||
              commande['journalier']?.toString().contains(searchTerm) == true ||
              commande['pack']?.toString().toLowerCase().contains(searchTerm) ==
                  true ||
              commande['livret']?.toString().contains(searchTerm) == true;

          bool matchesDate = true;
          if (_selectedStartDate != null || _selectedEndDate != null) {
            try {
              String dateString = commande['date_commande'] ?? '';
              DateTime commandeDate = DateTime.parse(dateString);

              if (_selectedStartDate != null && _selectedEndDate != null) {
                matchesDate = commandeDate.isAfter(_selectedStartDate!
                        .subtract(const Duration(days: 1))) &&
                    commandeDate.isBefore(
                        _selectedEndDate!.add(const Duration(days: 1)));
              } else if (_selectedStartDate != null) {
                matchesDate = commandeDate.isAfter(
                    _selectedStartDate!.subtract(const Duration(days: 1)));
              } else if (_selectedEndDate != null) {
                matchesDate = commandeDate
                    .isBefore(_selectedEndDate!.add(const Duration(days: 1)));
              }
            } catch (e) {
              matchesDate = false;
            }
          }

          return matchesSearch && matchesDate;
        }).toList();
      });
    }
  }

  String _formatDateForDisplay(DateTime date) {
    return DateFormat('yyyy/MM/dd').format(date);
  }

  Future<void> _selectDateRange() async {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Filtrer par période'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: _startDateController,
                decoration: InputDecoration(
                  labelText: 'Date de début',
                  hintText: 'JJ/MM/AAAA',
                  prefixIcon: GestureDetector(
                    onTap: () async {
                      final DateTime? picked = await showDatePicker(
                        context: context,
                        initialDate: _selectedStartDate ?? DateTime.now(),
                        firstDate: DateTime(2020),
                        lastDate: DateTime.now().add(const Duration(days: 365)),
                        locale: const Locale('fr', 'FR'),
                        helpText: 'Sélectionner la date de début',
                        cancelText: 'Annuler',
                        confirmText: 'Confirmer',
                      );
                      if (picked != null) {
                        if (mounted && !_disposed) {
                          setState(() {
                            _selectedStartDate = picked;
                            _startDateController.text =
                                _formatDateForDisplay(picked);
                          });
                        }
                      }
                    },
                    child: const Icon(Icons.calendar_today),
                  ),
                  border: const OutlineInputBorder(),
                ),
                readOnly: true,
              ),
              const SizedBox(height: 16),
              TextField(
                controller: _endDateController,
                decoration: InputDecoration(
                  labelText: 'Date de fin',
                  hintText: 'JJ/MM/AAAA',
                  prefixIcon: GestureDetector(
                    onTap: () async {
                      final DateTime? picked = await showDatePicker(
                        context: context,
                        initialDate: _selectedEndDate ?? DateTime.now(),
                        firstDate: DateTime(2020),
                        lastDate: DateTime.now().add(const Duration(days: 365)),
                        locale: const Locale('fr', 'FR'),
                        helpText: 'Sélectionner la date de fin',
                        cancelText: 'Annuler',
                        confirmText: 'Confirmer',
                      );
                      if (picked != null) {
                        if (mounted && !_disposed) {
                          setState(() {
                            _selectedEndDate = picked;
                            _endDateController.text =
                                _formatDateForDisplay(picked);
                          });
                        }
                      }
                    },
                    child: const Icon(Icons.calendar_today),
                  ),
                  border: const OutlineInputBorder(),
                ),
                readOnly: true,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('Annuler'),
            ),
            TextButton(
              onPressed: () {
                _fetchNewCommandes();
                Navigator.of(context).pop();
              },
              child: const Text('Appliquer'),
            ),
          ],
        );
      },
    );
  }

  void _clearDateFilter() {
    if (mounted && !_disposed) {
      setState(() {
        _selectedStartDate = null;
        _selectedEndDate = null;
        _startDateController.clear();
        _endDateController.clear();
      });
    }
    _filterCommandes();
  }

  String _formatDate(String? dateString) {
    if (dateString == null) return 'N/A';
    try {
      DateTime date = DateTime.parse(dateString);
      return DateFormat('dd/MM/yyyy').format(date);
    } catch (e) {
      return 'N/A';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          'Nouvelles Commandes',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color.fromARGB(255, 246, 196, 88),
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              if (mounted && !_disposed) {
                setState(() => _isLoading = true);
              }
              _fetchNewCommandes();
            },
          ),
        ],
      ),
      body: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(20.0),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  spreadRadius: 0,
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              children: [
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: const Color.fromARGB(255, 246, 196, 88)
                          .withValues(alpha: 0.3),
                      width: 2,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: const Color.fromARGB(255, 246, 196, 88)
                            .withValues(alpha: 0.1),
                        spreadRadius: 0,
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      labelText: 'Rechercher une commande...',
                      hintText: 'Nom client, code, montant, pack...',
                      prefixIcon: Container(
                        padding: const EdgeInsets.all(12),
                        child: Icon(
                          Icons.search_rounded,
                          color: const Color.fromARGB(255, 246, 196, 88),
                          size: 24,
                        ),
                      ),
                      suffixIcon: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          if (_searchController.text.isNotEmpty)
                            IconButton(
                              icon: Icon(
                                Icons.clear_rounded,
                                color: Colors.grey.shade600,
                                size: 20,
                              ),
                              onPressed: () {
                                _searchController.clear();
                                _fetchNewCommandes();
                              },
                              tooltip: 'Effacer la recherche',
                            ),
                          Container(
                            margin: const EdgeInsets.only(right: 8),
                            decoration: BoxDecoration(
                              color: (_selectedStartDate != null &&
                                      _selectedEndDate != null)
                                  ? const Color.fromARGB(255, 246, 196, 88)
                                  : Colors.grey.shade100,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: IconButton(
                              icon: Icon(
                                Icons.date_range_rounded,
                                color: (_selectedStartDate != null &&
                                        _selectedEndDate != null)
                                    ? Colors.white
                                    : Colors.grey.shade600,
                                size: 20,
                              ),
                              onPressed: _selectDateRange,
                              tooltip: 'Filtrer par période',
                            ),
                          ),
                        ],
                      ),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(16),
                        borderSide: BorderSide.none,
                      ),
                      filled: true,
                      fillColor: Colors.grey.shade50,
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 20,
                        vertical: 16,
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    if (_selectedStartDate != null && _selectedEndDate != null)
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 8,
                        ),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              const Color.fromARGB(255, 246, 196, 88),
                              const Color.fromARGB(255, 246, 196, 88)
                                  .withValues(alpha: 0.8),
                            ],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                          borderRadius: BorderRadius.circular(20),
                          boxShadow: [
                            BoxShadow(
                              color: const Color.fromARGB(255, 246, 196, 88)
                                  .withValues(alpha: 0.3),
                              spreadRadius: 0,
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Icon(
                              Icons.date_range_rounded,
                              color: Colors.white,
                              size: 16,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              '${DateFormat('dd/MM').format(_selectedStartDate!)} - ${DateFormat('dd/MM').format(_selectedEndDate!)}',
                              style: const TextStyle(
                                fontSize: 12,
                                color: Colors.white,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            const SizedBox(width: 8),
                            GestureDetector(
                              onTap: _clearDateFilter,
                              child: Container(
                                padding: const EdgeInsets.all(2),
                                decoration: BoxDecoration(
                                  color: Colors.white.withValues(alpha: 0.2),
                                  borderRadius: BorderRadius.circular(10),
                                ),
                                child: const Icon(
                                  Icons.close_rounded,
                                  color: Colors.white,
                                  size: 14,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    const Spacer(),
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade100,
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.list_alt_rounded,
                            size: 16,
                            color: Colors.grey.shade600,
                          ),
                          const SizedBox(width: 6),
                          Text(
                            '${_filteredCommandes.length} résultat${_filteredCommandes.length > 1 ? 's' : ''}',
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                              color: Colors.grey.shade700,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          // Liste des commandes
          Expanded(
            child: _isLoading
                ? const Center(
                    child: CircularProgressIndicator(
                      color: Color(0xFF3B82F6),
                    ),
                  )
                : _filteredCommandes.isEmpty
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.shopping_cart_outlined,
                              size: 80,
                              color: Colors.grey[400],
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'Aucune commande trouvée',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.w500,
                                color: Colors.grey[600],
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Essayez de modifier vos critères de recherche',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey[500],
                              ),
                            ),
                          ],
                        ),
                      )
                    : ListView.builder(
                        padding: const EdgeInsets.symmetric(horizontal: 20),
                        itemCount: _filteredCommandes.length,
                        itemBuilder: (context, index) {
                          final commande = _filteredCommandes[index];
                          return _buildCommandeCard(commande);
                        },
                      ),
          ),
        ],
      ),
    );
  }

  Widget _buildCommandeCard(Map<String, dynamic> commande) {
    String typeCompte = commande['nom_compte'] ?? 'BOUTIQUE';
    Color accountColor = typeCompte == 'BOUTIQUE'
        ? const Color(0xFF2563EB)
        : typeCompte == 'TONTINE'
            ? const Color(0xFF7C3AED)
            : const Color(0xFF059669);

    String montantJournalier = commande['journalier']?.toString() ?? '0';
    String nombreJours = commande['nombre_jours']?.toString() ?? '0';
    int montantTotal = 0;
    try {
      montantTotal = int.parse(montantJournalier) * int.parse(nombreJours);
    } catch (e) {
      montantTotal = int.tryParse(montantJournalier) ?? 0;
    }

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 6.0, horizontal: 16.0),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16.0),
        color: Colors.white,
        border:
            Border.all(color: accountColor.withValues(alpha: 0.3), width: 1),
        boxShadow: [
          BoxShadow(
            color: accountColor.withValues(alpha: 0.1),
            spreadRadius: 0,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Header avec fond coloré
          Container(
            padding: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
              color: accountColor.withValues(alpha: 0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16.0),
                topRight: Radius.circular(16.0),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: accountColor,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    typeCompte == 'BOUTIQUE'
                        ? Icons.store_rounded
                        : typeCompte == 'TONTINE'
                            ? Icons.group_rounded
                            : Icons.shopping_cart_rounded,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        typeCompte,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: accountColor,
                        ),
                      ),
                      Text(
                        '${commande['nom_client'] ?? ''} ${commande['prenom_client'] ?? ''}'
                            .trim(),
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade700,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.green,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    '${commande['journalier'] ?? '0'} F',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Contenu principal
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                // Code client
                if (commande['code_client'] != null) ...[
                  Row(
                    children: [
                      Icon(Icons.badge_rounded,
                          size: 16, color: Colors.grey.shade600),
                      const SizedBox(width: 8),
                      Text(
                        'Code: ${commande['code_client']}',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade700,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                ],

                // Détails selon le type de compte
                if (typeCompte == 'BOUTIQUE') ...[
                  _buildInfoRow(Icons.inventory_2_rounded, 'Pack',
                      commande['pack'] ?? 'N/A'),
                  const SizedBox(height: 8),
                  _buildInfoRow(Icons.book_rounded, 'Carnet',
                      commande['livret'] ?? 'N/A'),
                ] else if (typeCompte == 'TONTINE') ...[
                  _buildInfoRow(Icons.access_time_rounded, 'Nombre de jours',
                      '${commande['nombre_jours'] ?? '0'} jours'),
                  const SizedBox(height: 8),
                  _buildInfoRow(Icons.label_rounded, 'Libellé',
                      commande['libelle'] ?? 'N/A'),
                ],

                // Montant total pour TONTINE
                if (typeCompte == 'TONTINE' && montantTotal > 0) ...[
                  const SizedBox(height: 8),
                  _buildInfoRow(Icons.calculate_rounded, 'Total',
                      '${NumberFormat('#,###').format(montantTotal)} F'),
                ],

                const SizedBox(height: 12),

                // Date et heure
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade50,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.calendar_month_rounded,
                              size: 16, color: Colors.grey.shade600),
                          const SizedBox(width: 6),
                          Text(
                            _formatDate(commande['date_commande']),
                            style: TextStyle(
                              fontSize: 13,
                              color: Colors.grey.shade700,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Row(
      children: [
        Icon(icon, size: 16, color: Colors.grey.shade600),
        const SizedBox(width: 8),
        Text(
          '$label: ',
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey.shade600,
            fontWeight: FontWeight.w500,
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade700,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }
}
