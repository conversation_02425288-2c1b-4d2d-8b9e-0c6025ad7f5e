import 'dart:convert';

import 'package:callitris/pages/auth_provider.dart';
import 'package:callitris/pages/clients/payment_method_page.dart';
import 'package:callitris/pages/clients/vers_boutique.dart';
import 'package:callitris/pages/services/scanner_encaissement.dart';
import 'package:callitris/widget/getCurrenteLocalisation.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:provider/provider.dart';

import '../menu.dart';

class ClientPaymentPage extends StatefulWidget {
  final Map<String, dynamic> clientData;
  final String id;

  const ClientPaymentPage({
    super.key,
    required this.clientData,
    required this.id,
    
  });

  @override
  State<ClientPaymentPage> createState() => _ClientPaymentPageState();
}

class _ClientPaymentPageState extends State<ClientPaymentPage> {
  bool _isLoading = false;
  List<Versement> versements = [];
  List<dynamic> ids = [];
  String? cle;
  String? client_id;

  @override
  void initState() {
    super.initState();
    fetchVersements();
  }

  Future<void> _sendValidationRequest() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final provide = Provider.of<AuthProvider>(context, listen: false);
      final token = provide.token;

      Map<String, dynamic>? position = await getPositionAndAddress();

      if (position == null) {
        showSuccessDialog(
            context, "Opps, veuiller activer votre localisation et réessayer.");
        return;
      }

      final response = await http.post(
        Uri.parse(provide.getEndpoint('products/ValidatePayments.php')),
        headers: {
          'Authorization': token!,
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'personnel_id': provide.user?['id_personnel'].toString(),
          'ids': ids,
          'latitude': position['latitude'],
          'longitude': position['longitude'],
          'rue': position['rue'],
          'ville': position['ville'],
          'region': position['region'],
          'pays': position['pays'],
          'codePostal': position['codePostal'],
        }),
      );
      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
       if (responseData['code'] == 200) {
          _validateOk(responseData['message']);
        } else {
          showSuccessDialog(context, responseData['message']);
        }
      } else {
        showSuccessDialog(context, "Erreur : ${response.reasonPhrase}");
      }
    } catch (error) {
      showSuccessDialog(context, "Erreur : $error");
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> fetchCommandeInfo() async {
    try {
      final provide = Provider.of<AuthProvider>(context, listen: false);
      final token = Provider.of<AuthProvider>(context, listen: false).token;

      final response = await http.get(
        Uri.parse(provide.getEndpoint(
            'products/getCommandeById.php?commandeId=${widget.id}')),
        headers: {'Authorization': token!, 'Content-Type': 'application/json'},
      );
      //print(response.body);
      if (response.statusCode == 200) {
        final List<dynamic> responseData = jsonDecode(response.body);

        if (responseData.isNotEmpty) {
          final commandeData = responseData[0];

          setState(() {
            cle = commandeData['cle'].toString();
            client_id = commandeData['client_id'].toString();
          });
        }
      } else {
        print('Erreur : ${response.statusCode}');
      }
    } catch (error) {
      print(
          'Erreur lors de la récupération des informations de la commande : $error');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Paiement Client'),
        backgroundColor: const Color.fromARGB(255, 249, 221, 175),
        actions: buildAppBarActions(context),
      ),
      backgroundColor: Colors.grey[50],
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildClientHeader(),
              const SizedBox(height: 24),
              _buildPaymentSection(),
              const SizedBox(height: 24),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> fetchVersements() async {
    try {
      final provide = Provider.of<AuthProvider>(context, listen: false);
      final token = provide.token;

      final response = await http.get(
        Uri.parse(provide.getEndpoint(
            'products/getClientVersement.php?code_client=${widget.clientData['code_client']}&command_id=${widget.id}')),
        headers: {'Authorization': token!, 'Content-Type': 'application/json'},
      );

      print(response.body);

      if (response.statusCode == 200) {
        final dynamic jsonResponse = jsonDecode(response.body);

        final List<dynamic> responseData =
            jsonResponse is List ? jsonResponse : [jsonResponse];

        List<Versement> fetchedVersements = responseData.map((versementData) {
          String idVersement = versementData['id_versement'].toString();
          String journalier = versementData['journalier'].toString();
          String verser = versementData['montant_vers'].toString();
          String reelVers = versementData['montant_saisi'].toString();
          String monnaieReste = versementData['montant_reste'].toString();
          String monnaieVers = versementData['monnaie'].toString();
          String date = versementData['date_vers'].toString();
          String heure = versementData['heure_vers'].toString();

          return Versement(
            id_versement: idVersement,
            journalier: journalier,
            verser: verser,
            reelVers: reelVers,
            monnaieReste: monnaieReste,
            monnaieVers: monnaieVers,
            date: date,
            heure: heure,
          );
        }).toList();

        setState(() {
          versements = fetchedVersements;
        });
        fetchCommandeInfo();
        for (var payment in versements) {
          if (!ids.contains(payment.id_versement)) {
            ids.add(payment.id_versement);
          }
        }
      } else {
        print('Erreur : ${response.statusCode}');
      }
    } catch (error) {
      print(
          'Erreur lors de la récupération de l\'historique des versements: $error');
    }
  }

  void showSuccessDialog(BuildContext context, String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: const Column(
            children: [
              Icon(
                Icons.check_circle,
                color: Color.fromARGB(255, 189, 17, 17),
                size: 60,
              ),
              SizedBox(height: 16),
              Text(
                "Erreur",
                style: TextStyle(fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
              ),
            ],
          ),
          content: Text(
            message,
            textAlign: TextAlign.center,
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) =>
                            ScannerEncaissement(id: widget.id)));
              },
              child: const Text(
                "OK",
                style: TextStyle(fontSize: 16),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildClientHeader() {
    final String nomComplet =
        '${widget.clientData['nom'] ?? ''} ${widget.clientData['prenom'] ?? ''}'
            .trim();
    final String telephone = widget.clientData['telephone'] ?? '';
    final String code = widget.clientData['code_client'] ?? '';
    final String type = widget.clientData['type'] ?? '';

    return Container(
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.teal.shade50,
            Colors.cyan.shade50,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.teal.shade100),
        boxShadow: [
          BoxShadow(
            color: Colors.teal.withValues(alpha: 0.08),
            blurRadius: 12,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Colors.teal.shade300,
                      Colors.cyan.shade400,
                    ],
                  ),
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.teal.withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 3),
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.person,
                  color: Colors.white,
                  size: 28,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      nomComplet.isNotEmpty ? nomComplet : 'Client',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                        color: Colors.teal.shade800,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      type.isNotEmpty ? 'Type: $type' : 'Type: Inconnu',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.teal.shade600,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          if (telephone.isNotEmpty)
            _buildInfoRow(Icons.phone, 'Téléphone', telephone),
          if (code.isNotEmpty)
            _buildInfoRow(Icons.horizontal_split_sharp, 'Code', code),
        ],
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(
            icon,
            size: 18,
            color: Colors.teal.shade600,
          ),
          const SizedBox(width: 12),
          Text(
            '$label: ',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Colors.grey.shade700,
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black87,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.payment,
                color: Colors.orange.shade600,
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Paiements effectués',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey.shade800,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : versements.isEmpty
                  ? Center(
                      child: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 20),
                        child: Column(
                          children: [
                            Icon(Icons.receipt_long_outlined,
                                size: 48, color: Colors.grey.shade400),
                            const SizedBox(height: 12),
                            Text(
                              'Aucun paiement trouvé',
                              style: TextStyle(color: Colors.grey.shade600),
                            ),
                          ],
                        ),
                      ),
                    )
                  : SizedBox(
                      height: 300,
                      child: ListView.builder(
                        shrinkWrap: true,
                        itemCount: versements.length,
                        itemBuilder: (context, index) {
                          final payment = versements[index];
                          return _buildPaymentItem(payment);
                        },
                      ),
                    ),
          const SizedBox(height: 20),
          (versements.isNotEmpty)
              ? SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: ids.isEmpty ? null : () => _validatePayments(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.teal,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 15),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      elevation: 2,
                    ),
                    child: const Text(
                      'Valider',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                )
              : SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () {
                      _showPaymentMethodDialog();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.teal,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 15),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      elevation: 2,
                    ),
                    child: const Text(
                      'Effectuer un paiement',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
        ],
      ),
    );
  }

  Widget _buildPaymentItem(Versement payment) {
    final String montantVers = payment.verser;
    final String montantPayer = payment.reelVers;
    final String montantReste = payment.monnaieReste;
    final String date = payment.date;
    final String heure = payment.heure;
    final String montantSaisi = payment.journalier;

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: Colors.grey.shade200),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: Colors.orange.shade50,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.receipt,
                    color: Colors.orange.shade400,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '$montantVers FCFA',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Le $date à $heure',
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Divider(color: Colors.grey.shade200),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _buildPaymentDetail('Journalier', montantSaisi),
                _buildPaymentDetail('Montant payé', montantPayer),
                _buildPaymentDetail('Reste', montantReste),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentDetail(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          '$value FCFA',
          style: const TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 14,
          ),
        ),
      ],
    );
  }

  void _validatePayments() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: const Column(
            children: [
              Icon(
                Icons.check_circle,
                color: Colors.green,
                size: 60,
              ),
              SizedBox(height: 16),
              Text(
                "Confirmation",
                style: TextStyle(fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
              ),
            ],
          ),
          content: const Text(
            "Voulez-vous valider tous les paiements pour cette commande?",
            textAlign: TextAlign.center,
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text(
                "Annuler",
                style: TextStyle(color: Colors.grey),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                _sendValidationRequest();
                Navigator.of(context).pop();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
              ),
              child: const Text(
                "Confirmer",
                style: TextStyle(fontSize: 16),
              ),
            ),
          ],
        );
      },
    );
  }

  void _validateOk(String message) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: const Column(
            children: [
              Icon(
                Icons.check_circle,
                color: Colors.green,
                size: 60,
              ),
              SizedBox(height: 16),
              Text(
                "Validation réussie",
                style: TextStyle(fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
              ),
            ],
          ),
          content: Text(
            message,
            textAlign: TextAlign.center,
          ),
          actions: [
            ElevatedButton(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => VersementPage(
                      id: widget.id,
                      cle: cle!,
                      client: client_id!,
                    ),
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
              ),
              child: const Text(
                "OK",
                style: TextStyle(fontSize: 16),
              ),
            ),
          ],
        );
      },
    );
  }

  void _showPaymentMethodDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: const Text(
            'Choisir un moyen de paiement',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 18,
            ),
            textAlign: TextAlign.center,
          ),
          content: SizedBox(
            width: double.maxFinite,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildPaymentMethodOption(
                  'Orange Money',
                  'assets/orange_money.png',
                  Colors.orange,
                  'orange_money',
                ),
                const SizedBox(height: 12),
                _buildPaymentMethodOption(
                  'MTN MoMo',
                  'assets/mtn_momo.png',
                  Colors.yellow.shade700,
                  'mtn_momo',
                ),
                const SizedBox(height: 12),
                _buildPaymentMethodOption(
                  'MOOV Money',
                  'assets/moov_money.png',
                  Colors.blue,
                  'moov_money',
                ),
                const SizedBox(height: 12),
                // _buildPaymentMethodOption(
                //   'WAVE CI',
                //   'assets/wave_ci.png',
                //   const Color.fromARGB(255, 49, 97, 238),
                //   'wave_ci',
                // ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text(
                'Annuler',
                style: TextStyle(color: Colors.grey),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildPaymentMethodOption(
      String name, dynamic iconOrAsset, Color color, String paymentMethod) {
    return InkWell(
      onTap: () {
        Navigator.of(context).pop();
        _navigateToPaymentPage(paymentMethod);
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(12),
          color: Colors.grey.shade50,
        ),
        child: Row(
          children: [
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(25),
              ),
              child: iconOrAsset is IconData
                  ? Icon(
                      iconOrAsset,
                      color: color,
                      size: 28,
                    )
                  : Image.asset(
                      iconOrAsset,
                      width: 28,
                      height: 28,
                    ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Text(
                name,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: Colors.grey.shade400,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToPaymentPage(String paymentMethod) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PaymentMethodPage(
          paymentMethod: paymentMethod,
          clientId: client_id!,
          commandeId: widget.id,
          clientData: widget.clientData,
        ),
      ),
    );
  }
}

