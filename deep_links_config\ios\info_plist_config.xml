<!-- Configuration à ajouter dans ios/Runner/Info.plist -->

<!-- Ajouter cette configuration dans le dictionnaire principal -->
<key>CFBundleURLTypes</key>
<array>
    <!-- Deep Links pour le schéma callitris:// -->
    <dict>
        <key>CFBundleURLName</key>
        <string>callitris.deeplink</string>
        <key>CFBundleURLSchemes</key>
        <array>
            <string>callitris</string>
        </array>
        <key>CFBundleURLTypes</key>
        <string>Editor</string>
    </dict>
    
    <!-- Deep Links pour les paiements -->
    <dict>
        <key>CFBundleURLName</key>
        <string>callitris.payment</string>
        <key>CFBundleURLSchemes</key>
        <array>
            <string>callitris</string>
        </array>
        <key>CFBundleURLTypes</key>
        <string>Editor</string>
    </dict>
    
    <!-- Support pour les liens HTTPS du domaine -->
    <dict>
        <key>CFBundleURLName</key>
        <string>callitris.https</string>
        <key>CFBundleURLSchemes</key>
        <array>
            <string>https</string>
        </array>
        <key>CFBundleURLTypes</key>
        <string>Editor</string>
    </dict>
</array>

<!-- Associated Domains pour les liens universels -->
<key>com.apple.developer.associated-domains</key>
<array>
    <string>applinks:dev-mani.io</string>
</array>

<!-- Configuration pour les liens universels -->
<key>NSAppTransportSecurity</key>
<dict>
    <key>NSAllowsArbitraryLoads</key>
    <true/>
    <key>NSExceptionDomains</key>
    <dict>
        <key>dev-mani.io</key>
        <dict>
            <key>NSExceptionAllowsInsecureHTTPLoads</key>
            <false/>
            <key>NSExceptionMinimumTLSVersion</key>
            <string>TLSv1.2</string>
            <key>NSIncludesSubdomains</key>
            <true/>
        </dict>
    </dict>
</dict>

<!-- Permissions pour les URL schemes -->
<key>LSApplicationQueriesSchemes</key>
<array>
    <string>callitris</string>
    <string>https</string>
    <string>http</string>
</array>

<!-- Configuration pour le handling des deep links -->
<key>UIApplicationSupportsIndirectInputEvents</key>
<true/>

<!-- Support pour les liens entrants -->
<key>CFBundleDocumentTypes</key>
<array>
    <dict>
        <key>CFBundleTypeName</key>
        <string>Callitris Payment Link</string>
        <key>CFBundleTypeRole</key>
        <string>Viewer</string>
        <key>LSHandlerRank</key>
        <string>Owner</string>
        <key>LSItemContentTypes</key>
        <array>
            <string>public.url</string>
        </array>
    </dict>
</array>
