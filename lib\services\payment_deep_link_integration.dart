import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../pages/auth_provider.dart';
import '../config/deep_link_config.dart';
import 'deep_link_manager.dart';

/// Classe utilitaire pour intégrer les deep links dans les pages de paiement existantes
class PaymentDeepLinkIntegration {
  /// Modifie les URLs de paiement pour inclure les paramètres de deep link
  static String enhancePaymentUrl(
    String originalUrl, {
    String? commandId,
    String? clientId,
    String? transactionId,
  }) {
    final uri = Uri.parse(originalUrl);
    final queryParams = Map<String, String>.from(uri.queryParameters);

    // Ajouter les paramètres pour le deep link de retour
    if (commandId != null) {
      queryParams['command_id'] = commandId;
    }
    if (clientId != null) {
      queryParams['client_id'] = clientId;
    }
    if (transactionId != null) {
      queryParams['transaction_id'] = transactionId;
    }

    // Ajouter les URLs de callback
    queryParams['success_url'] =
        _generateCallbackUrl('success', commandId, clientId, transactionId);
    queryParams['failure_url'] =
        _generateCallbackUrl('failure', commandId, clientId, transactionId);
    queryParams['pending_url'] =
        _generateCallbackUrl('pending', commandId, clientId, transactionId);

    return uri.replace(queryParameters: queryParams).toString();
  }

  /// Génère les URLs de callback pour les différents statuts
  static String _generateCallbackUrl(String status, String? commandId,
      String? clientId, String? transactionId) {
    return DeepLinkConfig.generateGenericCallbackUrl(
      provider: 'generic',
      status: status,
      commandId: commandId,
      clientId: clientId,
      transactionId: transactionId,
    );
  }

  /// Modifie les données de requête pour Wave
  static Map<String, dynamic> enhanceWavePaymentData(
    Map<String, dynamic> originalData, {
    String? commandId,
    String? clientId,
  }) {
    final enhancedData = Map<String, dynamic>.from(originalData);

    // Ajouter les URLs de callback spécifiques à Wave
    enhancedData['success_url'] = DeepLinkConfig.generateWaveCallbackUrl(
      status: 'success',
      commandId: commandId,
      clientId: clientId,
    );
    enhancedData['failure_url'] = DeepLinkConfig.generateWaveCallbackUrl(
      status: 'failure',
      commandId: commandId,
      clientId: clientId,
    );
    enhancedData['pending_url'] = DeepLinkConfig.generateWaveCallbackUrl(
      status: 'pending',
      commandId: commandId,
      clientId: clientId,
    );

    // Ajouter les informations dans client_reference pour Wave
    if (commandId != null || clientId != null) {
      final clientReference = {
        if (commandId != null) 'command_id': commandId,
        if (clientId != null) 'client_id': clientId,
      };
      enhancedData['client_reference'] = clientReference;
    }

    return enhancedData;
  }

  /// Modifie les données de requête pour CinetPay
  static Map<String, dynamic> enhanceCinetPayPaymentData(
    Map<String, dynamic> originalData, {
    String? commandId,
    String? clientId,
  }) {
    final enhancedData = Map<String, dynamic>.from(originalData);

    // Ajouter les URLs de callback spécifiques à CinetPay
    enhancedData['notify_url'] = DeepLinkConfig.cinetPayCallbackUrl;
    enhancedData['return_url'] = DeepLinkConfig.generateCinetPayCallbackUrl(
      status: 'success',
      commandId: commandId,
      clientId: clientId,
    );
    enhancedData['cancel_url'] = DeepLinkConfig.generateCinetPayCallbackUrl(
      status: 'failure',
      commandId: commandId,
      clientId: clientId,
    );

    // Ajouter les informations dans cpm_custom pour CinetPay
    if (commandId != null || clientId != null) {
      final customData = {
        if (commandId != null) 'command_id': commandId,
        if (clientId != null) 'client_id': clientId,
      };
      enhancedData['cpm_custom'] = customData;
    }

    return enhancedData;
  }

  /// Génère un deep link de test
  static String generateTestDeepLink({
    required String status,
    String? transactionId,
    String? commandId,
    String? clientId,
  }) {
    return DeepLinkManager.generatePaymentDeepLink(
      status: status,
      transactionId:
          transactionId ?? 'test_${DateTime.now().millisecondsSinceEpoch}',
      commandId: commandId,
      clientId: clientId,
    );
  }

  /// Fonction utilitaire pour tester les deep links
  static void testDeepLink(
    BuildContext context, {
    required String status,
    String? transactionId,
    String? commandId,
    String? clientId,
  }) {
    final deepLink = generateTestDeepLink(
      status: status,
      transactionId: transactionId,
      commandId: commandId,
      clientId: clientId,
    );

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Test Deep Link'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Deep Link généré:'),
              const SizedBox(height: 8),
              SelectableText(
                deepLink,
                style: const TextStyle(
                  fontFamily: 'monospace',
                  fontSize: 12,
                ),
              ),
              const SizedBox(height: 16),
              Text('Statut: $status'),
              if (transactionId != null) Text('Transaction ID: $transactionId'),
              if (commandId != null) Text('Commande ID: $commandId'),
              if (clientId != null) Text('Client ID: $clientId'),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Fermer'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                // Simuler l'ouverture du deep link
                final deepLinkManager = DeepLinkManager();
                final uri = Uri.parse(deepLink);
                // Ici on pourrait appeler directement la méthode de traitement
                print('Test deep link: $deepLink');
              },
              child: const Text('Tester'),
            ),
          ],
        );
      },
    );
  }

  /// Widget pour afficher un bouton de test des deep links (pour le développement)
  static Widget buildTestButton(BuildContext context) {
    return PopupMenuButton<String>(
      icon: const Icon(Icons.link),
      tooltip: 'Tester Deep Links',
      onSelected: (String value) {
        switch (value) {
          case 'success':
            testDeepLink(
              context,
              status: 'success',
              transactionId:
                  'test_success_${DateTime.now().millisecondsSinceEpoch}',
              commandId: 'cmd_123',
              clientId: 'client_456',
            );
            break;
          case 'failure':
            testDeepLink(
              context,
              status: 'failure',
              transactionId:
                  'test_failure_${DateTime.now().millisecondsSinceEpoch}',
              commandId: 'cmd_123',
              clientId: 'client_456',
            );
            break;
          case 'pending':
            testDeepLink(
              context,
              status: 'pending',
              transactionId:
                  'test_pending_${DateTime.now().millisecondsSinceEpoch}',
              commandId: 'cmd_123',
              clientId: 'client_456',
            );
            break;
        }
      },
      itemBuilder: (BuildContext context) => <PopupMenuEntry<String>>[
        const PopupMenuItem<String>(
          value: 'success',
          child: ListTile(
            leading: Icon(Icons.check_circle, color: Colors.green),
            title: Text('Test Succès'),
          ),
        ),
        const PopupMenuItem<String>(
          value: 'failure',
          child: ListTile(
            leading: Icon(Icons.error, color: Colors.red),
            title: Text('Test Échec'),
          ),
        ),
        const PopupMenuItem<String>(
          value: 'pending',
          child: ListTile(
            leading: Icon(Icons.hourglass_empty, color: Colors.orange),
            title: Text('Test En Attente'),
          ),
        ),
      ],
    );
  }

  /// Fonction pour vérifier si l'app supporte les deep links
  static Future<bool> checkDeepLinkSupport() async {
    try {
      // Ici on pourrait vérifier si app_links est correctement configuré
      return true;
    } catch (e) {
      print('Erreur lors de la vérification du support des deep links: $e');
      return false;
    }
  }

  /// Fonction pour obtenir les informations de deep link depuis les SharedPreferences
  static Future<Map<String, dynamic>?> getLastPaymentResult() async {
    final deepLinkManager = DeepLinkManager();
    // Cette méthode devrait être exposée dans DeepLinkManager
    // return await deepLinkManager.getLastPaymentResult();
    return null;
  }
}
