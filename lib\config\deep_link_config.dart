/// Configuration des deep links pour l'application Callitris
class DeepLinkConfig {
  // Schéma de l'application
  static const String appScheme = 'callitris';

  // Hosts pour les deep links
  static const String paymentHost = 'payment';
  static const String callbackHost = 'callback';

  // Paths pour les statuts de paiement
  static const String successPath = '/success';
  static const String failurePath = '/failure';
  static const String pendingPath = '/pending';

  // URLs de base du backend
  static const String baseBackendUrl = 'https://api.callitris-distribution.com';
  static const String deepLinksPath = '/deep_links_config/backend';

  // URLs complètes des callbacks
  static String get waveCallbackUrl =>
      '$baseBackendUrl$deepLinksPath/wave_callback.php';
  static String get cinetPayCallbackUrl =>
      '$baseBackendUrl$deepLinksPath/cinetpay_callback.php';
  static String get genericCallbackUrl =>
      '$baseBackendUrl$deepLinksPath/payment_redirect.php';

  // Génère une URL de callback pour Wave
  static String generateWaveCallbackUrl({
    required String status,
    String? commandId,
    String? clientId,
    String? transactionId,
  }) {
    final params = <String, String>{
      'status': status,
    };

    if (commandId != null) params['command_id'] = commandId;
    if (clientId != null) params['client_id'] = clientId;
    if (transactionId != null) params['transaction_id'] = transactionId;

    final uri = Uri.parse(waveCallbackUrl).replace(queryParameters: params);
    return uri.toString();
  }

  // Génère une URL de callback pour CinetPay
  static String generateCinetPayCallbackUrl({
    required String status,
    String? commandId,
    String? clientId,
    String? transactionId,
  }) {
    final params = <String, String>{
      'status': status,
    };

    if (commandId != null) params['command_id'] = commandId;
    if (clientId != null) params['client_id'] = clientId;
    if (transactionId != null) params['transaction_id'] = transactionId;

    final uri = Uri.parse(cinetPayCallbackUrl).replace(queryParameters: params);
    return uri.toString();
  }

  // Génère une URL de callback générique
  static String generateGenericCallbackUrl({
    required String provider,
    required String status,
    String? commandId,
    String? clientId,
    String? transactionId,
  }) {
    final params = <String, String>{
      'provider': provider,
      'status': status,
    };

    if (commandId != null) params['command_id'] = commandId;
    if (clientId != null) params['client_id'] = clientId;
    if (transactionId != null) params['transaction_id'] = transactionId;

    final uri = Uri.parse(genericCallbackUrl).replace(queryParameters: params);
    return uri.toString();
  }

  // Génère un deep link complet
  static String generateDeepLink({
    required String host,
    required String path,
    Map<String, String>? params,
  }) {
    final uri = Uri(
      scheme: appScheme,
      host: host,
      path: path,
      queryParameters: params,
    );
    return uri.toString();
  }

  // Génère un deep link de paiement
  static String generatePaymentDeepLink({
    required String status,
    String? transactionId,
    String? commandId,
    String? clientId,
    String? reason,
  }) {
    final params = <String, String>{};

    if (transactionId != null) params['transaction_id'] = transactionId;
    if (commandId != null) params['command_id'] = commandId;
    if (clientId != null) params['client_id'] = clientId;
    if (reason != null) params['reason'] = reason;

    String path;
    switch (status.toLowerCase()) {
      case 'success':
        path = successPath;
        break;
      case 'failure':
      case 'failed':
        path = failurePath;
        break;
      case 'pending':
        path = pendingPath;
        break;
      default:
        path = failurePath;
        params['reason'] = 'Statut inconnu: $status';
    }

    return generateDeepLink(
      host: paymentHost,
      path: path,
      params: params.isNotEmpty ? params : null,
    );
  }

  // Valide un deep link
  static bool isValidDeepLink(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.scheme == appScheme &&
          (uri.host == paymentHost || uri.host == callbackHost);
    } catch (e) {
      return false;
    }
  }

  // Extrait les informations d'un deep link
  static Map<String, dynamic>? parseDeepLink(String url) {
    try {
      final uri = Uri.parse(url);

      if (!isValidDeepLink(url)) {
        return null;
      }

      return {
        'scheme': uri.scheme,
        'host': uri.host,
        'path': uri.path,
        'params': uri.queryParameters,
      };
    } catch (e) {
      return null;
    }
  }

  // URLs de test pour le développement
  static const Map<String, String> testUrls = {
    'success':
        '$appScheme://$paymentHost$successPath?transaction_id=test_success&command_id=test_cmd&client_id=test_client',
    'failure':
        '$appScheme://$paymentHost$failurePath?transaction_id=test_failure&reason=Test%20failure',
    'pending':
        '$appScheme://$paymentHost$pendingPath?transaction_id=test_pending',
  };

  // Configuration des timeouts
  static const Duration callbackTimeout = Duration(seconds: 30);
  static const Duration deepLinkProcessingTimeout = Duration(seconds: 10);

  // Configuration des retry
  static const int maxRetryAttempts = 3;
  static const Duration retryDelay = Duration(seconds: 2);
}
