import 'package:callitris/pages/menu.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:callitris/pages/auth_provider.dart';

class ClientSatisfairePage extends StatefulWidget {
  const ClientSatisfairePage({super.key});

  @override
  _ClientSatisfairePageState createState() => _ClientSatisfairePageState();
}

class Client {
  final String id;
  final String nom;
  final String prenom;
  final String contact;
  final String code;
  final String journalier;
  final String nbjr;
  final String pourcentagePaye;
  final String dateCreation;
  final String livret;
  final String pack;

  Client({
    required this.id,
    required this.nom,
    required this.prenom,
    required this.contact,
    required this.code,
    required this.journalier,
    required this.nbjr,
    required this.pourcentagePaye,
    required this.dateCreation,
    required this.livret,
    required this.pack,
  });
}

class _ClientSatisfairePageState extends State<ClientSatisfairePage> {
  String searchText = '';
  List<Client> clients = [];
  bool _isLoading = false;
  Map<String, dynamic> stats = {};
  bool _disposed = false;

  @override
  void initState() {
    super.initState();
    fetchClients();
  }

  @override
  void dispose() {
    _disposed = true;
    super.dispose();
  }

  Future<void> fetchClients() async {
    if (_disposed) return;

    try {
      if (mounted) {
        setState(() {
          _isLoading = true;
        });
      }
      final provide = Provider.of<AuthProvider>(context, listen: false);
      final user = Provider.of<AuthProvider>(context, listen: false).user;
      final token = Provider.of<AuthProvider>(context, listen: false).token;

      String idPersonnel = user!['id_personnel'].toString();

      final response = await http.get(
        Uri.parse(provide.getEndpoint(
            'products/getClientSatisfait.php?personnel_id=$idPersonnel')),
        headers: {'Authorization': token!, 'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseBody = jsonDecode(response.body);

        if (responseBody['code'] == 200 && responseBody['data'] != null) {
          final List<dynamic> responseData = responseBody['data'];

          List<Client> fetchedClients = responseData.map((clientData) {
            String id = clientData['id_client'].toString();
            String nom = clientData['nom_client'].toString();
            String prenom = clientData['prenom_client'].toString();
            String contact = clientData['telephone_client'].toString();
            String journalier = clientData['journalier'].toString();
            String code = clientData['code_cmd'].toString();
            String nbjr = clientData['nbjr'].toString();
            String pourcentagePaye =
                clientData['progress']?.toString() ?? '0.0';
            String dateCreation = clientData['date_commande']?.toString() ?? '';
            String livret = clientData['livret']?.toString() ?? '';
            String pack = clientData['pack']?.toString() ?? '';

            return Client(
              id: id,
              nom: nom,
              prenom: prenom,
              contact: contact,
              code: code,
              journalier: journalier,
              nbjr: nbjr,
              pourcentagePaye: pourcentagePaye,
              dateCreation: dateCreation,
              livret: livret,
              pack: pack,
            );
          }).toList();

          if (mounted && !_disposed) {
            setState(() {
              clients = fetchedClients;
              _isLoading = false;
            });
          }
        } else {
          if (mounted && !_disposed) {
            setState(() {
              clients = [];
              _isLoading = false;
            });
          }
        }
      } else {
        if (mounted && !_disposed) {
          setState(() {
            clients = [];
            _isLoading = false;
          });
        }
      }
    } catch (error) {
      debugPrint('Erreur lors de la récupération des clients: $error');
      if (mounted && !_disposed) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // Méthode pour déterminer la couleur selon le pourcentage
  Color _getPourcentageColor(double pourcentage) {
    if (pourcentage >= 90) {
      return Colors.green; // Vert pour 90% et plus
    } else if (pourcentage >= 80) {
      return Colors.orange; // Orange pour 80-89%
    } else if (pourcentage >= 60) {
      return Colors.amber; // Ambre pour 60-79%
    } else {
      return Colors.red; // Rouge pour moins de 60%
    }
  }

  // Méthode pour construire les cartes de statistiques simples
  Widget _buildSimpleStatCard(
      String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: color,
            size: 20,
          ),
          const SizedBox(height: 6),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: TextStyle(
              fontSize: 11,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  // Méthode pour construire une carte client dans le style de l'app
  Widget _buildClientCard(Client client) {
    final double pourcentage = double.tryParse(client.pourcentagePaye) ?? 0;
    final Color progressColor = _getPourcentageColor(pourcentage);

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.grey.shade200,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.06),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header avec nom et avatar
            Row(
              children: [
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        progressColor.withValues(alpha: 0.8),
                        progressColor
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Center(
                    child: Text(
                      '${client.nom[0]}${client.prenom[0]}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${client.nom} ${client.prenom}',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Icon(
                            Icons.qr_code,
                            size: 14,
                            color: Colors.grey[500],
                          ),
                          const SizedBox(width: 4),
                          Text(
                            client.code,
                            style: TextStyle(
                              fontSize: 13,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: progressColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    '${client.pourcentagePaye}%',
                    style: TextStyle(
                      color: progressColor,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Informations détaillées
            _buildSimpleInfoRow(Icons.phone, 'Contact', client.contact),
            const SizedBox(height: 8),
            _buildSimpleInfoRow(Icons.monetization_on, 'Journalier',
                '${client.journalier} FCFA'),
            const SizedBox(height: 8),
            _buildSimpleInfoRow(
                Icons.calendar_today, 'Jours payés', '${client.nbjr} jours'),

            if (client.dateCreation.isNotEmpty) ...[
              const SizedBox(height: 8),
              _buildSimpleInfoRow(
                  Icons.access_time, 'Créé le', client.dateCreation),
            ],

            if (client.livret.isNotEmpty) ...[
              const SizedBox(height: 8),
              _buildSimpleInfoRow(Icons.book, 'Livret', client.livret),
            ],

            if (client.pack.isNotEmpty) ...[
              const SizedBox(height: 8),
              _buildSimpleInfoRow(Icons.inventory, 'Pack', client.pack),
            ],

            // Barre de progression
            const SizedBox(height: 12),
            Container(
              height: 6,
              decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: BorderRadius.circular(3),
              ),
              child: FractionallySizedBox(
                alignment: Alignment.centerLeft,
                widthFactor: pourcentage / 100,
                child: Container(
                  decoration: BoxDecoration(
                    color: progressColor,
                    borderRadius: BorderRadius.circular(3),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSimpleInfoRow(IconData icon, String label, String value) {
    return Row(
      children: [
        Icon(
          icon,
          size: 14,
          color: Colors.grey[500],
        ),
        const SizedBox(width: 8),
        Text(
          '$label: ',
          style: TextStyle(
            fontSize: 13,
            color: Colors.grey[600],
            fontWeight: FontWeight.w500,
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(
              fontSize: 13,
              color: Colors.black87,
              fontWeight: FontWeight.w600,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Clients à satisfaire'),
        backgroundColor: const Color.fromARGB(255, 249, 221, 175),
        actions: buildAppBarActions(context),
      ),
      backgroundColor: Colors.grey[50],
      body: Column(
        children: [
          Container(
            margin: const EdgeInsets.all(16),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: Colors.grey.shade200,
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.06),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Statistiques',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Expanded(
                      child: _buildSimpleStatCard(
                        'Total',
                        clients.length.toString(),
                        Icons.people,
                        Colors.blue,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _buildSimpleStatCard(
                        '90%+',
                        clients
                            .where((c) =>
                                double.tryParse(c.pourcentagePaye) != null &&
                                double.parse(c.pourcentagePaye) >= 90)
                            .length
                            .toString(),
                        Icons.star,
                        Colors.green,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _buildSimpleStatCard(
                        '80-89%',
                        clients
                            .where((c) =>
                                double.tryParse(c.pourcentagePaye) != null &&
                                double.parse(c.pourcentagePaye) >= 80 &&
                                double.parse(c.pourcentagePaye) < 90)
                            .length
                            .toString(),
                        Icons.trending_up,
                        Colors.orange,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          // Barre de recherche
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: TextField(
              onChanged: (value) {
                setState(() {
                  searchText = value;
                });
              },
              decoration: InputDecoration(
                hintText: 'Rechercher un client',
                prefixIcon: const Icon(Icons.search, color: Colors.grey),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10.0),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: Colors.grey[200],
                contentPadding: const EdgeInsets.symmetric(
                  vertical: 15.0,
                  horizontal: 20.0,
                ),
              ),
            ),
          ),
          // Liste des clients
          Expanded(
            child: _isLoading
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const CircularProgressIndicator(
                          valueColor:
                              AlwaysStoppedAnimation<Color>(Color(0xFF4CAF50)),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Chargement des clients...',
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                  )
                : clients.isEmpty
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Container(
                              padding: const EdgeInsets.all(24),
                              decoration: BoxDecoration(
                                color: Colors.grey[100],
                                shape: BoxShape.circle,
                              ),
                              child: Icon(
                                Icons.people_outline,
                                size: 64,
                                color: Colors.grey[400],
                              ),
                            ),
                            const SizedBox(height: 24),
                            Text(
                              'Aucun client satisfait',
                              style: TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                color: Colors.grey[700],
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Les clients ayant payé 80% ou plus\napparaîtront ici',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey[500],
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      )
                    : ListView.builder(
                        padding: const EdgeInsets.all(16),
                        itemCount: clients.length,
                        itemBuilder: (context, index) {
                          final client = clients[index];
                          if (searchText.isNotEmpty &&
                              (!client.nom
                                      .toLowerCase()
                                      .contains(searchText.toLowerCase()) &&
                                  !client.prenom
                                      .toLowerCase()
                                      .contains(searchText.toLowerCase()))) {
                            return const SizedBox.shrink();
                          }
                          return _buildClientCard(client);
                        },
                      ),
          ),
        ],
      ),
    );
  }
}
