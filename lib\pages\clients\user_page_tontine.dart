import 'package:callitris/pages/auth_provider.dart';
import 'package:callitris/pages/clients/vers_tontine.dart';
import 'package:callitris/pages/menu.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

import 'package:provider/provider.dart';

class ClientDetailTontinePage extends StatefulWidget {
  final String id;

  const ClientDetailTontinePage({super.key, required this.id});

  @override
  _ClientDetailTontinePageState createState() =>
      _ClientDetailTontinePageState();
}

class Client {
  final String id;
  final String nom;
  final String prenom;
  final String contact;
  final String contact2;
  final String adresse;

  Client({
    required this.id,
    required this.nom,
    required this.prenom,
    required this.contact,
    required this.contact2,
    required this.adresse,
  });
}

class Tontine {
  final String id;
  final String prixJournalier;
  final int nombreJours;
  final String solde;
  final String libelle;
  final String day;

  Tontine({
    required this.id,
    required this.prixJournalier,
    required this.nombreJours,
    required this.solde,
    required this.libelle,
    required this.day,
  });
}

class _ClientDetailTontinePageState extends State<ClientDetailTontinePage> {
  Client? client;
  bool isLoading = true;
  List<Tontine> tontines = [];
  List<Tontine> filteredTontines = [];
  bool reachedTontines = true;
  TextEditingController montantJournalierController = TextEditingController();
  String? monnaie;
  final GlobalKey<ScaffoldMessengerState> _scaffoldMessengerKey =
      GlobalKey<ScaffoldMessengerState>();

  // Méthode utilitaire pour afficher des SnackBars de manière sécurisée
  void _showSafeSnackBar(String message,
      {Color backgroundColor = Colors.red, Duration? duration}) {
    // Vérifier si le widget est encore monté
    if (!mounted) {
      print('Widget non monté, SnackBar ignoré: $message');
      return;
    }

    // Utiliser un délai pour s'assurer que le contexte est stable
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;

      try {
        // Stratégie 1: Utiliser le GlobalKey si disponible
        if (_scaffoldMessengerKey.currentState != null) {
          _scaffoldMessengerKey.currentState!.showSnackBar(
            SnackBar(
              content: Text(message),
              backgroundColor: backgroundColor,
              duration: duration ?? const Duration(seconds: 3),
            ),
          );
          return;
        }

        // Stratégie 2: Utiliser le contexte si le widget est encore monté
        if (mounted && context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(message),
              backgroundColor: backgroundColor,
              duration: duration ?? const Duration(seconds: 3),
            ),
          );
          return;
        }
      } catch (e) {
        print('Erreur lors de l\'affichage du SnackBar: $e');
        print('Message: $message');
      }
    });
  }

  @override
  void dispose() {
    // Disposez du TextEditingController pour éviter les fuites de mémoire
    montantJournalierController.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    fetchClientData();
    fetchTontines();
    fetchMonnaie();
  }

  Future<void> fetchClientData() async {
    try {
      final token = Provider.of<AuthProvider>(context, listen: false).token;
      final provide = Provider.of<AuthProvider>(context, listen: false);

      final response = await http.get(
        Uri.parse(provide
            .getEndpoint('client/getClientById.php?id_client=${widget.id}')),
        headers: {
          'Authorization': token!,
          'Content-Type': 'application/json',
        },
      );
      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);
        String id = responseData['id_client'].toString();
        String nom = responseData['nom_client'].toString();
        String prenom = responseData['prenom_client'].toString();
        String contact = responseData['telephone_client'].toString();
        String contact2 = responseData['telephone2_client'].toString();
        String adresse = responseData['domicile_client'].toString();

        setState(() {
          client = Client(
            id: id,
            nom: nom,
            prenom: prenom,
            contact: contact,
            contact2: contact2,
            adresse: adresse,
          );
          isLoading = false;
        });
      } else {
        print('Erreur : ${response.statusCode}');
      }
    } catch (error) {
      print('Erreur lors de la récupération des données du client: $error');
    }
  }

  Future<void> fetchTontines() async {
    try {
      final provide = Provider.of<AuthProvider>(context, listen: false);
      final token = Provider.of<AuthProvider>(context, listen: false).token;

      final response = await http.get(
        Uri.parse(provide.getEndpoint(
            'products/getTontinesClient.php?id_client=${widget.id}')),
        headers: {'Authorization': token!, 'Content-Type': 'application/json'},
      );
      //print(response.statusCode);
      //print(response.body);
      if (response.statusCode == 200) {
        final List<dynamic> responseData = jsonDecode(response.body);
        List<Tontine> fetchedTontines = responseData.map((tontineData) {
          String id = tontineData['id_colombe'].toString();
          String prixJournalier = tontineData['journalier_col'].toString();
          int nombreJours = int.parse(tontineData['compteur_col'] ?? '0');
          String solde = tontineData['solde_col'].toString();
          String libelle = tontineData['libelle_col'].toString();
          String day = tontineData['date_ajout'].toString();

          return Tontine(
            id: id,
            prixJournalier: prixJournalier,
            nombreJours: nombreJours,
            solde: solde,
            libelle: libelle,
            day: day,
          );
        }).toList();

        setState(() {
          tontines = fetchedTontines;
          filteredTontines = List.from(tontines);
        });
      } else {
        print('Erreur : ${response.statusCode}');
      }
    } catch (error) {
      print('Erreur lors de la récupération des données de la tontine: $error');
    }
  }

  Future<void> fetchMonnaie() async {
    try {
      final provide = Provider.of<AuthProvider>(context, listen: false);
      final token = Provider.of<AuthProvider>(context, listen: false).token;

      final response = await http.get(
        Uri.parse(
            provide.getEndpoint('client/getMonnaie.php?clientId=${widget.id}')),
        headers: {'Authorization': token!, 'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);
        String monnaieValue = responseData['montant'].toString();

        setState(() {
          monnaie = monnaieValue;
        });
      } else {
        print(
            'Erreur lors de la récupération de la monnaie : ${response.statusCode}');
      }
    } catch (error) {
      print('Erreur lors de la récupération de la monnaie : $error');
    }
  }

  void filterTontines(String query) {
    setState(() {
      if (query.isNotEmpty) {
        filteredTontines = tontines
            .where((tontine) =>
                tontine.id.toLowerCase().contains(query.toLowerCase()))
            .toList();
      } else {
        filteredTontines = List.from(tontines);
      }
    });
  }

  void _sendNewTontine(
      double montantJournalier, String libelleEpargne, String clientId) async {
    try {
      final provide = Provider.of<AuthProvider>(context, listen: false);
      final token = Provider.of<AuthProvider>(context, listen: false).token;
      final user = Provider.of<AuthProvider>(context, listen: false).user;
      String idPersonnel = user!['id_personnel'].toString();
      final response = await http.post(
        Uri.parse(provide.getEndpoint('products/addTontine.php')),
        headers: {
          'Authorization': token!,
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'journalier': montantJournalier,
          'libelle_col': libelleEpargne,
          'clientId': clientId,
          'personnelId': idPersonnel,
        }),
      );
      if (response.statusCode == 200) {
        // Succès de la requête
        print('Réponse brute de l\'API: ${response.body}');

        try {
          // Nettoyer la réponse en cas de JSON multiples concaténés
          String cleanResponse = response.body.trim();
          Map<String, dynamic>? responseData;

          // Méthode 1: Essayer de parser directement
          try {
            responseData = jsonDecode(cleanResponse);
          } catch (e) {
            print(
                'Parsing direct échoué, tentative d\'extraction du premier JSON');

            // Méthode 2: Extraire le premier JSON valide
            int braceCount = 0;
            int startIndex = -1;
            int endIndex = -1;

            for (int i = 0; i < cleanResponse.length; i++) {
              if (cleanResponse[i] == '{') {
                if (startIndex == -1) startIndex = i;
                braceCount++;
              } else if (cleanResponse[i] == '}') {
                braceCount--;
                if (braceCount == 0 && startIndex != -1) {
                  endIndex = i + 1;
                  break;
                }
              }
            }

            if (startIndex != -1 && endIndex != -1) {
              String firstJsonString =
                  cleanResponse.substring(startIndex, endIndex);
              print('Premier JSON extrait: $firstJsonString');
              responseData = jsonDecode(firstJsonString);
            } else {
              throw FormatException('Impossible d\'extraire un JSON valide');
            }
          }

          if (responseData != null) {
            print('Code de réponse: ${responseData['code']}');

            if (responseData['code'] == 201) {
              _showSafeSnackBar(
                responseData['message'] ?? 'Erreur inconnue',
                backgroundColor: Colors.red,
              );
            } else {
              _showSafeSnackBar(
                responseData['message'] ?? 'Succès',
                backgroundColor: Colors.green,
              );
              if (mounted) {
                setState(() {
                  // Réinitialiser la liste des tontines pour forcer la récupération des données mises à jour depuis l'API
                  tontines.clear();
                  filteredTontines.clear();
                  fetchTontines();
                });
              }
            }
          } else {
            throw FormatException('Aucune donnée JSON valide trouvée');
          }
        } catch (e) {
          print('Erreur de parsing JSON: $e');
          _showSafeSnackBar('Erreur de format de réponse du serveur');
        }
      } else {
        // Erreur lors de la requête
        print(
            'Erreur lors de l\'envoi des données à l\'API: ${response.statusCode}');
        print('Corps de la réponse d\'erreur: ${response.body}');
        _showSafeSnackBar('Erreur serveur: ${response.statusCode}');
      }
    } catch (error) {
      // Gestion des erreurs
      print('Erreur lors de l\'envoi des données à l\'API: $error');
    }
  }

  @override
  Widget build(BuildContext context) {
    return ScaffoldMessenger(
      key: _scaffoldMessengerKey,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Détail Client Tontine'),
          backgroundColor: const Color.fromARGB(255, 249, 221, 175),
          actions: buildAppBarActions(context),
        ),
        backgroundColor: Colors.grey[50],
        body: isLoading
            ? const Center(
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.purple),
                ),
              )
            : SingleChildScrollView(
                child: client != null
                    ? Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Header moderne avec informations client
                          _buildModernClientHeader(),
                          const SizedBox(height: 20),

                          // Section des tontines
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 20),
                            child: Column(
                              children: [
                                _buildTontineHeader(),
                                const SizedBox(height: 16),
                                _buildModernSearchBar(),
                                const SizedBox(height: 20),
                                _buildModernTontinesList(),
                              ],
                            ),
                          ),
                        ],
                      )
                    : Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.person_off,
                              size: 64,
                              color: Colors.grey[400],
                            ),
                            const SizedBox(height: 16),
                            Text(
                              "Client non trouvé",
                              style: TextStyle(
                                fontSize: 18,
                                color: Colors.grey[600],
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),
              ),
      ),
    );
  }

  Widget _buildModernClientHeader() {
    return Container(
      margin: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.blue.shade50,
            Colors.indigo.shade50,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.blue.shade100),
        boxShadow: [
          BoxShadow(
            color: Colors.blue.withValues(alpha: 0.08),
            blurRadius: 12,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            // Avatar avec gradient doux
            Container(
              width: 70,
              height: 70,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.indigo.shade200,
                    Colors.blue.shade300,
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(color: Colors.white, width: 2),
                boxShadow: [
                  BoxShadow(
                    color: Colors.blue.withValues(alpha: 0.2),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Center(
                child: Text(
                  '${client!.nom[0]}${client!.prenom[0]}',
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Nom complet
            Text(
              '${client!.nom} ${client!.prenom}',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w600,
                color: Colors.indigo.shade800,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),

            // Informations de contact
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.7),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.blue.shade100),
              ),
              child: Column(
                children: [
                  _buildColoredContactRow(Icons.phone_outlined, client!.contact,
                      Colors.teal.shade600),
                  const SizedBox(height: 8),
                  _buildColoredContactRow(Icons.phone_android_outlined,
                      client!.contact2, Colors.cyan.shade600),
                  const SizedBox(height: 8),
                  _buildColoredContactRow(Icons.location_on_outlined,
                      client!.adresse, Colors.orange.shade600),
                ],
              ),
            ),
            const SizedBox(height: 16),

            // Solde monnaie
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.teal.shade50,
                    Colors.green.shade50,
                  ],
                ),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.green.shade200),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.account_balance_wallet_outlined,
                    color: Colors.green.shade700,
                    size: 18,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Solde: ${monnaie ?? '0'} FCFA',
                    style: TextStyle(
                      fontSize: 15,
                      fontWeight: FontWeight.w600,
                      color: Colors.green.shade700,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildColoredContactRow(IconData icon, String text, Color iconColor) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(6),
          decoration: BoxDecoration(
            color: iconColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Icon(
            icon,
            size: 14,
            color: iconColor,
          ),
        ),
        const SizedBox(width: 10),
        Expanded(
          child: Text(
            text,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade700,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildClientInfo(String nom, String prenom, String contact,
      String contact2, String adresse, String? monnaie) {
    return Card(
      elevation: 3,
      margin: const EdgeInsets.symmetric(vertical: 8),
      color: Colors.blue[50], // Couleur de fond de la carte
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: 60, // Largeur du conteneur de l'avatar
              height: 60, // Hauteur du conteneur de l'avatar
              decoration: const BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.white, // Couleur de fond de l'avatar
              ),
              child: Center(
                child: SvgPicture.asset(
                  'assets/icons/clipboard.svg',
                  width: 40.0,
                  colorFilter:
                      const ColorFilter.mode(Colors.orange, BlendMode.srcIn),
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '$nom $prenom',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 18,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Contact: $contact',
                    style: TextStyle(
                      color: Colors.grey[700],
                    ),
                  ),
                  Text(
                    'Contact Proche: $contact2',
                    style: TextStyle(
                      color: Colors.grey[700],
                    ),
                  ),
                  Text(
                    'Adresse: $adresse',
                    style: TextStyle(
                      color: Colors.grey[700],
                    ),
                  ),
                  const SizedBox(height: 12),
                  const Divider(),
                  const SizedBox(height: 8),
                  Text(
                    'Monnaie: $monnaie FCFA',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.green,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildShoppingButton() {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Row(
        children: [
          const Expanded(
            child: Text(
              'Mes Comptes Tontine',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.black,
              ),
            ),
          ),
          ElevatedButton.icon(
            onPressed: () {
              _showNouvelleTontineSheet(context);
            },
            style: ElevatedButton.styleFrom(
              padding:
                  const EdgeInsets.symmetric(vertical: 10.0, horizontal: 20.0),
              backgroundColor: Colors.orangeAccent,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(5.0),
              ),
            ),
            icon: const Icon(Icons.euro, color: Colors.white),
            label: const Text(
              'Epargner',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  void _showNouvelleTontineSheet(BuildContext context) {
    double montantJournalier = 0;
    double totalRetraitPossible = 0;
    double totalSoldeAttendu = 0;
    String libelleEpargne = '';

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) {
        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setState) {
            return SingleChildScrollView(
              padding: EdgeInsets.only(
                bottom: MediaQuery.of(context).viewInsets.bottom,
              ),
              child: Container(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Text(
                      'Nouvelle Tontine',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 20,
                      ),
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      decoration:
                          const InputDecoration(labelText: 'Libellé épargne'),
                      onChanged: (value) {
                        setState(() {
                          libelleEpargne = value;
                        });
                      },
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: montantJournalierController,
                      decoration: const InputDecoration(
                          labelText: 'Montant journalier'),
                      keyboardType:
                          const TextInputType.numberWithOptions(decimal: true),
                      onChanged: (value) {
                        setState(() {
                          try {
                            montantJournalier = double.parse(value);
                            totalRetraitPossible = montantJournalier * 20;
                            totalSoldeAttendu = montantJournalier * 21;
                          } catch (e) {
                            print(
                                'Erreur lors de la conversion de la valeur saisie en double: $e');
                            totalRetraitPossible = 1;
                            totalSoldeAttendu = 1;
                          }
                        });
                      },
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Total retrait possible: $totalRetraitPossible',
                      style: const TextStyle(
                          fontWeight: FontWeight.bold, fontSize: 20),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Total solde attendu: $totalSoldeAttendu',
                      style: const TextStyle(
                          fontWeight: FontWeight.bold, fontSize: 16),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () {
                        _sendNewTontine(
                            montantJournalier, libelleEpargne, widget.id);
                        Navigator.pop(
                            context); // Ferme la modalité après l'envoi des données

                        // Réinitialisez les valeurs des champs après la validation et la fermeture de la modalité
                        setState(() {
                          montantJournalierController.clear();
                          montantJournalier = 0;
                          libelleEpargne = '';
                        });
                      },
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(
                            vertical: 10.0,
                            horizontal: 20), // Ajuster le padding
                        backgroundColor:
                            Colors.blue, // Couleur de fond du bouton
                        shape: RoundedRectangleBorder(
                          borderRadius:
                              BorderRadius.circular(10.0), // Bord arrondi
                        ),
                      ),
                      child: const Text(
                        'Enregistrer',
                        style: TextStyle(fontSize: 20, color: Colors.white),
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildSearchBar() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: TextField(
        decoration: InputDecoration(
          prefixIcon: const Icon(Icons.search),
          hintText: 'Rechercher une tontine',
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8.0),
          ),
        ),
        onChanged: (query) {
          filterTontines(query);
        },
      ),
    );
  }

  Widget _buildCommandesList() {
    return SingleChildScrollView(
      physics: reachedTontines ? const NeverScrollableScrollPhysics() : null,
      child: Column(
        children: [
          const SizedBox(height: 16),
          ...filteredTontines.map((tontine) {
            return Card(
              elevation: 3,
              margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
              child: Column(
                children: [
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Icon(Icons.money_rounded, color: Colors.blue),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                tontine.libelle,
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Text('Journalier: ${tontine.prixJournalier}'),
                              Text('Jours: ${tontine.nombreJours}'),
                              Text('Solde: ${tontine.solde}'),
                              Text('Date: ${tontine.day}'),
                            ],
                          ),
                        ),
                        const SizedBox(
                            width:
                                16), // Ajout d'un espace entre le texte et les boutons
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            ElevatedButton(
                              onPressed: () async {
                                final result = await Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => VersementTontinePage(
                                        id: tontine.id, client: widget.id),
                                  ),
                                );

                                //print(result);
                                if (result == null) {
                                  // Recharger les données
                                  await fetchClientData();
                                  await fetchTontines();
                                  await fetchMonnaie();
                                }
                              },
                              style: ElevatedButton.styleFrom(
                                padding: const EdgeInsets.symmetric(
                                    vertical: 10.0, horizontal: 20.0),
                                backgroundColor: Colors.blue,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(5.0),
                                ),
                              ),
                              child: const Text(
                                'Verser',
                                style: TextStyle(color: Colors.white),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  const Divider(height: 0, thickness: 1),
                ],
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildTontineHeader() {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Mes Comptes Tontine',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                  color: Colors.indigo.shade700,
                ),
              ),
              Text(
                '${filteredTontines.length} compte${filteredTontines.length > 1 ? 's' : ''} actif${filteredTontines.length > 1 ? 's' : ''}',
                style: TextStyle(
                  fontSize: 13,
                  color: Colors.indigo.shade400,
                ),
              ),
            ],
          ),
        ),
        Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Colors.purple.shade300,
                Colors.indigo.shade400,
              ],
            ),
            borderRadius: BorderRadius.circular(10),
            boxShadow: [
              BoxShadow(
                color: Colors.purple.withValues(alpha: 0.2),
                blurRadius: 6,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: () => _showModernNouvelleTontineSheet(context),
              borderRadius: BorderRadius.circular(10),
              child: const Padding(
                padding: EdgeInsets.symmetric(horizontal: 14, vertical: 10),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.add, color: Colors.white, size: 18),
                    SizedBox(width: 6),
                    Text(
                      'Épargner',
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.w500,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildModernSearchBar() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextField(
        decoration: InputDecoration(
          hintText: 'Rechercher une tontine...',
          hintStyle: TextStyle(color: Colors.grey[500]),
          prefixIcon: Icon(Icons.search, color: Colors.grey[500]),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide.none,
          ),
          filled: true,
          fillColor: Colors.white,
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
        ),
        onChanged: (query) {
          filterTontines(query);
        },
      ),
    );
  }

  Widget _buildModernTontinesList() {
    if (filteredTontines.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(40),
        child: Column(
          children: [
            Icon(
              Icons.savings_outlined,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'Aucune tontine trouvée',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[600],
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Créez votre première épargne',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[500],
              ),
            ),
          ],
        ),
      );
    }

    return Column(
      children: filteredTontines.map((tontine) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: ModernTontineCard(
            tontine: tontine,
            onVersementPressed: () async {
              final result = await Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) =>
                      VersementTontinePage(id: tontine.id, client: widget.id),
                ),
              );

              if (result == null) {
                await fetchClientData();
                await fetchTontines();
                await fetchMonnaie();
              }
            },
          ),
        );
      }).toList(),
    );
  }

  void _showModernNouvelleTontineSheet(BuildContext context) {
    _showNouvelleTontineSheet(context);
  }
}

// Widget moderne pour les cartes de tontine
class ModernTontineCard extends StatelessWidget {
  final Tontine tontine;
  final VoidCallback onVersementPressed;

  const ModernTontineCard({
    super.key,
    required this.tontine,
    required this.onVersementPressed,
  });

  @override
  Widget build(BuildContext context) {
    double progress = tontine.nombreJours / 21.0;
    if (progress > 1.0) progress = 1.0;

    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.white,
            Colors.blue.shade50,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.blue.shade100,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.blue.withValues(alpha: 0.08),
            blurRadius: 8,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header avec titre et badge
            Row(
              children: [
                Container(
                  width: 36,
                  height: 36,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Colors.teal.shade200,
                        Colors.cyan.shade300,
                      ],
                    ),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.white, width: 1),
                  ),
                  child: Icon(
                    Icons.savings_outlined,
                    color: Colors.white,
                    size: 18,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        tontine.libelle,
                        style: const TextStyle(
                          fontSize: 15,
                          fontWeight: FontWeight.w600,
                          color: Colors.black87,
                        ),
                      ),
                      Text(
                        'Créé le ${tontine.day}',
                        style: TextStyle(
                          fontSize: 11,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _getStatusColor().withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(6),
                    border: Border.all(
                      color: _getStatusColor().withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Text(
                    _getStatusText(),
                    style: TextStyle(
                      fontSize: 13,
                      fontWeight: FontWeight.bold,
                      color: _getStatusColor(),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Informations financières
            Container(
              padding: const EdgeInsets.all(14),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.indigo.shade50,
                    Colors.blue.shade50,
                  ],
                ),
                borderRadius: BorderRadius.circular(10),
                border: Border.all(color: Colors.blue.shade100),
              ),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      _buildInfoColumn(
                          'Journalier', '${tontine.prixJournalier} FCFA'),
                      _buildInfoColumn('Jours', '${tontine.nombreJours}/21'),
                      _buildInfoColumn('Solde', '${tontine.solde} FCFA'),
                    ],
                  ),
                  const SizedBox(height: 14),

                  // Barre de progression avec couleur selon le pourcentage
                  Container(
                    height: 6,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(3),
                      color: Colors.grey.shade200,
                    ),
                    child: FractionallySizedBox(
                      alignment: Alignment.centerLeft,
                      widthFactor: progress > 0
                          ? progress
                          : 0.01, // Très petit point à 0%
                      child: Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(3),
                          color: _getProgressColor(progress),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 14),

            // Bouton d'action
            SizedBox(
              width: double.infinity,
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Colors.teal.shade50,
                      Colors.cyan.shade50,
                    ],
                  ),
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(
                    color: Colors.teal.shade200,
                    width: 1,
                  ),
                ),
                child: Material(
                  color: Colors.transparent,
                  borderRadius: BorderRadius.circular(10),
                  child: InkWell(
                    onTap: onVersementPressed,
                    borderRadius: BorderRadius.circular(10),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                          vertical: 12, horizontal: 16),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.account_balance_wallet_outlined,
                            size: 18,
                            color: Colors.teal.shade700,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Effectuer un Versement',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                              color: Colors.teal.shade700,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoColumn(String label, String value) {
    return Column(
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
      ],
    );
  }

  Color _getStatusColor() {
    if (tontine.nombreJours >= 21) {
      return Colors.green.shade600;
    } else if (tontine.nombreJours >= 15) {
      return Colors.orange.shade600;
    } else {
      return Colors.blue.shade600;
    }
  }

  String _getStatusText() {
    if (tontine.nombreJours >= 21) {
      return 'Terminé';
    } else if (tontine.nombreJours >= 15) {
      return 'Bientôt fini';
    } else {
      return 'En cours';
    }
  }

  Color _getProgressColor(double progress) {
    double percentage = progress * 100;
    if (percentage >= 100) {
      return Colors.green.shade400;
    } else if (percentage >= 34) {
      return Colors.orange.shade400;
    } else {
      return Colors.red.shade400;
    }
  }
}
