<?php
/**
 * Callback handler pour CinetPay
 * Ce fichier gère les notifications de paiement de CinetPay et redirige vers l'app
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Gestion des requêtes OPTIONS pour CORS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Configuration
$config = [
    'cinetpay_api_key' => 'your_cinetpay_api_key',     // À remplacer par votre clé API CinetPay
    'cinetpay_site_id' => 'your_cinetpay_site_id',     // À remplacer par votre Site ID CinetPay
    'cinetpay_secret' => 'your_cinetpay_secret',       // À remplacer par votre secret CinetPay
    'app_scheme' => 'callitris',
    'log_file' => __DIR__ . '/logs/cinetpay_callback.log'
];

// Fonction de logging
function logMessage($message, $logFile) {
    $timestamp = date('Y-m-d H:i:s');
    $logEntry = "[$timestamp] $message" . PHP_EOL;
    
    // Créer le dossier logs s'il n'existe pas
    $logDir = dirname($logFile);
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
}

// Fonction pour valider la signature CinetPay
function validateCinetPaySignature($data, $signature, $secret) {
    // CinetPay utilise généralement une signature basée sur les données de transaction
    $expectedSignature = hash('sha256', implode('', $data) . $secret);
    return hash_equals($expectedSignature, $signature);
}

// Fonction pour vérifier le statut du paiement avec l'API CinetPay
function verifyCinetPayTransaction($transactionId, $apiKey, $siteId) {
    $url = "https://api-checkout.cinetpay.com/v2/payment/check";
    
    $data = [
        'apikey' => $apiKey,
        'site_id' => $siteId,
        'transaction_id' => $transactionId
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Accept: application/json'
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode === 200) {
        return json_decode($response, true);
    }
    
    return null;
}

// Fonction pour générer le deep link
function generateDeepLink($status, $params = []) {
    global $config;
    
    $baseUrl = $config['app_scheme'] . '://payment/' . $status;
    
    if (!empty($params)) {
        $queryString = http_build_query($params);
        $baseUrl .= '?' . $queryString;
    }
    
    return $baseUrl;
}

// Fonction pour envoyer une notification push (optionnel)
function sendPushNotification($transactionId, $status, $message) {
    // Ici vous pouvez intégrer votre service de notifications push
    logMessage("Push notification: Transaction $transactionId - Status: $status - Message: $message", $GLOBALS['config']['log_file']);
}

try {
    // Log de la requête entrante
    $method = $_SERVER['REQUEST_METHOD'];
    $rawInput = file_get_contents('php://input');
    
    logMessage("CinetPay callback received - Method: $method", $config['log_file']);
    logMessage("Raw input: $rawInput", $config['log_file']);
    
    // Traitement selon la méthode HTTP
    if ($method === 'POST') {
        // Callback de notification de CinetPay
        $data = json_decode($rawInput, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            // Essayer de traiter comme des données de formulaire
            parse_str($rawInput, $data);
            if (empty($data)) {
                $data = $_POST;
            }
        }
        
        if (empty($data)) {
            logMessage("No data received in CinetPay callback", $config['log_file']);
            http_response_code(400);
            echo json_encode(['error' => 'No data received']);
            exit();
        }
        
        // Extraire les informations du paiement
        $transactionId = $data['cpm_trans_id'] ?? $data['transaction_id'] ?? '';
        $status = $data['cpm_result'] ?? $data['status'] ?? '';
        $amount = $data['cpm_amount'] ?? $data['amount'] ?? 0;
        $currency = $data['cpm_currency'] ?? $data['currency'] ?? 'XOF';
        $customData = $data['cpm_custom'] ?? $data['custom'] ?? '';
        $paymentMethod = $data['cpm_payid'] ?? $data['payment_method'] ?? '';
        
        logMessage("CinetPay payment notification - Transaction: $transactionId, Status: $status, Amount: $amount", $config['log_file']);
        
        // Vérifier le paiement avec l'API CinetPay pour plus de sécurité
        if (!empty($transactionId) && !empty($config['cinetpay_api_key'])) {
            $verificationResult = verifyCinetPayTransaction(
                $transactionId, 
                $config['cinetpay_api_key'], 
                $config['cinetpay_site_id']
            );
            
            if ($verificationResult) {
                logMessage("CinetPay verification result: " . json_encode($verificationResult), $config['log_file']);
                
                // Utiliser les données vérifiées
                if (isset($verificationResult['data'])) {
                    $verifiedData = $verificationResult['data'];
                    $status = $verifiedData['status'] ?? $status;
                    $amount = $verifiedData['amount'] ?? $amount;
                }
            }
        }
        
        // Déterminer le statut pour le deep link
        $deepLinkStatus = '';
        $deepLinkParams = [
            'transaction_id' => $transactionId,
            'amount' => $amount,
            'currency' => $currency,
            'payment_method' => $paymentMethod
        ];
        
        // Extraire les informations de commande et client depuis custom data si disponible
        if (!empty($customData)) {
            $customDataDecoded = json_decode($customData, true);
            if (is_array($customDataDecoded)) {
                if (isset($customDataDecoded['command_id'])) {
                    $deepLinkParams['command_id'] = $customDataDecoded['command_id'];
                }
                if (isset($customDataDecoded['client_id'])) {
                    $deepLinkParams['client_id'] = $customDataDecoded['client_id'];
                }
            }
        }
        
        // Mapper les statuts CinetPay
        switch ($status) {
            case '00': // Succès
            case 'ACCEPTED':
            case 'SUCCESS':
                $deepLinkStatus = 'success';
                sendPushNotification($transactionId, 'success', 'Paiement effectué avec succès');
                break;
                
            case '01': // Échec
            case '02': // Échec
            case 'FAILED':
            case 'REFUSED':
            case 'CANCELLED':
                $deepLinkStatus = 'failure';
                $deepLinkParams['reason'] = $data['cpm_error_message'] ?? $data['error_message'] ?? 'Paiement échoué';
                sendPushNotification($transactionId, 'failure', 'Paiement échoué');
                break;
                
            case 'PENDING':
            case 'PROCESSING':
                $deepLinkStatus = 'pending';
                sendPushNotification($transactionId, 'pending', 'Paiement en cours de traitement');
                break;
                
            default:
                logMessage("Unknown CinetPay payment status: $status", $config['log_file']);
                $deepLinkStatus = 'failure';
                $deepLinkParams['reason'] = 'Statut de paiement inconnu: ' . $status;
        }
        
        // Générer le deep link
        $deepLink = generateDeepLink($deepLinkStatus, $deepLinkParams);
        
        logMessage("Generated deep link: $deepLink", $config['log_file']);
        
        // Répondre à CinetPay
        http_response_code(200);
        echo json_encode([
            'status' => 'success',
            'message' => 'Callback processed',
            'deep_link' => $deepLink
        ]);
        
    } elseif ($method === 'GET') {
        // Redirection depuis CinetPay vers l'app
        $transactionId = $_GET['transaction_id'] ?? $_GET['cpm_trans_id'] ?? '';
        $status = $_GET['status'] ?? $_GET['cpm_result'] ?? '';
        $commandId = $_GET['command_id'] ?? '';
        $clientId = $_GET['client_id'] ?? '';
        
        logMessage("CinetPay redirect - Transaction: $transactionId, Status: $status", $config['log_file']);
        
        // Vérifier le paiement avec l'API CinetPay
        if (!empty($transactionId) && !empty($config['cinetpay_api_key'])) {
            $verificationResult = verifyCinetPayTransaction(
                $transactionId, 
                $config['cinetpay_api_key'], 
                $config['cinetpay_site_id']
            );
            
            if ($verificationResult && isset($verificationResult['data'])) {
                $verifiedData = $verificationResult['data'];
                $status = $verifiedData['status'] ?? $status;
                
                logMessage("CinetPay redirect verification: " . json_encode($verifiedData), $config['log_file']);
            }
        }
        
        // Déterminer le statut pour le deep link
        $deepLinkStatus = '';
        $deepLinkParams = ['transaction_id' => $transactionId];
        
        if (!empty($commandId)) {
            $deepLinkParams['command_id'] = $commandId;
        }
        if (!empty($clientId)) {
            $deepLinkParams['client_id'] = $clientId;
        }
        
        switch ($status) {
            case '00':
            case 'ACCEPTED':
            case 'SUCCESS':
                $deepLinkStatus = 'success';
                break;
                
            case '01':
            case '02':
            case 'FAILED':
            case 'REFUSED':
            case 'CANCELLED':
                $deepLinkStatus = 'failure';
                $deepLinkParams['reason'] = $_GET['error_message'] ?? 'Paiement échoué';
                break;
                
            case 'PENDING':
            case 'PROCESSING':
                $deepLinkStatus = 'pending';
                break;
                
            default:
                $deepLinkStatus = 'failure';
                $deepLinkParams['reason'] = 'Statut de paiement inconnu';
        }
        
        // Générer le deep link
        $deepLink = generateDeepLink($deepLinkStatus, $deepLinkParams);
        
        logMessage("Redirecting to deep link: $deepLink", $config['log_file']);
        
        // Redirection vers l'app
        header("Location: $deepLink");
        exit();
        
    } else {
        // Méthode non supportée
        http_response_code(405);
        echo json_encode(['error' => 'Method not allowed']);
    }
    
} catch (Exception $e) {
    logMessage("Error in CinetPay callback: " . $e->getMessage(), $config['log_file']);
    
    http_response_code(500);
    echo json_encode([
        'error' => 'Internal server error',
        'message' => $e->getMessage()
    ]);
}
?>
