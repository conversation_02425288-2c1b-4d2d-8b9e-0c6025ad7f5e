# Configuration Deep Links pour Callitris

Ce dossier contient toutes les configurations nécessaires pour intégrer les deep links dans l'application Callitris.

## Structure du dossier

```
deep_links_config/
├── README.md                    # Ce fichier
├── android/                     # Configurations Android
│   └── android_manifest_config.xml
├── ios/                         # Configurations iOS
│   └── info_plist_config.xml
├── flutter/                     # Services Flutter
│   ├── deep_link_service.dart
│   └── payment_deep_link_handler.dart
└── backend/                     # Fichiers PHP backend
    ├── wave_callback.php
    ├── cinetpay_callback.php
    └── payment_redirect.php

## Schéma des Deep Links

L'application utilise le schéma personnalisé `callitris://` pour les deep links.

### URLs de deep links supportées:

1. **Succès de paiement:**
   - `callitris://payment/success?transaction_id={id}&command_id={id}&client_id={id}`

2. **Échec de paiement:**
   - `callitris://payment/failure?transaction_id={id}&reason={reason}`

3. **Paiement en attente:**
   - `callitris://payment/pending?transaction_id={id}`

## Configuration du serveur

Domaine: `https://api.callitris-distribution.com`

### URLs de callback backend:
- Wave: `{domain}/deep_links_config/backend/wave_callback.php`
- CinetPay: `{domain}/deep_links_config/backend/cinetpay_callback.php`

## Installation

1. Copier les configurations Android et iOS dans les fichiers appropriés
2. Ajouter les services Flutter au projet
3. Déployer les fichiers PHP sur le serveur
4. Configurer les URLs de callback dans les plateformes de paiement

## Test

### Tests Manuels

Pour tester les deep links en développement:

```bash
# Android - Test de succès
adb shell am start -W -a android.intent.action.VIEW -d "callitris://payment/success?transaction_id=test123&command_id=cmd456&client_id=client789" com.callitris.pro

# Android - Test d'échec
adb shell am start -W -a android.intent.action.VIEW -d "callitris://payment/failure?transaction_id=test123&reason=Paiement%20annulé" com.callitris.pro

# Android - Test en attente
adb shell am start -W -a android.intent.action.VIEW -d "callitris://payment/pending?transaction_id=test123" com.callitris.pro

# iOS (Simulator) - Test de succès
xcrun simctl openurl booted "callitris://payment/success?transaction_id=test123&command_id=cmd456&client_id=client789"

# iOS (Simulator) - Test d'échec
xcrun simctl openurl booted "callitris://payment/failure?transaction_id=test123&reason=Paiement%20annulé"
```

### Tests des Callbacks Backend

```bash
# Test Wave callback
curl -X POST https://api.callitris-distribution.com/deep_links_config/backend/wave_callback.php \
  -H "Content-Type: application/json" \
  -d '{"id":"test123","status":"successful","amount":1000,"currency":"XOF"}'

# Test CinetPay callback
curl -X POST https://api.callitris-distribution.com/deep_links_config/backend/cinetpay_callback.php \
  -H "Content-Type: application/json" \
  -d '{"cpm_trans_id":"test123","cpm_result":"00","cpm_amount":1000}'

# Test redirection générique
curl -X GET "https://api.callitris-distribution.com/deep_links_config/backend/payment_redirect.php?provider=wave&status=success&transaction_id=test123"
```

### Tests Automatisés

Utilisez le widget de test intégré dans l'application:

```dart
// Ajouter temporairement dans une page de développement
PaymentDeepLinkIntegration.buildTestButton(context)
```

## Monitoring et Logs

### Logs Backend
- Wave: `/backend/logs/wave_callback.log`
- CinetPay: `/backend/logs/cinetpay_callback.log`
- Général: `/backend/logs/payment_redirect.log`

### Logs Flutter
```dart
// Activer les logs détaillés
debugPrint('Deep link traité: $uri');
```

### Métriques à Surveiller
- Taux de succès des redirections
- Temps de réponse des callbacks
- Erreurs de parsing des deep links
- Échecs de lancement d'application
