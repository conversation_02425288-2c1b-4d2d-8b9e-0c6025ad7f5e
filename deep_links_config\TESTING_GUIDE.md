# Guide de Test des Deep Links Callitris

Ce guide détaille comment tester les deep links dans différents environnements.

## 🧪 Types de Tests

### 1. Tests Unitaires

#### Test du Service Deep Link
```dart
// test/services/deep_link_manager_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:callitris/services/deep_link_manager.dart';

void main() {
  group('DeepLinkManager Tests', () {
    test('generatePaymentDeepLink should create valid URL', () {
      final deepLink = DeepLinkManager.generatePaymentDeepLink(
        status: 'success',
        transactionId: 'test123',
        commandId: 'cmd456',
        clientId: 'client789',
      );
      
      expect(deepLink, equals('callitris://payment/success?transaction_id=test123&command_id=cmd456&client_id=client789'));
    });
    
    test('generatePaymentDeepLink should handle missing parameters', () {
      final deepLink = DeepLinkManager.generatePaymentDeepLink(
        status: 'failure',
        transactionId: 'test123',
        reason: 'Payment cancelled',
      );
      
      expect(deepLink, contains('callitris://payment/failure'));
      expect(deepLink, contains('transaction_id=test123'));
      expect(deepLink, contains('reason=Payment%20cancelled'));
    });
  });
}
```

#### Test de l'Intégration Paiement
```dart
// test/services/payment_integration_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:callitris/services/payment_deep_link_integration.dart';

void main() {
  group('PaymentDeepLinkIntegration Tests', () {
    test('enhanceWavePaymentData should add callback URLs', () {
      final originalData = {
        'amount': '1000',
        'phone': '1234567890',
      };
      
      final enhancedData = PaymentDeepLinkIntegration.enhanceWavePaymentData(
        originalData,
        commandId: 'cmd123',
        clientId: 'client456',
      );
      
      expect(enhancedData['success_url'], isNotNull);
      expect(enhancedData['failure_url'], isNotNull);
      expect(enhancedData['client_reference'], isNotNull);
    });
  });
}
```

### 2. Tests d'Intégration

#### Test Complet de Flux de Paiement
```dart
// test/integration/payment_flow_test.dart
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:callitris/main.dart' as app;

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();
  
  group('Payment Flow Integration Tests', () {
    testWidgets('Complete payment flow with deep link return', (tester) async {
      app.main();
      await tester.pumpAndSettle();
      
      // 1. Naviguer vers la page de paiement
      // 2. Initier un paiement
      // 3. Simuler le retour via deep link
      // 4. Vérifier que l'app gère correctement le deep link
      
      // Simuler un deep link de succès
      await tester.binding.defaultBinaryMessenger.handlePlatformMessage(
        'flutter/navigation',
        const StandardMethodCodec().encodeMethodCall(
          const MethodCall('routeUpdated', {
            'location': 'callitris://payment/success?transaction_id=test123',
            'state': null,
          }),
        ),
        (data) {},
      );
      
      await tester.pumpAndSettle();
      
      // Vérifier que le dialog de succès s'affiche
      expect(find.text('Paiement Réussi'), findsOneWidget);
    });
  });
}
```

### 3. Tests Manuels

#### Tests sur Appareil Physique

**Android:**
```bash
# 1. Installer l'app
flutter install

# 2. Tester les deep links
adb shell am start -W -a android.intent.action.VIEW \
  -d "callitris://payment/success?transaction_id=test123&command_id=cmd456&client_id=client789" \
  com.callitris.pro

# 3. Vérifier les logs
adb logcat | grep -i "callitris\|deeplink"
```

**iOS:**
```bash
# 1. Installer l'app sur simulateur
flutter install

# 2. Tester les deep links
xcrun simctl openurl booted \
  "callitris://payment/success?transaction_id=test123&command_id=cmd456&client_id=client789"

# 3. Vérifier dans Xcode Console
```

#### Tests des Callbacks Backend

**Test Wave Callback:**
```bash
# Test de notification POST
curl -X POST \
  https://api.callitris-distribution.com/deep_links_config/backend/wave_callback.php \
  -H "Content-Type: application/json" \
  -H "X-Wave-Signature: test_signature" \
  -d '{
    "id": "wave_test_123",
    "status": "successful",
    "amount": 1000,
    "currency": "XOF",
    "client_reference": "{\"command_id\":\"cmd123\",\"client_id\":\"client456\"}"
  }'

# Test de redirection GET
curl -L \
  "https://api.callitris-distribution.com/deep_links_config/backend/wave_callback.php?status=success&transaction_id=wave_test_123&command_id=cmd123&client_id=client456"
```

**Test CinetPay Callback:**
```bash
# Test de notification POST
curl -X POST \
  https://api.callitris-distribution.com/deep_links_config/backend/cinetpay_callback.php \
  -H "Content-Type: application/json" \
  -d '{
    "cpm_trans_id": "cinetpay_test_123",
    "cpm_result": "00",
    "cpm_amount": 1000,
    "cpm_currency": "XOF",
    "cpm_custom": "{\"command_id\":\"cmd123\",\"client_id\":\"client456\"}"
  }'

# Test de redirection GET
curl -L \
  "https://api.callitris-distribution.com/deep_links_config/backend/cinetpay_callback.php?cpm_result=00&cmp_trans_id=cinetpay_test_123&command_id=cmd123&client_id=client456"
```

### 4. Tests de Performance

#### Test de Temps de Réponse
```bash
# Mesurer le temps de réponse des callbacks
time curl -X POST \
  https://api.callitris-distribution.com/deep_links_config/backend/wave_callback.php \
  -H "Content-Type: application/json" \
  -d '{"id":"perf_test","status":"successful","amount":1000}'
```

#### Test de Charge
```bash
# Utiliser Apache Bench pour tester la charge
ab -n 100 -c 10 \
  -H "Content-Type: application/json" \
  -p test_payload.json \
  https://api.callitris-distribution.com/deep_links_config/backend/wave_callback.php
```

## 📊 Scénarios de Test

### Scénario 1: Paiement Wave Réussi
1. **Étapes:**
   - Initier un paiement Wave depuis l'app
   - Compléter le paiement sur Wave
   - Vérifier la redirection vers l'app
   - Vérifier l'affichage du succès

2. **Résultat attendu:**
   - Deep link: `callitris://payment/success?transaction_id=xxx&command_id=xxx&client_id=xxx`
   - Dialog de succès affiché
   - Navigation vers la page appropriée

### Scénario 2: Paiement CinetPay Échoué
1. **Étapes:**
   - Initier un paiement CinetPay depuis l'app
   - Annuler le paiement sur CinetPay
   - Vérifier la redirection vers l'app
   - Vérifier l'affichage de l'erreur

2. **Résultat attendu:**
   - Deep link: `callitris://payment/failure?transaction_id=xxx&reason=xxx`
   - Dialog d'erreur affiché
   - Retour à la page de paiement

### Scénario 3: Paiement en Attente
1. **Étapes:**
   - Initier un paiement
   - Simuler un statut "pending"
   - Vérifier la redirection vers l'app
   - Vérifier l'affichage du statut en attente

2. **Résultat attendu:**
   - Deep link: `callitris://payment/pending?transaction_id=xxx`
   - Dialog d'attente affiché
   - Message informatif

### Scénario 4: App Non Installée
1. **Étapes:**
   - Désinstaller l'app
   - Cliquer sur un lien de callback
   - Vérifier l'affichage de la page web de fallback

2. **Résultat attendu:**
   - Page web affichée avec bouton "Ouvrir l'application"
   - Message informatif si l'app n'est pas installée

## 🔍 Debugging

### Logs à Surveiller

**Flutter (Debug Console):**
```
I/flutter: Deep link reçu: callitris://payment/success?transaction_id=test123
I/flutter: Traitement deep link - Scheme: callitris, Host: payment, Path: /success
I/flutter: Paiement réussi - Transaction: test123, Commande: cmd456, Client: client789
```

**Android (Logcat):**
```bash
adb logcat | grep -E "(Intent|callitris|deeplink)"
```

**iOS (Xcode Console):**
```
[app] Deep link received: callitris://payment/success?transaction_id=test123
[app] Processing payment success with transaction: test123
```

**Backend (PHP Logs):**
```bash
tail -f /path/to/backend/logs/wave_callback.log
tail -f /path/to/backend/logs/cinetpay_callback.log
```

### Outils de Debug

1. **Flutter Inspector:** Pour vérifier l'état des widgets
2. **Network Inspector:** Pour vérifier les requêtes HTTP
3. **Device Logs:** Pour vérifier les deep links système
4. **Backend Logs:** Pour vérifier les callbacks

## ✅ Checklist de Test

### Avant Production
- [ ] Tests unitaires passent
- [ ] Tests d'intégration passent
- [ ] Tests manuels sur Android réussis
- [ ] Tests manuels sur iOS réussis
- [ ] Callbacks Wave testés
- [ ] Callbacks CinetPay testés
- [ ] Tests de performance acceptables
- [ ] Logs de production configurés
- [ ] Monitoring en place

### Tests de Régression
- [ ] Paiements existants fonctionnent toujours
- [ ] Navigation normale non affectée
- [ ] Performance générale maintenue
- [ ] Aucune régression sur les autres fonctionnalités

## 🚨 Tests d'Urgence

En cas de problème en production:

1. **Test de Santé Rapide:**
   ```bash
   curl -I https://api.callitris-distribution.com/deep_links_config/backend/payment_redirect.php
   ```

2. **Test Deep Link Basique:**
   ```bash
   adb shell am start -W -a android.intent.action.VIEW -d "callitris://payment/success?transaction_id=emergency_test" com.callitris.pro
   ```

3. **Vérification Logs:**
   ```bash
   tail -n 50 /path/to/backend/logs/*.log
   ```
