import 'package:flutter/foundation.dart';

/// Service de logging pour l'application
class AppLogger {
  static const String _tag = 'Callitris';
  
  /// Log de debug (seulement en mode debug)
  static void debug(String message, [String? tag]) {
    if (kDebugMode) {
      debugPrint('[$_tag${tag != null ? ':$tag' : ''}] DEBUG: $message');
    }
  }
  
  /// Log d'information
  static void info(String message, [String? tag]) {
    if (kDebugMode) {
      debugPrint('[$_tag${tag != null ? ':$tag' : ''}] INFO: $message');
    }
  }
  
  /// Log d'avertissement
  static void warning(String message, [String? tag]) {
    if (kDebugMode) {
      debugPrint('[$_tag${tag != null ? ':$tag' : ''}] WARNING: $message');
    }
  }
  
  /// Log d'erreur
  static void error(String message, [String? tag, dynamic error]) {
    if (kDebugMode) {
      debugPrint('[$_tag${tag != null ? ':$tag' : ''}] ERROR: $message');
      if (error != null) {
        debugPrint('[$_tag${tag != null ? ':$tag' : ''}] ERROR DETAILS: $error');
      }
    }
  }
  
  /// Log pour les deep links
  static void deepLink(String message) {
    debug(message, 'DeepLink');
  }
  
  /// Log pour les paiements
  static void payment(String message) {
    debug(message, 'Payment');
  }
  
  /// Log pour la navigation
  static void navigation(String message) {
    debug(message, 'Navigation');
  }
}
