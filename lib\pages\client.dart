import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'app_bar_navigation.dart';
import 'auth_provider.dart';
import 'package:flutter_svg/svg.dart';
// import 'package:skeletonizer/skeletonizer.dart'; // Supprimé

class ClientPage extends StatefulWidget {
  const ClientPage({super.key});

  @override
  ClientPageState createState() => ClientPageState();
}

class Client {
  final String id;
  final String nom;
  final String prenom;
  final String startDate;
  final String startHeure;
  final String contact;
  final String contact2;
  final String adresse;
  final String? tontine;
  final String? boutique;

  Client({
    required this.id,
    required this.nom,
    required this.prenom,
    required this.startDate,
    required this.startHeure,
    required this.contact,
    required this.contact2,
    required this.adresse,
    required this.tontine,
    required this.boutique,
  });
}

class ClientPageState extends State<ClientPage> {
  String searchText = '';
  bool _isLoading = true;
  List<Client> clients = [];
  final GlobalKey<ScaffoldMessengerState> _scaffoldMessengerKey =
      GlobalKey<ScaffoldMessengerState>();

  void _showSafeSnackBar(String message,
      {Color backgroundColor = Colors.red, Duration? duration}) {
    if (!mounted) {
      print('Widget non monté, SnackBar ignoré: $message');
      return;
    }

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;

      try {
        if (_scaffoldMessengerKey.currentState != null) {
          _scaffoldMessengerKey.currentState!.showSnackBar(
            SnackBar(
              content: Text(message),
              backgroundColor: backgroundColor,
              duration: duration ?? const Duration(seconds: 3),
            ),
          );
          return;
        }

        // Stratégie 2: Utiliser le contexte si le widget est encore monté
        if (mounted && context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(message),
              backgroundColor: backgroundColor,
              duration: duration ?? const Duration(seconds: 3),
            ),
          );
          return;
        }
      } catch (e) {
        print('Erreur lors de l\'affichage du SnackBar: $e');
        print('Message: $message');
      }
    });
  }

  @override
  void initState() {
    super.initState();
    fetchClients();
  }

  Future<void> _registerClientCompte(String clientId, int typeCompte) async {
    try {
      final provide = Provider.of<AuthProvider>(context, listen: false);
      final token = Provider.of<AuthProvider>(context, listen: false).token;

      final Map<String, dynamic> requestBody = {
        'typeCompte': typeCompte,
        'clientId': clientId,
      };

      final response = await http.post(
        Uri.parse(provide.getEndpoint('client/addCompte.php')),
        body: json.encode(requestBody),
        headers: {'Authorization': token!, 'Content-Type': 'application/json'},
      );

      final Map<String, dynamic> responseData = jsonDecode(response.body);

      if (response.statusCode == 200) {
        _showSafeSnackBar(
          responseData['message'],
          backgroundColor: Colors.green,
        );
        if (mounted) {
          Navigator.pushNamed(context, '/client');
        }
      } else {
        _showSafeSnackBar(responseData['message']);
      }
    } catch (error) {
      _showSafeSnackBar('Une erreur est survenue lors de l\'enregistrement.');
    }
  }

  Future<void> fetchClients() async {
    try {
      final provide = Provider.of<AuthProvider>(context, listen: false);
      final user = Provider.of<AuthProvider>(context, listen: false).user;
      final token = Provider.of<AuthProvider>(context, listen: false).token;

      String idPersonnel = user!['id_personnel'].toString();

      final response = await http.get(
        Uri.parse(provide
            .getEndpoint('client/getClient.php?id_personnel=$idPersonnel')),
        headers: {'Authorization': token!, 'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final List<dynamic> responseData = jsonDecode(response.body);

        List<Client> fetchedClients = responseData.map((clientData) {
          String id = clientData['id_client'].toString();
          String nom = clientData['nom_client'].toString();
          String prenom = clientData['prenom_client'].toString();
          String contact = clientData['telephone_client'].toString();
          String contact2 = clientData['telephone2_client'].toString();
          String adresse = clientData['domicile_client'].toString();
          String startDate = clientData['date_ajout'].toString();
          String startHeure = clientData['heure_ajout'].toString();
          String tontine = clientData['tontine'].toString();
          String boutique = clientData['boutique'].toString();

          return Client(
            id: id,
            nom: nom,
            prenom: prenom,
            startDate: startDate,
            startHeure: startHeure,
            contact: contact,
            contact2: contact2,
            adresse: adresse,
            tontine: tontine,
            boutique: boutique,
          );
        }).toList();

        setState(() {
          clients = fetchedClients;
          _isLoading = false;
        });
      } else {
        setState(() {
          _isLoading = false;
        });
        print('Erreur : ${response.statusCode}');
      }
    } catch (error) {
      setState(() {
        _isLoading = false;
      });
      print('Erreur lors de la récupération des clients: $error');
    }
  }

  @override
  Widget build(BuildContext context) {
    List<String> searchTerms = searchText.toLowerCase().split(' ');

    // Filtrer les clients en fonction des mots de recherche
    List<Client> displayedClients = clients.where((client) {
      // Vérifier si tous les mots de recherche correspondent à un client
      return searchTerms.every((term) {
        return client.nom.toLowerCase().contains(term) ||
            client.prenom.toLowerCase().contains(term) ||
            client.contact.toLowerCase().contains(term);
      });
    }).toList();
    return ScaffoldMessenger(
      key: _scaffoldMessengerKey,
      child: MyAppBarNavigation(
        currentIndex: 1,
        onDestinationSelected: (int index) {
          switch (index) {
            case 0:
              Navigator.pushNamed(context, '/home');
              break;
            case 2:
              Navigator.pushNamed(context, '/services');
              break;
            case 3:
              Navigator.pushNamed(context, '/profile');
              break;
          }
        },
        body: Scaffold(
          backgroundColor: Colors.grey[50],
          body: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header simple et discret
              Container(
                color: Colors.grey[50],
                child: SafeArea(
                  child: Padding(
                    padding: const EdgeInsets.all(20.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Mes Clients',
                                    style: TextStyle(
                                      fontSize: 24,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.grey[800],
                                    ),
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    '${displayedClients.length} client${displayedClients.length > 1 ? 's' : ''} trouvé${displayedClients.length > 1 ? 's' : ''}',
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: Colors.grey[600],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Container(
                              decoration: BoxDecoration(
                                color: Colors.blue.shade50,
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: Colors.blue.shade200,
                                  width: 1,
                                ),
                              ),
                              child: IconButton(
                                onPressed: () {
                                  Navigator.pushNamed(context, '/add_client');
                                },
                                icon: Icon(
                                  Icons.person_add,
                                  color: Colors.blue.shade600,
                                  size: 24,
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        // Barre de recherche simple
                        Container(
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: Colors.grey.shade300,
                              width: 1,
                            ),
                          ),
                          child: TextFormField(
                            onChanged: (value) {
                              setState(() {
                                searchText = value;
                              });
                            },
                            decoration: InputDecoration(
                              hintText: 'Rechercher un client...',
                              hintStyle: TextStyle(color: Colors.grey[500]),
                              prefixIcon: Padding(
                                padding: const EdgeInsets.all(12.0),
                                child: SvgPicture.asset(
                                  'assets/icons/search.svg',
                                  width: 18.0,
                                  colorFilter: ColorFilter.mode(
                                    Colors.grey[500]!,
                                    BlendMode.srcIn,
                                  ),
                                ),
                              ),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12.0),
                                borderSide: BorderSide.none,
                              ),
                              filled: true,
                              fillColor: Colors.white,
                              contentPadding: const EdgeInsets.symmetric(
                                  vertical: 14.0, horizontal: 16.0),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              // Message si aucun client trouvé
              if (displayedClients.isEmpty && searchText.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.all(20.0),
                  child: Center(
                    child: Column(
                      children: [
                        Icon(
                          Icons.search_off,
                          size: 64,
                          color: Colors.grey[400],
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Aucun client trouvé',
                          style: TextStyle(
                            fontSize: 18,
                            color: Colors.grey[600],
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Essayez avec d\'autres mots-clés',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[500],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              Expanded(
                child: _isLoading
                    ? ListView.builder(
                        itemCount: 5, // Nombre arbitraire de squelettes
                        itemBuilder: (context, index) {
                          // Remplacer Skeletonizer par un widget de chargement simple
                          return Padding(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 16.0, vertical: 8.0),
                            child: Card(
                              color: const Color.fromARGB(255, 152, 208, 253),
                              elevation: 4.0,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(10.0),
                              ),
                              child: ListTile(
                                leading: CircleAvatar(
                                  backgroundColor: Colors.white,
                                  child: Container(
                                    width: 30.0,
                                    height: 30.0,
                                    color: Colors.grey[300],
                                  ),
                                ),
                                title: Container(
                                  width: double.infinity,
                                  height: 20.0,
                                  color: Colors.grey[300],
                                ),
                                subtitle: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Container(
                                      width: double.infinity,
                                      height: 16.0,
                                      color: Colors.grey[300],
                                    ),
                                    const SizedBox(height: 4.0),
                                    Container(
                                      width: double.infinity,
                                      height: 16.0,
                                      color: Colors.grey[300],
                                    ),
                                    const SizedBox(height: 4.0),
                                    Container(
                                      width: double.infinity,
                                      height: 16.0,
                                      color: Colors.grey[300],
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          );
                        },
                      )
                    : ListView.builder(
                        padding: const EdgeInsets.all(16.0),
                        itemCount: displayedClients.length,
                        itemBuilder: (context, index) {
                          final client = displayedClients[index];
                          return Padding(
                            padding: const EdgeInsets.only(bottom: 16.0),
                            child: ModernClientCard(
                              client: client,
                              onBoutiquePressed: () =>
                                  _makeBoutique(context, client.id),
                              onTontinePressed: () =>
                                  _makeTontine(context, client.id),
                            ),
                          );
                        },
                      ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _makeTontine(BuildContext context, String clientId) {
    Client? client = clients.firstWhere((client) => client.id == clientId);

    if (client.tontine == '1') {
      _showSafeSnackBar('Le client ${client.nom} a déjà un compte tontine.');
    } else {
      _registerClientCompte(clientId, 2);
    }
  }

  void _makeBoutique(BuildContext context, String clientId) {
    Client? client = clients.firstWhere((client) => client.id == clientId);

    if (client.boutique == '1') {
      _showSafeSnackBar('Le client ${client.nom} a déjà un compte boutique.');
    } else {
      _registerClientCompte(clientId, 1);
    }
  }
}

// Widget moderne pour les cartes de clients
class ModernClientCard extends StatelessWidget {
  final Client client;
  final VoidCallback onBoutiquePressed;
  final VoidCallback onTontinePressed;

  const ModernClientCard({
    super.key,
    required this.client,
    required this.onBoutiquePressed,
    required this.onTontinePressed,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.grey.shade200,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(14),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header avec nom et statuts
            Row(
              children: [
                // Avatar avec initiales
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [Colors.blue.shade400, Colors.blue.shade600],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Center(
                    child: Text(
                      '${client.nom[0]}${client.prenom[0]}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${client.nom} ${client.prenom}',
                        style: const TextStyle(
                          fontSize: 15,
                          fontWeight: FontWeight.w600,
                          color: Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Row(
                        children: [
                          Icon(
                            Icons.phone,
                            size: 14,
                            color: Colors.grey[500],
                          ),
                          const SizedBox(width: 4),
                          Text(
                            client.contact,
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                // Statuts avec icônes
                Row(
                  children: [
                    _buildModernStatusBadge(
                      Icons.store,
                      client.boutique == '1'
                          ? Colors.green
                          : Colors.grey.shade400,
                      'Boutique ${client.boutique == '1' ? 'active' : 'inactive'}',
                    ),
                    const SizedBox(width: 8),
                    _buildModernStatusBadge(
                      Icons.savings,
                      client.tontine == '1'
                          ? Colors.purple
                          : Colors.grey.shade400,
                      'Tontine ${client.tontine == '1' ? 'active' : 'inactive'}',
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 12),
            // Informations détaillées
            Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                children: [
                  _buildInfoRow(
                      Icons.location_on_outlined, 'Adresse', client.adresse),
                  const SizedBox(height: 8),
                  _buildInfoRow(
                      Icons.phone_outlined, 'Contact proche', client.contact2),
                  const SizedBox(height: 8),
                  _buildInfoRow(Icons.calendar_today_outlined, 'Créé le',
                      '${client.startDate} à ${client.startHeure}'),
                ],
              ),
            ),
            const SizedBox(height: 12),
            // Boutons d'action élégants
            Row(
              children: [
                Expanded(
                  child: _buildElegantButton(
                    'Boutique',
                    client.boutique == '1' ? Colors.green : Colors.orange,
                    client.boutique == '1'
                        ? Icons.check_circle_outline
                        : Icons.store_outlined,
                    onBoutiquePressed,
                    client.boutique == '1',
                  ),
                ),
                const SizedBox(width: 10),
                Expanded(
                  child: _buildElegantButton(
                    'Tontine',
                    client.tontine == '1' ? Colors.green : Colors.purple,
                    client.tontine == '1'
                        ? Icons.check_circle_outline
                        : Icons.savings_outlined,
                    onTontinePressed,
                    client.tontine == '1',
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildModernStatusBadge(IconData icon, Color color, String tooltip) {
    return Tooltip(
      message: tooltip,
      child: Container(
        width: 28,
        height: 28,
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: color.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Icon(
          icon,
          size: 16,
          color: color,
        ),
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: Colors.grey[500],
        ),
        const SizedBox(width: 8),
        Text(
          '$label: ',
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
            fontWeight: FontWeight.w500,
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(
              fontSize: 12,
              color: Colors.black87,
              fontWeight: FontWeight.w500,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Widget _buildElegantButton(String text, Color color, IconData icon,
      VoidCallback onPressed, bool isActive) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Material(
        color: isActive ? color.withValues(alpha: 0.1) : Colors.transparent,
        borderRadius: BorderRadius.circular(8),
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(8),
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 12),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  icon,
                  size: 16,
                  color: color,
                ),
                const SizedBox(width: 6),
                Text(
                  text,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: color,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
