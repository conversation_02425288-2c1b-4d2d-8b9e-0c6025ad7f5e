name: callitris
description: "Callitris Pro - Application de gestion commerciale avec tontine."
publish_to: 'none'
version: 1.1.2

environment:
  sdk: '>=3.2.3 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  mobile_scanner: ^7.0.1
  flutter_svg: ^2.0.9
  google_fonts: ^6.2.1
  image_picker: ^1.1.2
  image: ^4.1.7
  http: ^1.2.0
  path: ^1.8.3
  provider: ^6.1.2
  cached_network_image: ^3.3.1
  connectivity_plus: ^6.0.1
  shared_preferences: ^2.2.2
  
  flutter_launcher_icons: ^0.14.0
  shimmer: ^3.0.0
  syncfusion_flutter_charts: ^30.1.37
  file_picker: ^10.2.0
  intl: ^0.20.2
  url_launcher: ^6.3.1
  geolocator: ^14.0.2
  permission_handler: ^12.0.1
  geocoding: ^4.0.0
  app_links: ^6.4.0
  fl_chart: ^0.65.0
  rename: ^3.1.0
  webview_flutter: ^4.13.0

flutter_launcher_icons:
  android: true
  ios: true
  image_path: "assets/logo.png"
  min_sdk_android: 21
dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^6.0.0

flutter:
  uses-material-design: true
  assets:
    - assets/
    - assets/images/
    - assets/icons/
    - assets/logo.jpeg
    - assets/logo.png
    - assets/logo_callitris.png
    - assets/macket.jpeg


fonts:
  - family : Poppins
    fonts:
      - asset: fonts/Poppins-Bold.ttf
        weight: 700
      - asset: fonts/Poppins-Medium.ttf
        weight: 500
      - asset: fonts/Poppins-Regular.ttf
        weight: 400
      - asset: fonts/Poppins-SemiBold.ttf
        weight: 600
      
