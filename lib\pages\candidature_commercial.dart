import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:provider/provider.dart';
import 'auth_provider.dart';

class CandidatureCommercialPage extends StatefulWidget {
  const CandidatureCommercialPage({super.key});

  @override
  State<CandidatureCommercialPage> createState() =>
      _CandidatureCommercialPageState();
}

class _CandidatureCommercialPageState extends State<CandidatureCommercialPage> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final TextEditingController _nomController = TextEditingController();
  final TextEditingController _prenomsController = TextEditingController();
  final TextEditingController _ageController = TextEditingController();
  final TextEditingController _contact1Controller = TextEditingController();
  final TextEditingController _contact2Controller = TextEditingController();
  final TextEditingController _contact3Controller = TextEditingController();
  final TextEditingController _lieuResidenceController =
      TextEditingController();

  bool _isLoading = false;
  String? _selectedGenre;

  final List<String> _genres = ['homme', 'femme'];
  final TextEditingController _niveauxEtudeController = TextEditingController();

  @override
  void dispose() {
    _nomController.dispose();
    _prenomsController.dispose();
    _ageController.dispose();
    _contact1Controller.dispose();
    _contact2Controller.dispose();
    _contact3Controller.dispose();
    _niveauxEtudeController.dispose();
    _lieuResidenceController.dispose();
    super.dispose();
  }

  Future<void> _soumettreCandidate() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final provide = Provider.of<AuthProvider>(context, listen: false);

      final Map<String, dynamic> candidatureData = {
        'nom': _nomController.text.trim(),
        'prenoms': _prenomsController.text.trim(),
        'age': _ageController.text.trim(),
        'genre': _selectedGenre,
        'contact1': _contact1Controller.text.trim(),
        'contact2': _contact2Controller.text.trim(),
        'contact3': _contact3Controller.text.trim(),
        'niveau_etude': _niveauxEtudeController.text.trim(),
        'lieu_residence': _lieuResidenceController.text.trim(),
        'date_candidature': DateTime.now().toIso8601String(),
      };

      final response = await http.post(
        Uri.parse(provide.getEndpoint('candidature/soumettre_candidature.php')),
        headers: {'Content-Type': 'application/json'},
        body: json.encode(candidatureData),
      );

      if (response.statusCode == 200) {
        try {
          final responseData = json.decode(response.body);
          print('Parsed response: $responseData');

          // Vérifier si c'est un succès (code 200 ou success = true)
          if (responseData['code'] == 200 || responseData['success'] == true) {
            _showSuccessDialog();
          } else {
            if (mounted) {
              _showErrorMessage(
                  responseData['message'] ?? 'Erreur lors de la soumission');
            }
          }
        } catch (e) {
          print('Error parsing JSON: $e');
          if (mounted) {
            _showErrorMessage('Erreur de format de réponse du serveur');
          }
        }
      } else {
        if (mounted) {
          _showErrorMessage(
              'Erreur de connexion au serveur (${response.statusCode})');
        }
      }
    } catch (error) {
      if (mounted) {
        _showErrorMessage('Erreur: $error');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showSuccessDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: Colors.green.shade100,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Icon(
                  Icons.check_circle,
                  color: Colors.green,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              const Expanded(
                child: Text(
                  'Candidature envoyée !',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
                ),
              ),
            ],
          ),
          content: const Text(
            'Votre candidature a été soumise avec succès. Nous vous contacterons bientôt.',
            style: TextStyle(fontSize: 16),
          ),
          actions: [
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _clearAllFields();
                Navigator.of(context).pop();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
              child: const Text(
                'OK',
                style: TextStyle(color: Colors.white),
              ),
            ),
          ],
        );
      },
    );
  }

  void _clearAllFields() {
    _nomController.clear();
    _prenomsController.clear();
    _ageController.clear();
    _contact1Controller.clear();
    _contact2Controller.clear();
    _contact3Controller.clear();
    _niveauxEtudeController.clear();
    _lieuResidenceController.clear();

    setState(() {
      _selectedGenre = null;
    });
  }

  void _showErrorMessage(String message) {
    if (!mounted) return;

    try {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 4),
        ),
      );
    } catch (e) {
      print('Error showing snackbar: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          'Candidature Commercial',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color.fromARGB(255, 247, 194, 115),
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [Colors.orange, Color.fromARGB(255, 255, 187, 114)],
                  ),
                  borderRadius: BorderRadius.circular(15),
                ),
                child: Column(
                  children: [
                    Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(40),
                      ),
                      child: const Icon(
                        Icons.business_center,
                        size: 40,
                        color: Colors.orange,
                      ),
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'Rejoignez notre équipe !',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'Remplissez ce formulaire pour postuler comme commercial',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.white70,
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 30),

              _buildSectionTitle('Informations personnelles'),
              const SizedBox(height: 16),

              _buildTextField(
                controller: _nomController,
                label: 'Nom *',
                icon: Icons.person_outline,
                validator: (value) =>
                    value?.isEmpty == true ? 'Nom requis' : null,
              ),

              const SizedBox(height: 16),

              _buildTextField(
                controller: _prenomsController,
                label: 'Prénoms *',
                icon: Icons.person_outline,
                validator: (value) =>
                    value?.isEmpty == true ? 'Prénoms requis' : null,
              ),

              const SizedBox(height: 16),

              _buildTextField(
                controller: _ageController,
                label: 'Âge *',
                icon: Icons.cake_outlined,
                keyboardType: TextInputType.number,
                validator: (value) =>
                    value?.isEmpty == true ? 'Âge requis' : null,
              ),

              const SizedBox(height: 16),

              _buildDropdown(
                value: _selectedGenre,
                label: 'Genre *',
                icon: Icons.wc_outlined,
                items: _genres,
                onChanged: (value) => setState(() => _selectedGenre = value),
                validator: (value) => value == null ? 'Genre requis' : null,
              ),

              const SizedBox(height: 16),

              _buildTextField(
                  controller: _contact1Controller,
                  label: 'Contact 1 *',
                  icon: Icons.phone_outlined,
                  keyboardType: TextInputType.phone,
                  hintText: 'Contact 1',
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Contact requis';
                    }
                    if (!RegExp(r'^\d{8,14}$').hasMatch(value)) {
                      return 'Numéro invalide';
                    }
                    return null;
                  }),

              const SizedBox(height: 16),

              _buildTextField(
                  controller: _contact2Controller,
                  label: 'Contact 2 *',
                  icon: Icons.phone_outlined,
                  keyboardType: TextInputType.phone,
                  hintText: 'Contact 2 ',
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Contact requis';
                    }
                    if (!RegExp(r'^\d{8,14}$').hasMatch(value)) {
                      return 'Numéro invalide';
                    }
                    return null;
                  }),

              const SizedBox(height: 16),

              _buildTextField(
                  controller: _contact3Controller,
                  label: 'Contact 3',
                  icon: Icons.contact_phone_outlined,
                  keyboardType: TextInputType.phone,
                  hintText: 'Contact 3 (optionnel)',
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) return null;
                    if (!RegExp(r'^\d{8,14}$').hasMatch(value)) {
                      return 'Numéro invalide';
                    }
                    return null;
                  }),

              const SizedBox(height: 16),

              _buildTextField(
                controller: _niveauxEtudeController,
                label: 'Niveau d\'études *',
                icon: Icons.school_outlined,
                hintText: 'Votre niveau d\'études',
                validator: (value) =>
                    value?.isEmpty == true ? 'Niveau d\'études requis' : null,
              ),

              const SizedBox(height: 16),

              _buildTextField(
                controller: _lieuResidenceController,
                label: 'Lieu de résidence *',
                icon: Icons.location_on_outlined,
                hintText: 'Ville, quartier ou commune',
                validator: (value) =>
                    value?.isEmpty == true ? 'Lieu de résidence requis' : null,
              ),

              const SizedBox(height: 40),

              // Bouton de soumission
              SizedBox(
                width: double.infinity,
                height: 56,
                child: ElevatedButton(
                  onPressed: _isLoading ? null : _soumettreCandidate,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(15),
                    ),
                    elevation: 3,
                  ),
                  child: _isLoading
                      ? const CircularProgressIndicator(color: Colors.white)
                      : const Text(
                          'Soumettre ma candidature',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                ),
              ),

              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: const TextStyle(
        fontSize: 20,
        fontWeight: FontWeight.bold,
        color: Colors.black87,
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    String? hintText,
    TextInputType? keyboardType,
    int maxLines = 1,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      maxLines: maxLines,
      validator: validator,
      decoration: InputDecoration(
        labelText: label,
        hintText: hintText,
        prefixIcon: Icon(icon, color: Colors.blue),
        filled: true,
        fillColor: Colors.white,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Colors.orange, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Colors.red),
        ),
        labelStyle: TextStyle(color: Colors.grey.shade700),
      ),
    );
  }

  Widget _buildDropdown({
    required String? value,
    required String label,
    required IconData icon,
    required List<String> items,
    required void Function(String?) onChanged,
    String? Function(String?)? validator,
  }) {
    return DropdownButtonFormField<String>(
      value: value,
      onChanged: onChanged,
      validator: validator,
      decoration: InputDecoration(
        labelText: label,
        prefixIcon: Icon(icon, color: Colors.blue),
        filled: true,
        fillColor: Colors.white,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Colors.orange, width: 2),
        ),
        labelStyle: TextStyle(color: Colors.grey.shade700),
      ),
      items: items.map((String item) {
        return DropdownMenuItem<String>(
          value: item,
          child: Text(item),
        );
      }).toList(),
    );
  }
}
