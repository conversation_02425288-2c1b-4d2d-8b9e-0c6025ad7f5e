import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:geocoding/geocoding.dart';
import 'package:shared_preferences/shared_preferences.dart';

Future<Map<String, dynamic>?> getPositionAndAddress() async {
  var status = await Permission.location.status;
  if (status.isDenied || status.isRestricted || status.isPermanentlyDenied) {
    status = await Permission.location.request();
    if (!status.isGranted) {
      print("Permission refusée.");
      return null;
    }
  }

  bool isEnabled = await Geolocator.isLocationServiceEnabled();
  if (!isEnabled) {
    print("La localisation est désactivée.");
    return null;
  }

  try {
    Position position = await Geolocator.getCurrentPosition(
      desiredAccuracy: LocationAccuracy.high,
    );

    print("Position: ${position.latitude}, ${position.longitude}");

    List<Placemark> placemarks = await placemarkFromCoordinates(
      position.latitude,
      position.longitude,
    );

    if (placemarks.isNotEmpty) {
      Placemark place = placemarks.first;

      Map<String, dynamic> jsonResult = {
        'latitude': position.latitude,
        'longitude': position.longitude,
        'rue': place.street ?? '',
        'ville': place.locality ?? '',
        'region': place.administrativeArea ?? '',
        'pays': place.country ?? '',
        'codePostal': place.postalCode ?? '',
      };

      print("Adresse récupérée : $jsonResult");
      return jsonResult;
    } else {
      print("Aucune adresse trouvée.");
      return null;
    }
  } catch (e) {
    print("Erreur : $e");
    return null;
  }
}

void saveData(String name, String data) async {
  SharedPreferences prefs = await SharedPreferences.getInstance();
  await prefs.setString(name, data);
}

Future<String?> readData(String name) async {
  SharedPreferences prefs = await SharedPreferences.getInstance();
  String? token = prefs.getString(name);
  return token;
}

void deleteData(String name) async {
  SharedPreferences prefs = await SharedPreferences.getInstance();
  prefs.remove(name);
}
