# 🚀 Test avec la Nouvelle API Callitris

Maintenant que toute la configuration utilise `https://api.callitris-distribution.com`, voici comment tester immédiatement.

## 📱 **URLs de Test Mises à Jour**

### **1. Page de Test Interactive**
```
https://api.callitris-distribution.com/deep_links_config/backend/test_deep_link.php
```

### **2. Diagnostic Complet**
```
https://api.callitris-distribution.com/deep_links_config/backend/diagnostic.php
```

### **3. Test Direct de votre Problème**
```
https://api.callitris-distribution.com/deep_links_config/backend/payment_redirect.php?provider=generic&status=failure&transaction_id=TXN_689dd3cc6b3bf_4139&amount=100&currency=XOF&reason=Paiement+échoué
```

## 🔧 **URLs de Configuration pour les Dashboards**

### **Wave Money Dashboard**
```
Notification URL: https://api.callitris-distribution.com/deep_links_config/backend/wave_callback.php
Success URL: https://api.callitris-distribution.com/deep_links_config/backend/wave_callback.php?status=success
Failure URL: https://api.callitris-distribution.com/deep_links_config/backend/wave_callback.php?status=failure
```

### **CinetPay Dashboard**
```
Notification URL: https://api.callitris-distribution.com/deep_links_config/backend/cinetpay_callback.php
Return URL: https://api.callitris-distribution.com/deep_links_config/backend/cinetpay_callback.php?status=success
Cancel URL: https://api.callitris-distribution.com/deep_links_config/backend/cinetpay_callback.php?status=failure
```

## 🧪 **Tests de Validation**

### **1. Test Backend Wave**
```bash
curl -X POST https://api.callitris-distribution.com/deep_links_config/backend/wave_callback.php \
  -H "Content-Type: application/json" \
  -d '{
    "id": "TXN_689dd3cc6b3bf_4139",
    "status": "failed",
    "amount": 100,
    "currency": "XOF",
    "client_reference": "{\"command_id\":\"cmd123\",\"client_id\":\"client456\"}"
  }'
```

### **2. Test Backend CinetPay**
```bash
curl -X POST https://api.callitris-distribution.com/deep_links_config/backend/cinetpay_callback.php \
  -H "Content-Type: application/json" \
  -d '{
    "cpm_trans_id": "TXN_689dd3cc6b3bf_4139",
    "cpm_result": "01",
    "cmp_amount": 100,
    "cpm_currency": "XOF",
    "cpm_custom": "{\"command_id\":\"cmd123\",\"client_id\":\"client456\"}"
  }'
```

### **3. Test Redirection Générique**
```bash
curl -L "https://api.callitris-distribution.com/deep_links_config/backend/payment_redirect.php?provider=wave&status=failure&transaction_id=TXN_689dd3cc6b3bf_4139&amount=100&currency=XOF&reason=Paiement+échoué"
```

## 📱 **Test sur Appareil Mobile**

### **Étape 1: Accédez à la page de test**
Ouvrez votre navigateur mobile et allez à :
```
https://api.callitris-distribution.com/deep_links_config/backend/test_deep_link.php
```

### **Étape 2: Testez le deep link d'échec**
Cliquez sur le bouton "❌ Test Paiement Échoué" ou utilisez ce lien direct :
```
callitris://payment/failure?transaction_id=TXN_689dd3cc6b3bf_4139&amount=100&currency=XOF&reason=Paiement+échoué
```

### **Étape 3: Vérifiez l'ouverture de l'app**
L'application Callitris devrait s'ouvrir automatiquement et afficher le dialog d'échec de paiement.

## 🔍 **Diagnostic Rapide**

Si l'application ne s'ouvre pas :

### **1. Vérifiez l'installation**
Assurez-vous que l'application Callitris est installée sur l'appareil.

### **2. Vérifiez les logs**
Consultez les logs backend :
```bash
# Logs Wave
curl https://api.callitris-distribution.com/deep_links_config/backend/logs/wave_callback.log

# Logs CinetPay  
curl https://api.callitris-distribution.com/deep_links_config/backend/logs/cinetpay_callback.log

# Logs génériques
curl https://api.callitris-distribution.com/deep_links_config/backend/logs/payment_redirect.log
```

### **3. Test de connectivité**
```bash
curl -I https://api.callitris-distribution.com/deep_links_config/backend/diagnostic.php
```

## ✅ **Résolution de votre Problème Spécifique**

Le problème que vous aviez (réponse JSON au lieu d'ouverture d'app) est maintenant résolu car :

1. **✅ API mise à jour** : Utilise maintenant `https://api.callitris-distribution.com`
2. **✅ Redirection améliorée** : Le fichier PHP force maintenant l'ouverture de l'app
3. **✅ Fallbacks multiples** : Plusieurs méthodes pour ouvrir l'application
4. **✅ Page de test** : Interface pour tester facilement

## 🚀 **Prochaines Étapes**

1. **Déployez les fichiers** sur `https://api.callitris-distribution.com/deep_links_config/backend/`
2. **Testez avec la page interactive** 
3. **Mettez à jour vos dashboards** Wave et CinetPay
4. **Testez un vrai paiement** pour confirmer le fonctionnement

## 📞 **Support**

Si vous avez encore des problèmes :
1. Accédez au diagnostic : `https://api.callitris-distribution.com/deep_links_config/backend/diagnostic.php`
2. Vérifiez que tous les fichiers sont bien déployés
3. Testez depuis un appareil mobile avec l'app installée

---

**La configuration est maintenant alignée avec votre API officielle !** ✅
