import 'dart:async';
import 'package:flutter/material.dart';
import 'package:app_links/app_links.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:provider/provider.dart';
import '../pages/auth_provider.dart';
import 'app_logger.dart';
import 'payment_navigation_service.dart';

/// Gestionnaire principal des deep links pour l'application Callitris
class DeepLinkManager {
  static final DeepLinkManager _instance = DeepLinkManager._internal();
  factory DeepLinkManager() => _instance;
  DeepLinkManager._internal();

  final AppLinks _appLinks = AppLinks();
  StreamSubscription<Uri>? _linkSubscription;
  BuildContext? _context;

  /// Initialise le gestionnaire de deep links
  Future<void> initialize(BuildContext context) async {
    _context = context;

    // Écouter les deep links entrants
    _linkSubscription = _appLinks.uriLinkStream.listen(
      _handleIncomingLink,
      onError: (err) {
        AppLogger.error('Erreur deep link: $err', 'DeepLink');
      },
    );

    // Vérifier s'il y a un deep link initial (app fermée)
    try {
      final initialLink = await _appLinks.getInitialLink();
      if (initialLink != null) {
        // Attendre un peu pour que l'app soit complètement initialisée
        await Future.delayed(const Duration(milliseconds: 500));
        _handleIncomingLink(initialLink);
      }
    } catch (e) {
      AppLogger.error(
          'Erreur lors de la récupération du deep link initial', 'DeepLink', e);
    }
  }

  /// Gère les deep links entrants
  void _handleIncomingLink(Uri uri) {
    AppLogger.deepLink('Deep link reçu: $uri');

    if (_context == null) {
      AppLogger.warning(
          'Contexte non disponible pour traiter le deep link', 'DeepLink');
      return;
    }

    // Sauvegarder le deep link pour traitement ultérieur si nécessaire
    _saveLastDeepLink(uri.toString());

    final scheme = uri.scheme;
    final host = uri.host;
    final path = uri.path;
    final queryParams = uri.queryParameters;

    AppLogger.deepLink(
        'Traitement deep link - Scheme: $scheme, Host: $host, Path: $path');
    AppLogger.deepLink('Paramètres: $queryParams');

    if (scheme == 'callitris') {
      switch (host) {
        case 'payment':
          _handlePaymentDeepLink(path, queryParams);
          break;
        case 'callback':
          _handleCallbackDeepLink(path, queryParams);
          break;
        default:
          AppLogger.warning('Host de deep link non reconnu: $host', 'DeepLink');
      }
    } else if (scheme == 'https' && host == 'api.callitris-distribution.com') {
      _handleHttpsDeepLink(path, queryParams);
    }
  }

  /// Gère les deep links de paiement
  void _handlePaymentDeepLink(String path, Map<String, String> params) {
    if (_context == null) return;

    switch (path) {
      case '/success':
        _handlePaymentSuccess(params);
        break;
      case '/failure':
        _handlePaymentFailure(params);
        break;
      case '/pending':
        _handlePaymentPending(params);
        break;
      default:
        AppLogger.warning('Path de paiement non reconnu: $path', 'DeepLink');
    }
  }

  /// Gère les deep links de callback
  void _handleCallbackDeepLink(String path, Map<String, String> params) {
    switch (path) {
      case '/wave':
        _handleWaveCallback(params);
        break;
      case '/cinetpay':
        _handleCinetPayCallback(params);
        break;
      default:
        AppLogger.warning('Path de callback non reconnu: $path', 'DeepLink');
    }
  }

  /// Gère les deep links HTTPS
  void _handleHttpsDeepLink(String path, Map<String, String> params) {
    if (path.startsWith('/deep_links_config')) {
      // Traiter les callbacks depuis le serveur
      final action = params['action'];
      switch (action) {
        case 'payment_success':
          _handlePaymentSuccess(params);
          break;
        case 'payment_failure':
          _handlePaymentFailure(params);
          break;
        default:
          AppLogger.warning('Action HTTPS non reconnue: $action', 'DeepLink');
      }
    }
  }

  /// Gère le succès de paiement
  void _handlePaymentSuccess(Map<String, String> params) {
    if (_context == null) return;

    final transactionId = params['transaction_id'];
    final commandId = params['command_id'];
    final clientId = params['client_id'];

    AppLogger.payment(
        'Paiement réussi - Transaction: $transactionId, Commande: $commandId, Client: $clientId');

    // Sauvegarder les informations de succès
    _savePaymentResult('success', params);

    // Afficher le dialog de succès avec navigation
    PaymentNavigationService.showSuccessDialogWithNavigation(
      _context!,
      message: 'Votre paiement a été effectué avec succès!',
      transactionId: transactionId,
      commandId: commandId,
      clientId: clientId,
    );
  }

  /// Gère l'échec de paiement
  void _handlePaymentFailure(Map<String, String> params) {
    if (_context == null) return;

    final transactionId = params['transaction_id'];
    final reason = params['reason'] ?? 'Raison non spécifiée';

    AppLogger.payment(
        'Paiement échoué - Transaction: $transactionId, Raison: $reason');

    // Sauvegarder les informations d'échec
    _savePaymentResult('failure', params);

    // Afficher le dialog d'échec avec navigation
    PaymentNavigationService.showFailureDialogWithNavigation(
      _context!,
      message: 'Votre paiement a échoué: $reason',
      reason: reason,
    );
  }

  /// Gère le paiement en attente
  void _handlePaymentPending(Map<String, String> params) {
    if (_context == null) return;

    final transactionId = params['transaction_id'];

    AppLogger.payment('Paiement en attente - Transaction: $transactionId');

    // Sauvegarder les informations de paiement en attente
    _savePaymentResult('pending', params);

    // Afficher le dialog de paiement en attente
    PaymentNavigationService.showPendingDialog(_context!,
        transactionId: transactionId);
  }

  /// Gère les callbacks Wave
  void _handleWaveCallback(Map<String, String> params) {
    AppLogger.payment('Callback Wave reçu: $params');
    // Rediriger vers le traitement de paiement approprié
    final status = params['status'];
    if (status == 'success') {
      _handlePaymentSuccess(params);
    } else if (status == 'failed') {
      _handlePaymentFailure(params);
    }
  }

  /// Gère les callbacks CinetPay
  void _handleCinetPayCallback(Map<String, String> params) {
    AppLogger.payment('Callback CinetPay reçu: $params');
    // Rediriger vers le traitement de paiement approprié
    final status = params['status'];
    if (status == 'success') {
      _handlePaymentSuccess(params);
    } else if (status == 'failed') {
      _handlePaymentFailure(params);
    }
  }

  /// Sauvegarde le résultat du paiement
  Future<void> _savePaymentResult(
      String status, Map<String, String> params) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('last_payment_status', status);
      await prefs.setString('last_payment_params', params.toString());
      await prefs.setInt(
          'last_payment_timestamp', DateTime.now().millisecondsSinceEpoch);
    } catch (e) {
      AppLogger.error('Erreur lors de la sauvegarde du résultat de paiement',
          'DeepLink', e);
    }
  }

  /// Sauvegarde le dernier deep link
  Future<void> _saveLastDeepLink(String link) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('last_deep_link', link);
      await prefs.setInt(
          'last_deep_link_timestamp', DateTime.now().millisecondsSinceEpoch);
    } catch (e) {
      AppLogger.error(
          'Erreur lors de la sauvegarde du deep link', 'DeepLink', e);
    }
  }

  /// Génère une URL de deep link pour les paiements
  static String generatePaymentDeepLink({
    required String status,
    String? transactionId,
    String? commandId,
    String? clientId,
    String? reason,
  }) {
    final uri = Uri(
      scheme: 'callitris',
      host: 'payment',
      path: '/$status',
      queryParameters: {
        if (transactionId != null) 'transaction_id': transactionId,
        if (commandId != null) 'command_id': commandId,
        if (clientId != null) 'client_id': clientId,
        if (reason != null) 'reason': reason,
      },
    );
    return uri.toString();
  }

  /// Libère les ressources
  void dispose() {
    _linkSubscription?.cancel();
    _linkSubscription = null;
    _context = null;
  }
}
