import 'package:callitris/pages/auth_provider.dart';
import 'package:callitris/pages/menu.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;
import 'package:intl/intl.dart';
import 'dart:convert';

import 'package:provider/provider.dart';

import '../services/scanner_encaissement.dart';

class VersementTontinePage extends StatefulWidget {
  final String id;
  final String client;
  const VersementTontinePage(
      {super.key, required this.id, required this.client});

  @override
  _VersementTontinePageState createState() => _VersementTontinePageState();
}

class Versement {
  final String journalier;
  final String verser;
  final String reelVerser;
  final String monnaieReste;
  final String monnaieReel;
  final String date;
  final String heure;

  Versement({
    required this.journalier,
    required this.verser,
    required this.reelVerser,
    required this.monnaieReste,
    required this.monnaieReel,
    required this.date,
    required this.heure,
  });
}

class _VersementTontinePageState extends State<VersementTontinePage> {
  double montantEpargne = 0;
  List<Versement> versements = [];
  List<Versement> filteredVersements = [];
  late int compteur = 0;

  late TextEditingController _montantController;
  final TextEditingController _searchController = TextEditingController();
  int? solde;
  double? journalier;

  String? libelle;
  double? monnaie;

  final _controllerMontant = TextEditingController();
  final _controllerJours = TextEditingController();
  final NumberFormat _numberFormat = NumberFormat("#,###", "fr_FR");

  bool isSubmitting = false;
  final GlobalKey<ScaffoldMessengerState> _scaffoldMessengerKey =
      GlobalKey<ScaffoldMessengerState>();

  // Méthode utilitaire pour afficher des SnackBars de manière sécurisée
  void _showSafeSnackBar(String message,
      {Color backgroundColor = Colors.red, Duration? duration}) {
    // Vérifier si le widget est encore monté
    if (!mounted) {
      print('Widget non monté, SnackBar ignoré: $message');
      return;
    }

    // Utiliser un délai pour s'assurer que le contexte est stable
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;

      try {
        // Stratégie 1: Utiliser le GlobalKey si disponible
        if (_scaffoldMessengerKey.currentState != null) {
          _scaffoldMessengerKey.currentState!.showSnackBar(
            SnackBar(
              content: Text(message),
              backgroundColor: backgroundColor,
              duration: duration ?? const Duration(seconds: 3),
            ),
          );
          return;
        }

        // Stratégie 2: Utiliser le contexte si le widget est encore monté
        if (mounted && context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(message),
              backgroundColor: backgroundColor,
              duration: duration ?? const Duration(seconds: 3),
            ),
          );
          return;
        }
      } catch (e) {
        print('Erreur lors de l\'affichage du SnackBar: $e');
        print('Message: $message');
      }
    });
  }

  @override
  void initState() {
    super.initState();
    _chargerHistorique();
    _tontineInfo();
    fetchMonnaie();
    _montantController = TextEditingController();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    setState(() {
      if (_searchController.text.isEmpty) {
        filteredVersements = List.from(versements);
      } else {
        final searchTerm = _searchController.text.toLowerCase();
        filteredVersements = versements.where((versement) {
          return versement.date.toLowerCase().contains(searchTerm) ||
              versement.heure.toLowerCase().contains(searchTerm) ||
              versement.journalier.toLowerCase().contains(searchTerm) ||
              versement.verser.toLowerCase().contains(searchTerm) ||
              versement.reelVerser.toLowerCase().contains(searchTerm) ||
              versement.monnaieReste.toLowerCase().contains(searchTerm) ||
              versement.monnaieReel.toLowerCase().contains(searchTerm);
        }).toList();
      }
    });
  }

  void showSuccessDialog(BuildContext context, String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: const Column(
            children: [
              Icon(
                Icons.check_circle,
                color: Colors.green,
                size: 60,
              ),
              SizedBox(height: 16),
              Text(
                "Succès",
                style: TextStyle(fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
              ),
            ],
          ),
          content: Text(
            message,
            textAlign: TextAlign.center,
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text(
                "OK",
                style: TextStyle(fontSize: 16),
              ),
            ),
          ],
        );
      },
    );
  }

  Future<void> fetchMonnaie() async {
    try {
      final token = Provider.of<AuthProvider>(context, listen: false).token;
      final provide = Provider.of<AuthProvider>(context, listen: false);

      final response = await http.get(
        Uri.parse(provide
            .getEndpoint('client/getMonnaie.php?clientId=${widget.client}')),
        headers: {'Authorization': token!, 'Content-Type': 'application/json'},
      );
      //print(response.body);
      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);
        String monnaieValue = responseData['montant'].toString();
        setState(() {
          monnaie = double.parse(monnaieValue);
          print(monnaie);
        });
      } else {
        print(
            'Erreur lors de la récupération de la monnaie : ${response.statusCode}');
      }
    } catch (error) {
      print('Erreur lors de la récupération de la monnaie : $error');
    }
  }

  void _formatMontant() {
    String text = _montantController.text.replaceAll(' ', '');
    if (text.isNotEmpty) {
      int value = int.parse(text);
      _montantController.value = TextEditingValue(
        text: _numberFormat.format(value),
        selection:
            TextSelection.collapsed(offset: _numberFormat.format(value).length),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return ScaffoldMessenger(
      key: _scaffoldMessengerKey,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Versement Tontine'),
          backgroundColor: const Color.fromARGB(255, 249, 221, 175),
        actions: [
          IconButton(
            onPressed: () async {
              final result = await Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const ScannerEncaissement(),
                ),
              );

              if (result != null && mounted) {
                _showSafeSnackBar(
                  "Code scanné : $result",
                  backgroundColor: Colors.green,
                );
              }
            },
            icon: const Icon(Icons.qr_code_scanner),
            tooltip: 'Scanner QR Code',
          ),
          ...buildAppBarActions(context),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            ElevatedButton.icon(
              onPressed: () {
                _showNouvelleTontineSheet(context);
              },
              icon: const Icon(Icons.payment, color: Colors.white),
              label: const Text(
                'Effectuer un versement',
                style: TextStyle(color: Colors.white),
              ),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 12.0),
                backgroundColor: Colors.blue,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10.0),
                ),
              ),
            ),
            const SizedBox(height: 20),
            _buildTontineInfoCard(),
            const SizedBox(height: 20),
            _buildModernDaysGrid(),
            const SizedBox(height: 20),
            _buildModernHistorySection(),
          ],
        ),
      ),
      ),
    );
  }

  Widget _buildTontineInfoCard() {
    if (libelle == null && journalier == null && solde == null) {
      return Container(
        height: 100,
        decoration: BoxDecoration(
          color: Colors.grey.shade100,
          borderRadius: BorderRadius.circular(16),
        ),
        child: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            libelle ?? 'Tontine',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildInfoItem(
                  'Journalier',
                  '${journalier ?? 0} FCFA',
                  Icons.monetization_on,
                  Colors.green,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildInfoItem(
                  'Montant payé',
                  '${solde ?? 0} FCFA',
                  Icons.account_balance_wallet,
                  Colors.blue,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInfoItem(
      String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, size: 16, color: color),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  label,
                  style: TextStyle(
                    fontSize: 12,
                    color: color,
                    fontWeight: FontWeight.w600,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildModernDaysGrid() {
    return SizedBox(
      height: 200,
      child: GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 7,
          crossAxisSpacing: 8,
          mainAxisSpacing: 8,
          childAspectRatio: 1,
        ),
        itemCount: 21,
        itemBuilder: (context, index) {
          int dayNumber = index + 1;
          return _buildDayButton(dayNumber);
        },
      ),
    );
  }

  Widget _buildDayButton(int dayNumber) {
    Color backgroundColor = _getModernColorForButton(dayNumber, compteur);
    Color textColor = Colors.white;

    return Container(
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: backgroundColor.withValues(alpha: 0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: backgroundColor.withValues(alpha: 0.3),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Center(
        child: Text(
          dayNumber.toString(),
          style: TextStyle(
            color: textColor,
            fontWeight: FontWeight.w600,
            fontSize: 14,
          ),
        ),
      ),
    );
  }

  Color _getModernColorForButton(int number, int compteur) {
    if (number <= compteur) {
      return Colors.green.shade400;
    } else if (number == compteur + 1) {
      return Colors.blue.shade400;
    } else {
      return Colors.grey.shade400;
    }
  }

  Widget _buildModernHistorySection() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Icon(
                  Icons.history,
                  color: Colors.indigo.shade600,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Historique du compte',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.indigo.shade700,
                  ),
                ),
                const Spacer(),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade50,
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Text(
                    '${versements.length} ',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.blue.shade700,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
          versements.isEmpty ? _buildEmptyHistoryState() : _buildHistoryList(),
        ],
      ),
    );
  }

  Widget _buildEmptyHistoryState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.receipt_long_outlined,
            size: 48,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            'Aucun versement effectué',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey.shade600,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Les versements apparaîtront ici',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHistoryList() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: versements.map((versement) {
          final index = versements.indexOf(versement);
          return Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: _buildVersementCard(versement, index),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildVersementCard(Versement versement, int index) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: Colors.teal.shade100,
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Center(
                  child: Text(
                    '${index + 1}',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: Colors.teal.shade700,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '${versement.reelVerser} FCFA',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                    Text(
                      '${versement.date} à ${versement.heure}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.check_circle,
                color: Colors.green.shade400,
                size: 20,
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: Text(
                  'Monnaie: ${versement.monnaieReel} FCFA',
                  style: TextStyle(
                    fontSize: 11,
                    color: Colors.grey.shade600,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Reste: ${versement.monnaieReste} FCFA',
                  style: TextStyle(
                    fontSize: 11,
                    color: Colors.grey.shade600,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  String _formatNumber(String s) {
    return _numberFormat.format(int.parse(
        s.replaceAll('.', '').replaceAll(' ', '').replaceAll('\u202F', '')));
  }

///////

  bool useMonnaie = false;

  void _showNouvelleTontineSheet(BuildContext context) {
    double montantJ = 0;
    int montantSaisi = 0;
    int nbrePaye = 0;
    int quotient = 0;
    int monnaieExact = 0;
    double resteMonnaie = 0;

    void recalculerMontants(StateSetter updateState) {
      try {
        montantJ = journalier ?? 0;
        String amount = _controllerMontant.text
            .replaceAll('.', '')
            .replaceAll(' ', '')
            .replaceAll('\u202F', '');
        montantSaisi = amount.isNotEmpty ? int.parse(amount) : 0;

        double monnaieToUse = useMonnaie ? (monnaie ?? 0) : 0;

        if (montantSaisi >= montantJ && (resteMonnaie + monnaie!) < montantJ) {
          useMonnaie = false;
          monnaieToUse = 0;
        }
        if (montantJ > 0 && montantSaisi >= (montantJ - monnaieToUse)) {
          resteMonnaie = (montantSaisi + monnaieToUse) % montantJ;
          quotient = (montantSaisi + monnaieToUse) ~/ montantJ;
        } else {
          resteMonnaie = 0;
          quotient = 0;
        }

        updateState(() {});
        monnaieExact =
            (useMonnaie ? ((quotient * montantJ.toInt()) - montantSaisi) : 0);
      } catch (e) {
        resteMonnaie = 0;
        quotient = 0;
        monnaieExact = 0;
        updateState(() {});
      }
    }

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) {
        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setState) {
            return SingleChildScrollView(
              padding: EdgeInsets.only(
                bottom: MediaQuery.of(context).viewInsets.bottom,
              ),
              child: Container(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Text(
                      'Versement Tontine',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 20,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Utiliser la monnaie de $monnaie F',
                          style: const TextStyle(fontSize: 20),
                        ),
                        Switch(
                          value: useMonnaie,
                          onChanged: (bool value) {
                            setState(() {
                              useMonnaie = value;
                              recalculerMontants(setState);
                            });
                          },
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: Container(
                            margin: const EdgeInsets.symmetric(horizontal: 8.0),
                            child: TextFormField(
                              controller: _controllerMontant,
                              inputFormatters: [
                                FilteringTextInputFormatter.digitsOnly,
                                TextInputFormatter.withFunction(
                                  (oldValue, newValue) {
                                    String newText =
                                        _formatNumber(newValue.text);
                                    return TextEditingValue(
                                      text: newText,
                                      selection: TextSelection.collapsed(
                                          offset: newText.length),
                                    );
                                  },
                                ),
                              ],
                              keyboardType: TextInputType.number,
                              onChanged: (value) {
                                recalculerMontants(setState);
                              },
                              decoration: const InputDecoration(
                                border: OutlineInputBorder(
                                  borderSide: BorderSide(color: Colors.orange),
                                ),
                                labelText: 'Montant à verser',
                              ),
                              style: const TextStyle(fontSize: 16),
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Container(
                            margin: const EdgeInsets.symmetric(horizontal: 8.0),
                            child: TextFormField(
                              controller: _controllerJours,
                              keyboardType: TextInputType.number,
                              decoration: const InputDecoration(
                                border: OutlineInputBorder(
                                  borderSide: BorderSide(color: Colors.orange),
                                ),
                                labelText: 'Jours à payer',
                              ),
                              style: const TextStyle(fontSize: 16),
                              onChanged: (value) {
                                try {
                                  nbrePaye = int.parse(value);
                                  setState(() {});
                                  print('Nombre payer  : $nbrePaye');
                                } catch (e) {
                                  print(
                                      'Erreur lors de la conversion de la valeur nombre de jours: $e');
                                }
                              },
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 20),
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(16.0),
                      color: Colors.grey,
                      child: Text(
                        'Jours Correspondants : $quotient Jour(s)',
                        style:
                            const TextStyle(fontSize: 16, color: Colors.white),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    const SizedBox(height: 10),
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(16.0),
                      color: Colors.grey,
                      child: Text(
                        'Monnaie restante : $resteMonnaie FCFA',
                        style:
                            const TextStyle(fontSize: 16, color: Colors.white),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () {
                        if (!useMonnaie &&
                            (resteMonnaie + monnaie!) > montantJ) {
                          showDialog<void>(
                            context: context,
                            barrierDismissible: false,
                            builder: (BuildContext context) {
                              return AlertDialog(
                                content: const Column(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      Icons.error,
                                      color: Colors.red,
                                      size: 48.0,
                                    ),
                                    SizedBox(height: 16.0),
                                    Text(
                                      'Le total des monnaies dépasse le montant journalier. La monnaie doit etre utilisée.',
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                ),
                                actions: [
                                  TextButton(
                                    onPressed: () {
                                      Navigator.pop(context);
                                    },
                                    child: const Text('OK'),
                                  ),
                                ],
                              );
                            },
                          );
                          return;
                        }
                        if (montantSaisi >= 0 &&
                            (quotient == nbrePaye) &&
                            nbrePaye > 0) {
                          _envoyerMontant(
                              montantSaisi, resteMonnaie, monnaieExact);
                          Navigator.pop(context);
                        } else {
                          Navigator.pop(context);
                          showDialog<void>(
                            context: context,
                            barrierDismissible: false,
                            builder: (BuildContext context) {
                              return AlertDialog(
                                content: const Column(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      Icons.error,
                                      color: Colors.red,
                                      size: 48.0,
                                    ),
                                    SizedBox(height: 16.0),
                                    Text(
                                      'Veuillez entrer le montant et le nombre de jours correspondant',
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                ),
                                actions: [
                                  TextButton(
                                    onPressed: () {
                                      Navigator.pop(context);
                                    },
                                    child: const Text('OK'),
                                  ),
                                ],
                              );
                            },
                          );
                        }
                        setState(() {
                          _montantController.clear();
                          _controllerJours.clear();
                          _controllerMontant.clear();
                          resteMonnaie = 0;
                          quotient = 0;
                          useMonnaie = false;
                        });
                      },
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(
                            vertical: 10.0, horizontal: 25),
                        backgroundColor: Colors.blue,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10.0),
                        ),
                      ),
                      child: const Text(
                        'Enregistrer',
                        style: TextStyle(color: Colors.white),
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

///////

  void _envoyerMontant(montantSaisi, resteMonnaie, monnaieExact) async {
    setState(() {
      isSubmitting = true;
    });
    try {
      final provide = Provider.of<AuthProvider>(context, listen: false);
      final user = Provider.of<AuthProvider>(context, listen: false).user;
      final token = Provider.of<AuthProvider>(context, listen: false).token;

      String idPersonnel = user!['id_personnel'].toString();

      Map<String, dynamic> requestBody = {
        'montantSaisi': montantSaisi,
        'monnaieExact': monnaieExact,
        'restMonnaie': resteMonnaie,
        'clientId': widget.client,
        'tontine_id': widget.id,
        'personnelId': idPersonnel,
        'monnaie': monnaie,
      };
      if (montantSaisi + monnaieExact < (journalier ?? 0)) {
        showDialog<void>(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext context) {
            return AlertDialog(
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(
                    Icons.error,
                    color: Colors.red,
                    size: 48.0,
                  ),
                  const SizedBox(height: 16.0),
                  Text(
                    'Le montant doit etre superieur ou egal au montant journalier $journalier F',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  child: const Text('OK'),
                ),
              ],
            );
          },
        );

        _showSafeSnackBar(
          'Le montant doit etre superieur ou egal au montant journalier $journalier F',
        );
        return;
      }
      final response = await http.post(
        Uri.parse(provide.getEndpoint('products/addVersementTontine.php')),
        headers: {
          'Authorization': token!,
          'Content-Type': 'application/json',
        },
        body: jsonEncode(requestBody),
      );
      //print(response.body);
      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);
        //print(responseData['code']);

        if (responseData['code'] == 201) {
          if (mounted) {
            setState(() {
              isSubmitting = false;
            });
          }
          _showSafeSnackBar(responseData['message']);
        } else {
          if (mounted) {
            setState(() {
              isSubmitting = false;
            });
          }
          if (mounted) {
            showSuccessDialog(context, responseData['message']);
          }
          _showSafeSnackBar(
            responseData['message'],
            backgroundColor: Colors.green,
          );
          if (mounted) {
            setState(() {
              _chargerHistorique();
              _tontineInfo();
              fetchMonnaie();
            });
          }
        }
      } else {
        print('Erreur lors de l\'envoi du montant: ${response.body}');
      }
    } catch (error) {
      print('Erreur lors de l\'envoi du montant: $error');
    } finally {
      if (mounted) {
        setState(() {
          isSubmitting = false;
        });
      }
    }
  }

  List<DataRow> _buildRows() {
    List<DataRow> rows = [];
    for (int i = 0; i < versements.length; i++) {
      Versement versement = versements[i];
      rows.add(
        DataRow(cells: [
          DataCell(Text(versement.reelVerser)),
          DataCell(Text(versement.monnaieReel)),
          DataCell(Text(versement.monnaieReste)),
          DataCell(Text(versement.date)),
          DataCell(Text(versement.heure)),
        ]),
      );
    }
    return rows;
  }

  Future<void> _tontineInfo() async {
    try {
      final provide = Provider.of<AuthProvider>(context, listen: false);
      final token = Provider.of<AuthProvider>(context, listen: false).token;

      final response = await http.get(
        Uri.parse(provide
            .getEndpoint('products/getTontineById.php?tontineId=${widget.id}')),
        headers: {'Authorization': token!, 'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> tontineData = jsonDecode(response.body);

        setState(() {
          compteur = int.parse(tontineData['compteur_col']);
          journalier = double.parse(tontineData['journalier_col']);
          solde = (compteur * journalier!).toInt();
          libelle = tontineData['libelle_col'].toString();
        });
      } else {
        print(
            'Erreur lors de la récupération des informations de la tontine: ${response.statusCode}');
      }
    } catch (error) {
      print('Erreur lors du chargement de tontine: $error');
    }
  }

  void _chargerHistorique() async {
    try {
      final provide = Provider.of<AuthProvider>(context, listen: false);
      final token = Provider.of<AuthProvider>(context, listen: false).token;
      final versementsResponse = await http.get(
        Uri.parse(provide.getEndpoint(
            'products/getVersementsCompte.php?compte_id=2&id=${widget.id}')),
        headers: {'Authorization': token!, 'Content-Type': 'application/json'},
      );

      if (versementsResponse.statusCode == 200) {
        final List<dynamic> data = jsonDecode(versementsResponse.body);
        List<Versement> historiqueVersements = data.map((versData) {
          String journalier = versData['montant_vers'].toString();
          String verser = versData['montant_vers'].toString();
          String reelVerser = versData['montant_saisi'].toString();
          String monnaieReste = versData['monnaie_reste'].toString();
          String monnaieReel = versData['monnaie'].toString();
          String date = versData['date_vers'].toString();
          String heure = versData['heure_vers'].toString();

          return Versement(
              journalier: journalier,
              verser: verser,
              reelVerser: reelVerser,
              monnaieReste: monnaieReste,
              monnaieReel: monnaieReel,
              date: date,
              heure: heure);
        }).toList();

        setState(() {
          compteur = compteur;

          versements = historiqueVersements;
        });
      } else {
        print(
            'Erreur lors du chargement de l\'historique des versements: ${versementsResponse.statusCode}');
      }
    } catch (error) {
      print('Erreur lors du chargement de l\'historique: $error');
    }
  }

  Color getColorForButton(int number, int compteur) {
    if (number <= compteur) {
      return Colors.green;
    } else if (number == compteur + 1) {
      return Colors.blue;
    } else {
      return Colors.red;
    }
  }

  Color getColorForText(int number, int compteur) {
    if (number <= compteur) {
      return Colors.green;
    } else if (number == compteur + 1) {
      return Colors.blue;
    } else {
      return Colors.red;
    }
  }

  Widget _buildSearchBar() {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'Rechercher dans l\'historique...',
          hintStyle: TextStyle(color: Colors.grey[500]),
          prefixIcon: Icon(Icons.search, color: Colors.grey[500]),
          suffixIcon: _searchController.text.isNotEmpty
              ? IconButton(
                  icon: Icon(Icons.clear, color: Colors.grey[500]),
                  onPressed: () {
                    _searchController.clear();
                  },
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide.none,
          ),
          filled: true,
          fillColor: Colors.white,
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
        ),
      ),
    );
  }
}
