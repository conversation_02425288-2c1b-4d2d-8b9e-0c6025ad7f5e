<?php
/**
 * Diagnostic des deep links Callitris
 * Vérifiez que tout est correctement configuré
 */

header('Content-Type: text/html; charset=utf-8');

// Fonction pour vérifier si un fichier existe et est lisible
function checkFile($path, $description) {
    $fullPath = __DIR__ . '/' . $path;
    $exists = file_exists($fullPath);
    $readable = $exists && is_readable($fullPath);
    $size = $exists ? filesize($fullPath) : 0;
    
    return [
        'path' => $path,
        'description' => $description,
        'exists' => $exists,
        'readable' => $readable,
        'size' => $size,
        'status' => $exists && $readable ? 'OK' : 'ERREUR'
    ];
}

// Fonction pour vérifier les permissions d'un dossier
function checkDirectory($path, $description) {
    $fullPath = __DIR__ . '/' . $path;
    $exists = is_dir($fullPath);
    $writable = $exists && is_writable($fullPath);
    
    return [
        'path' => $path,
        'description' => $description,
        'exists' => $exists,
        'writable' => $writable,
        'status' => $exists && $writable ? 'OK' : ($exists ? 'LECTURE SEULE' : 'MANQUANT')
    ];
}

// Vérifications des fichiers
$fileChecks = [
    checkFile('wave_callback.php', 'Callback Wave Money'),
    checkFile('cinetpay_callback.php', 'Callback CinetPay'),
    checkFile('payment_redirect.php', 'Redirection générique'),
    checkFile('config.php', 'Configuration (optionnel)'),
    checkFile('test_deep_link.php', 'Page de test'),
];

// Vérifications des dossiers
$dirChecks = [
    checkDirectory('logs', 'Dossier des logs'),
    checkDirectory('.', 'Dossier backend'),
];

// Vérification de la configuration PHP
$phpInfo = [
    'version' => phpversion(),
    'curl' => extension_loaded('curl'),
    'json' => extension_loaded('json'),
    'openssl' => extension_loaded('openssl'),
    'allow_url_fopen' => ini_get('allow_url_fopen'),
    'max_execution_time' => ini_get('max_execution_time'),
    'memory_limit' => ini_get('memory_limit'),
];

// Test de connectivité
function testConnectivity() {
    $testUrls = [
        'https://api.wave.com' => 'Wave API',
        'https://api-checkout.cinetpay.com' => 'CinetPay API',
        'https://www.google.com' => 'Internet général',
    ];
    
    $results = [];
    foreach ($testUrls as $url => $description) {
        $start = microtime(true);
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_NOBODY, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        
        $result = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        $time = round((microtime(true) - $start) * 1000, 2);
        
        $results[] = [
            'url' => $url,
            'description' => $description,
            'success' => $result !== false && $httpCode < 400,
            'http_code' => $httpCode,
            'error' => $error,
            'time' => $time . 'ms'
        ];
    }
    
    return $results;
}

$connectivityTests = testConnectivity();

// Informations sur le serveur
$serverInfo = [
    'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Non défini',
    'document_root' => $_SERVER['DOCUMENT_ROOT'] ?? 'Non défini',
    'script_name' => $_SERVER['SCRIPT_NAME'] ?? 'Non défini',
    'request_uri' => $_SERVER['REQUEST_URI'] ?? 'Non défini',
    'http_host' => $_SERVER['HTTP_HOST'] ?? 'Non défini',
    'https' => isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off',
    'remote_addr' => $_SERVER['REMOTE_ADDR'] ?? 'Non défini',
    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Non défini',
];

?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Diagnostic Deep Links Callitris</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .section { background: white; margin: 20px 0; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .header { text-align: center; color: #333; }
        .status-ok { color: #4CAF50; font-weight: bold; }
        .status-warning { color: #ff9800; font-weight: bold; }
        .status-error { color: #f44336; font-weight: bold; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f2f2f2; }
        .code { background: #f0f0f0; padding: 10px; border-radius: 4px; font-family: monospace; overflow-x: auto; }
        .button { display: inline-block; padding: 10px 20px; background: #2196F3; color: white; text-decoration: none; border-radius: 4px; margin: 5px; }
        .summary { display: flex; justify-content: space-around; text-align: center; }
        .summary-item { padding: 20px; }
        .summary-number { font-size: 2em; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Diagnostic Deep Links Callitris</h1>
            <p>Vérification de la configuration et du fonctionnement</p>
            <p><em>Généré le <?php echo date('Y-m-d H:i:s'); ?></em></p>
        </div>

        <div class="section">
            <h2>📊 Résumé</h2>
            <div class="summary">
                <div class="summary-item">
                    <div class="summary-number status-ok"><?php echo count(array_filter($fileChecks, fn($f) => $f['status'] === 'OK')); ?></div>
                    <div>Fichiers OK</div>
                </div>
                <div class="summary-item">
                    <div class="summary-number status-ok"><?php echo count(array_filter($dirChecks, fn($d) => $d['status'] === 'OK')); ?></div>
                    <div>Dossiers OK</div>
                </div>
                <div class="summary-item">
                    <div class="summary-number status-ok"><?php echo count(array_filter($connectivityTests, fn($c) => $c['success'])); ?></div>
                    <div>Connexions OK</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>📁 Vérification des Fichiers</h2>
            <table>
                <thead>
                    <tr>
                        <th>Fichier</th>
                        <th>Description</th>
                        <th>Taille</th>
                        <th>Statut</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($fileChecks as $check): ?>
                    <tr>
                        <td><code><?php echo htmlspecialchars($check['path']); ?></code></td>
                        <td><?php echo htmlspecialchars($check['description']); ?></td>
                        <td><?php echo $check['exists'] ? number_format($check['size']) . ' bytes' : '-'; ?></td>
                        <td class="status-<?php echo $check['status'] === 'OK' ? 'ok' : 'error'; ?>">
                            <?php echo $check['status']; ?>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <div class="section">
            <h2>📂 Vérification des Dossiers</h2>
            <table>
                <thead>
                    <tr>
                        <th>Dossier</th>
                        <th>Description</th>
                        <th>Statut</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($dirChecks as $check): ?>
                    <tr>
                        <td><code><?php echo htmlspecialchars($check['path']); ?></code></td>
                        <td><?php echo htmlspecialchars($check['description']); ?></td>
                        <td class="status-<?php echo $check['status'] === 'OK' ? 'ok' : ($check['status'] === 'LECTURE SEULE' ? 'warning' : 'error'); ?>">
                            <?php echo $check['status']; ?>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <div class="section">
            <h2>🌐 Test de Connectivité</h2>
            <table>
                <thead>
                    <tr>
                        <th>Service</th>
                        <th>URL</th>
                        <th>Code HTTP</th>
                        <th>Temps</th>
                        <th>Statut</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($connectivityTests as $test): ?>
                    <tr>
                        <td><?php echo htmlspecialchars($test['description']); ?></td>
                        <td><code><?php echo htmlspecialchars($test['url']); ?></code></td>
                        <td><?php echo $test['http_code']; ?></td>
                        <td><?php echo $test['time']; ?></td>
                        <td class="status-<?php echo $test['success'] ? 'ok' : 'error'; ?>">
                            <?php echo $test['success'] ? 'OK' : 'ERREUR'; ?>
                            <?php if (!$test['success'] && $test['error']): ?>
                                <br><small><?php echo htmlspecialchars($test['error']); ?></small>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <div class="section">
            <h2>🐘 Configuration PHP</h2>
            <table>
                <thead>
                    <tr>
                        <th>Paramètre</th>
                        <th>Valeur</th>
                        <th>Statut</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Version PHP</td>
                        <td><?php echo $phpInfo['version']; ?></td>
                        <td class="status-<?php echo version_compare($phpInfo['version'], '7.4', '>=') ? 'ok' : 'warning'; ?>">
                            <?php echo version_compare($phpInfo['version'], '7.4', '>=') ? 'OK' : 'ANCIEN'; ?>
                        </td>
                    </tr>
                    <tr>
                        <td>Extension cURL</td>
                        <td><?php echo $phpInfo['curl'] ? 'Activée' : 'Désactivée'; ?></td>
                        <td class="status-<?php echo $phpInfo['curl'] ? 'ok' : 'error'; ?>">
                            <?php echo $phpInfo['curl'] ? 'OK' : 'MANQUANT'; ?>
                        </td>
                    </tr>
                    <tr>
                        <td>Extension JSON</td>
                        <td><?php echo $phpInfo['json'] ? 'Activée' : 'Désactivée'; ?></td>
                        <td class="status-<?php echo $phpInfo['json'] ? 'ok' : 'error'; ?>">
                            <?php echo $phpInfo['json'] ? 'OK' : 'MANQUANT'; ?>
                        </td>
                    </tr>
                    <tr>
                        <td>Extension OpenSSL</td>
                        <td><?php echo $phpInfo['openssl'] ? 'Activée' : 'Désactivée'; ?></td>
                        <td class="status-<?php echo $phpInfo['openssl'] ? 'ok' : 'warning'; ?>">
                            <?php echo $phpInfo['openssl'] ? 'OK' : 'RECOMMANDÉ'; ?>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="section">
            <h2>🔗 Tests de Deep Links</h2>
            <p>Utilisez ces liens pour tester les deep links directement:</p>
            
            <a href="test_deep_link.php" class="button">📱 Page de Test Interactive</a>
            <a href="test_deep_link.php?status=success" class="button">✅ Test Succès</a>
            <a href="test_deep_link.php?status=failure" class="button">❌ Test Échec</a>
            <a href="test_deep_link.php?status=pending" class="button">⏳ Test En Attente</a>
        </div>

        <div class="section">
            <h2>ℹ️ Informations Serveur</h2>
            <table>
                <tbody>
                    <?php foreach ($serverInfo as $key => $value): ?>
                    <tr>
                        <td><strong><?php echo ucfirst(str_replace('_', ' ', $key)); ?></strong></td>
                        <td><code><?php echo is_bool($value) ? ($value ? 'Oui' : 'Non') : htmlspecialchars($value); ?></code></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <div class="section">
            <h2>📋 URLs de Configuration</h2>
            <p>Utilisez ces URLs dans vos dashboards de paiement:</p>
            
            <h4>Wave Money:</h4>
            <div class="code">
                Notification URL: <?php echo (isset($_SERVER['HTTPS']) ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['SCRIPT_NAME']) . '/wave_callback.php'; ?>
            </div>
            
            <h4>CinetPay:</h4>
            <div class="code">
                Notification URL: <?php echo (isset($_SERVER['HTTPS']) ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['SCRIPT_NAME']) . '/cinetpay_callback.php'; ?>
            </div>
            
            <h4>Générique:</h4>
            <div class="code">
                Callback URL: <?php echo (isset($_SERVER['HTTPS']) ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['SCRIPT_NAME']) . '/payment_redirect.php'; ?>
            </div>
        </div>
    </div>
</body>
</html>
