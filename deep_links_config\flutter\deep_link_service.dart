import 'dart:async';
import 'package:flutter/material.dart';
import 'package:app_links/app_links.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Service de gestion des deep links pour l'application Callitris
class DeepLinkService {
  static final DeepLinkService _instance = DeepLinkService._internal();
  factory DeepLinkService() => _instance;
  DeepLinkService._internal();

  final AppLinks _appLinks = AppLinks();
  StreamSubscription<Uri>? _linkSubscription;
  
  // Callback pour gérer les deep links
  Function(Uri)? _onLinkReceived;

  /// Initialise le service de deep links
  Future<void> initialize({Function(Uri)? onLinkReceived}) async {
    _onLinkReceived = onLinkReceived;
    
    // Écouter les deep links entrants
    _linkSubscription = _appLinks.uriLinkStream.listen(
      _handleIncomingLink,
      onError: (err) {
        print('Erreur deep link: $err');
      },
    );

    // Vérifier s'il y a un deep link initial (app fermée)
    try {
      final initialLink = await _appLinks.getInitialLink();
      if (initialLink != null) {
        _handleIncomingLink(initialLink);
      }
    } catch (e) {
      print('Erreur lors de la récupération du deep link initial: $e');
    }
  }

  /// Gère les deep links entrants
  void _handleIncomingLink(Uri uri) {
    print('Deep link reçu: $uri');
    
    // Sauvegarder le deep link pour traitement ultérieur si nécessaire
    _saveLastDeepLink(uri.toString());
    
    // Appeler le callback si défini
    if (_onLinkReceived != null) {
      _onLinkReceived!(uri);
    } else {
      // Traitement par défaut
      _handleDefaultDeepLink(uri);
    }
  }

  /// Traitement par défaut des deep links
  void _handleDefaultDeepLink(Uri uri) {
    final scheme = uri.scheme;
    final host = uri.host;
    final path = uri.path;
    final queryParams = uri.queryParameters;

    print('Traitement deep link - Scheme: $scheme, Host: $host, Path: $path');
    print('Paramètres: $queryParams');

    if (scheme == 'callitris') {
      switch (host) {
        case 'payment':
          _handlePaymentDeepLink(path, queryParams);
          break;
        case 'callback':
          _handleCallbackDeepLink(path, queryParams);
          break;
        default:
          print('Host de deep link non reconnu: $host');
      }
    }
  }

  /// Gère les deep links de paiement
  void _handlePaymentDeepLink(String path, Map<String, String> params) {
    switch (path) {
      case '/success':
        _handlePaymentSuccess(params);
        break;
      case '/failure':
        _handlePaymentFailure(params);
        break;
      case '/pending':
        _handlePaymentPending(params);
        break;
      default:
        print('Path de paiement non reconnu: $path');
    }
  }

  /// Gère les deep links de callback
  void _handleCallbackDeepLink(String path, Map<String, String> params) {
    switch (path) {
      case '/wave':
        _handleWaveCallback(params);
        break;
      case '/cinetpay':
        _handleCinetPayCallback(params);
        break;
      default:
        print('Path de callback non reconnu: $path');
    }
  }

  /// Gère le succès de paiement
  void _handlePaymentSuccess(Map<String, String> params) {
    final transactionId = params['transaction_id'];
    final commandId = params['command_id'];
    final clientId = params['client_id'];
    
    print('Paiement réussi - Transaction: $transactionId, Commande: $commandId, Client: $clientId');
    
    // Sauvegarder les informations de succès
    _savePaymentResult('success', params);
  }

  /// Gère l'échec de paiement
  void _handlePaymentFailure(Map<String, String> params) {
    final transactionId = params['transaction_id'];
    final reason = params['reason'];
    
    print('Paiement échoué - Transaction: $transactionId, Raison: $reason');
    
    // Sauvegarder les informations d'échec
    _savePaymentResult('failure', params);
  }

  /// Gère le paiement en attente
  void _handlePaymentPending(Map<String, String> params) {
    final transactionId = params['transaction_id'];
    
    print('Paiement en attente - Transaction: $transactionId');
    
    // Sauvegarder les informations de paiement en attente
    _savePaymentResult('pending', params);
  }

  /// Gère les callbacks Wave
  void _handleWaveCallback(Map<String, String> params) {
    print('Callback Wave reçu: $params');
    // Traitement spécifique pour Wave
  }

  /// Gère les callbacks CinetPay
  void _handleCinetPayCallback(Map<String, String> params) {
    print('Callback CinetPay reçu: $params');
    // Traitement spécifique pour CinetPay
  }

  /// Sauvegarde le résultat du paiement
  Future<void> _savePaymentResult(String status, Map<String, String> params) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('last_payment_status', status);
      await prefs.setString('last_payment_params', params.toString());
      await prefs.setInt('last_payment_timestamp', DateTime.now().millisecondsSinceEpoch);
    } catch (e) {
      print('Erreur lors de la sauvegarde du résultat de paiement: $e');
    }
  }

  /// Sauvegarde le dernier deep link
  Future<void> _saveLastDeepLink(String link) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('last_deep_link', link);
      await prefs.setInt('last_deep_link_timestamp', DateTime.now().millisecondsSinceEpoch);
    } catch (e) {
      print('Erreur lors de la sauvegarde du deep link: $e');
    }
  }

  /// Récupère le dernier résultat de paiement
  Future<Map<String, dynamic>?> getLastPaymentResult() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final status = prefs.getString('last_payment_status');
      final params = prefs.getString('last_payment_params');
      final timestamp = prefs.getInt('last_payment_timestamp');
      
      if (status != null && params != null && timestamp != null) {
        return {
          'status': status,
          'params': params,
          'timestamp': timestamp,
        };
      }
    } catch (e) {
      print('Erreur lors de la récupération du résultat de paiement: $e');
    }
    return null;
  }

  /// Nettoie les données de paiement sauvegardées
  Future<void> clearPaymentResult() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('last_payment_status');
      await prefs.remove('last_payment_params');
      await prefs.remove('last_payment_timestamp');
    } catch (e) {
      print('Erreur lors du nettoyage des données de paiement: $e');
    }
  }

  /// Génère une URL de deep link
  static String generatePaymentDeepLink({
    required String status,
    String? transactionId,
    String? commandId,
    String? clientId,
    String? reason,
  }) {
    final uri = Uri(
      scheme: 'callitris',
      host: 'payment',
      path: '/$status',
      queryParameters: {
        if (transactionId != null) 'transaction_id': transactionId,
        if (commandId != null) 'command_id': commandId,
        if (clientId != null) 'client_id': clientId,
        if (reason != null) 'reason': reason,
      },
    );
    return uri.toString();
  }

  /// Libère les ressources
  void dispose() {
    _linkSubscription?.cancel();
    _linkSubscription = null;
    _onLinkReceived = null;
  }
}
