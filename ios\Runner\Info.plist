<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>Callitris Pro</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>Callitris Pro</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UIStatusBarStyle</key>
	<string></string>
	<!-- Configuration des Deep Links -->
	<key>CFBundleURLTypes</key>
	<array>
		<!-- Deep Links pour le schéma callitris:// -->
		<dict>
			<key>CFBundleURLName</key>
			<string>callitris.deeplink</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>callitris</string>
			</array>
			<key>CFBundleURLTypes</key>
			<string>Editor</string>
		</dict>

		<!-- Deep Links pour les paiements -->
		<dict>
			<key>CFBundleURLName</key>
			<string>callitris.payment</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>callitris</string>
			</array>
			<key>CFBundleURLTypes</key>
			<string>Editor</string>
		</dict>

		<!-- Support pour les liens HTTPS du domaine -->
		<dict>
			<key>CFBundleURLName</key>
			<string>callitris.https</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>https</string>
			</array>
			<key>CFBundleURLTypes</key>
			<string>Editor</string>
		</dict>
	</array>

	<!-- Associated Domains pour les liens universels -->
	<key>com.apple.developer.associated-domains</key>
	<array>
		<string>applinks:api.callitris-distribution.com</string>
	</array>

	<!-- Configuration pour les liens universels -->
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
		<key>NSExceptionDomains</key>
		<dict>
			<key>api.callitris-distribution.com</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<false/>
				<key>NSExceptionMinimumTLSVersion</key>
				<string>TLSv1.2</string>
				<key>NSIncludesSubdomains</key>
				<true/>
			</dict>
		</dict>
	</dict>

	<!-- Permissions pour les URL schemes -->
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>callitris</string>
		<string>https</string>
		<string>http</string>
	</array>

	<key>UISupportedInterfaceOrientations</key>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>L'application nécessite votre autorisation pour accéder à votre location.</string>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
</dict>
</plist>
