import 'package:callitris/pages/menu.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:provider/provider.dart';
import 'package:callitris/pages/auth_provider.dart';

class LivraisonPage extends StatefulWidget {
  const LivraisonPage({super.key});

  @override
  _LivraisonPageState createState() => _LivraisonPageState();
}

class Option {
  final String id;
  final String value;

  Option(this.id, this.value);
}

class _LivraisonPageState extends State<LivraisonPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  List<DeliveryInfo> _deliveries = [];
  List<DeliveryInfo> _filteredDeliveries = [];
  bool _isLoading = true;
  final TextEditingController _searchController = TextEditingController();
  Option? selectedChoice;
  List<Option> _planningOptions = [];

  Future<void> _fetchPlanningOptions() async {
    final provide = Provider.of<AuthProvider>(context, listen: false);
    final token = Provider.of<AuthProvider>(context, listen: false).token;
    final response = await http.get(
      Uri.parse(provide.getEndpoint('products/getPlanning.php')),
      headers: {'Authorization': token!, 'Content-Type': 'application/json'},
    );

    if (response.statusCode == 200) {
      final List<dynamic> data = json.decode(response.body);
      final List<Option> options = data.map((item) {
        return Option(
            item['id_campagne_detail'].toString(), item['name_cmp'].toString());
      }).toList();
      setState(() {
        _planningOptions = options;
      });
    }
  }

  @override
  void initState() {
    super.initState();
    _fetchPlanningOptions();
    _searchController.addListener(_onSearchChanged);
    _tabController = TabController(length: 2, vsync: this);
    _tabController.addListener(_onTabChanged);
    fetchDeliveries();
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    _tabController.removeListener(_onTabChanged);
    _tabController.dispose();
    super.dispose();
  }

  void _onTabChanged() {
    setState(() {
      if (_tabController.index == 0) {
        _filteredDeliveries = _deliveries
            .where(
                (delivery) => delivery.finished == 1 && delivery.delivered == 0)
            .toList();
      } else {
        _filteredDeliveries =
            _deliveries.where((delivery) => delivery.delivered == 1).toList();
      }
    });
  }

  void _onSearchChanged() {
    setState(() {
      _filteredDeliveries = _deliveries.where((delivery) {
        final searchValue = _searchController.text.toLowerCase();
        return delivery.commande.contains(searchValue) ||
            delivery.nom.toLowerCase().contains(searchValue) ||
            delivery.dateFin.toLowerCase().contains(searchValue) ||
            delivery.contact.toLowerCase().contains(searchValue) ||
            delivery.prenom.contains(searchValue);
      }).toList();
    });
  }

  Future<void> fetchDeliveries() async {
    try {
      final provide = Provider.of<AuthProvider>(context, listen: false);
      final token = Provider.of<AuthProvider>(context, listen: false).token;
      final user = Provider.of<AuthProvider>(context, listen: false)
          .user;

      final response = await http.get(
        Uri.parse(provide.getEndpoint(
            'products/getLivraison.php?personnel_id=${user?['id_personnel']}')),
        headers: {'Authorization': token!, 'Content-Type': 'application/json'},
      );
      print(response.body);
      if (response.statusCode == 200) {
        final List<dynamic> responseData = jsonDecode(response.body);
        List<DeliveryInfo> fetchedDeliveries = responseData.map((deliveryData) {
          String commande = deliveryData['code_cmd'].toString();
          String dateFin = deliveryData['last_date_add'].toString();
          String nom = deliveryData['nom_client'].toString();
          String prenom = deliveryData['prenom_client'].toString();
          String contact = deliveryData['telephone_client'].toString();
          String choix = deliveryData['choix'] ?? ' ';
          int delivered = int.parse(deliveryData['delivered'].toString());
          int finished = int.parse(deliveryData['finished'].toString());

          return DeliveryInfo(commande, dateFin, nom, prenom, contact, choix,
              delivered, finished);
        }).toList();

        setState(() {
          _deliveries = fetchedDeliveries;
          _filteredDeliveries = List.from(_deliveries);
          _isLoading = false;
        });
      } else {
        print('Erreur : ${response.statusCode}');
      }
    } catch (error) {
      print('Erreur lors de la récupération des livraisons: $error');
    }
  }

  Future<void> _sendPlan(
      String choixId, String commandeId, String campagne) async {
    try {
      final provide = Provider.of<AuthProvider>(context, listen: false);
      final token = Provider.of<AuthProvider>(context, listen: false).token;

      final response = await http.post(
        Uri.parse(provide.getEndpoint('products/addPlan.php')),
        body: jsonEncode({
          'choixId': choixId,
          'commandeId': commandeId,
          'campagne': campagne
        }),
        headers: {'Authorization': token!, 'Content-Type': 'application/json'},
      );
      //print(response.statusCode);
      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              responseData['message'],
              style: const TextStyle(color: Colors.white),
            ),
            backgroundColor: Colors.green,
          ),
        );
        setState(() {
          fetchDeliveries();
        });
      } else {
        print('Erreur : ${response.statusCode}');
      }
    } catch (error) {
      print('Erreur lors de la récupération des livraisons: $error');
    }
  }

  Future<void> _refreshData() async {
    await fetchDeliveries();
    setState(() {
      _filteredDeliveries = List.from(_deliveries);
      print('test');
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: const Color.fromARGB(255, 249, 221, 175),
        title: const Text('Suivi de Livraison'),
        actions: buildAppBarActions(context),
        bottom: TabBar(
          controller: _tabController,
          tabs: [
            Tab(
              child: Text(
                'En attente',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: _tabController.index == 0
                      ? Colors.black
                      : Colors.grey,
                ),
              ),
            ),
            Tab(
              child: Text(
                'Livrées',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: _tabController.index == 1
                      ? Colors.black
                      : Colors.grey,
                ),
              ),
            ),
          ],
        ),
      ),
      body: RefreshIndicator(
        onRefresh: _refreshData,
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : Column(
                children: [
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: TextField(
                      controller: _searchController,
                      decoration: const InputDecoration(
                        labelText: 'Rechercher',
                        prefixIcon: Icon(Icons.search),
                      ),
                    ),
                  ),
                  Expanded(
                    child: TabBarView(
                      controller: _tabController,
                      children: [
                        _buildDeliveryList(_filteredDeliveries
                            .where((delivery) =>
                                delivery.delivered == 0 &&
                                delivery.finished == 1)
                            .toList()),
                        _buildDeliveryList(_filteredDeliveries
                            .where((delivery) => delivery.delivered == 1)
                            .toList()),
                      ],
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  Widget _buildDeliveryList(List<DeliveryInfo> deliveries) {
    if (deliveries.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.local_shipping_outlined,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'Aucune livraison trouvée',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[600],
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Les livraisons apparaîtront ici',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[500],
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: deliveries.length,
      itemBuilder: (context, index) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: ModernDeliveryCard(
            delivery: deliveries[index],
            planningOptions: _planningOptions,
            onPlanSelected: (choixId, commandeId, campagne) {
              _sendPlan(choixId, commandeId, campagne);
            },
          ),
        );
      },
    );
  }

  Widget buildDeliveryCard(DeliveryInfo delivery) {
    String deliveryStatusText;
    Color deliveryStatusColor;

    if (delivery.delivered == 0) {
      deliveryStatusText = 'En attente de livraison';
      deliveryStatusColor =
          Colors.red;
    } else {
      deliveryStatusText = 'Livraison effectuée';
      deliveryStatusColor =
          Colors.green;
    }

    return Card(
      elevation: 4.0,
      margin: const EdgeInsets.all(16.0),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.0),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'N° CMD: ${delivery.commande}',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16.0,
                  ),
                ),
                const SizedBox(height: 8.0),
                Text('Nom: ${delivery.nom}'),
                Text('Prenoms: ${delivery.prenom}'),
                Text('Date de Fin: ${delivery.dateFin}'),
                Text('Contact: ${delivery.contact}'),
                const SizedBox(height: 8.0),
                Row(
                  children: [
                    Icon(Icons.circle, color: deliveryStatusColor, size: 12.0),
                    const SizedBox(width: 4.0),
                    Text(deliveryStatusText,
                        style: TextStyle(
                            fontSize: 12.0, color: deliveryStatusColor)),
                  ],
                ),
              ],
            ),
            const Spacer(),
            Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SvgPicture.asset(
                  'assets/icons/truck-check.svg',
                  colorFilter:
                      const ColorFilter.mode(Colors.blue, BlendMode.srcIn),
                  height: 40,
                ),
                const SizedBox(height: 8.0),
                if (delivery.delivered == 0)
                  delivery.choix != ' '
                      ? Container(
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(8.0),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.grey.withValues(alpha: 0.5),
                                spreadRadius: 2,
                                blurRadius: 3,
                                offset: const Offset(
                                    0, 2),
                              ),
                            ],
                          ),
                          child: Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Text(
                              delivery.choix,
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.blue,
                              ),
                            ),
                          ),
                        )
                      : PopupMenuButton<Option>(
                          child: Container(
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(8.0),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.grey.withValues(alpha: 0.5),
                                  spreadRadius: 2,
                                  blurRadius: 3,
                                  offset: const Offset(
                                      0, 2),
                                ),
                              ],
                            ),
                            padding: const EdgeInsets.all(8.0),
                            child: Text(
                              selectedChoice?.value ?? 'Choisir',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.blue,
                              ),
                            ),
                          ),
                          itemBuilder: (BuildContext context) {
                            return _planningOptions.map((Option option) {
                              return PopupMenuItem<Option>(
                                value: option,
                                child: Container(
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(8.0),
                                    boxShadow: [
                                      BoxShadow(
                                        color:
                                            Colors.grey.withValues(alpha: 0.5),
                                        spreadRadius: 2,
                                        blurRadius: 3,
                                        offset: const Offset(
                                            0, 2),
                                      ),
                                    ],
                                  ),
                                  padding: const EdgeInsets.all(8.0),
                                  child: Text(
                                    option.value,
                                    style: const TextStyle(
                                      fontWeight: FontWeight.bold,
                                      color: Colors.blue,
                                    ),
                                  ),
                                ),
                              );
                            }).toList();
                          },
                          onSelected: (Option choice) {
                            setState(() {
                              selectedChoice = choice;
                              _sendPlan(
                                  choice.id, delivery.commande, choice.value);
                            });

                            //
                          },
                        ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class ModernDeliveryCard extends StatelessWidget {
  final DeliveryInfo delivery;
  final List<Option> planningOptions;
  final Function(String, String, String) onPlanSelected;

  const ModernDeliveryCard({
    super.key,
    required this.delivery,
    required this.planningOptions,
    required this.onPlanSelected,
  });

  @override
  Widget build(BuildContext context) {
    bool isDelivered = delivery.delivered == 1;
    Color statusColor = isDelivered ? Colors.green : Colors.orange;
    String statusText = isDelivered ? 'Livré' : 'En attente';
    IconData statusIcon = isDelivered ? Icons.check_circle : Icons.schedule;

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: statusColor.withValues(alpha: 0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.06),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: Colors.blue.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: SvgPicture.asset(
                    'assets/icons/truck-check.svg',
                    colorFilter:
                        const ColorFilter.mode(Colors.blue, BlendMode.srcIn),
                    width: 24,
                    height: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'CMD ${delivery.commande}',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        'Date: ${delivery.dateFin}',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                  decoration: BoxDecoration(
                    color: statusColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        statusIcon,
                        size: 14,
                        color: statusColor,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        statusText,
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: statusColor,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(10),
              ),
              child: Column(
                children: [
                  _buildInfoRow(Icons.person_outline, 'Client',
                      '${delivery.nom} ${delivery.prenom}'),
                  const SizedBox(height: 8),
                  _buildInfoRow(
                      Icons.phone_outlined, 'Contact', delivery.contact),
                ],
              ),
            ),

            if (!isDelivered) ...[
              const SizedBox(height: 16),
              if (delivery.choix.trim().isNotEmpty && delivery.choix != ' ')
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.green.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(10),
                    border: Border.all(
                      color: Colors.green.withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.check_circle_outline,
                        color: Colors.green,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'Planning: ${delivery.choix}',
                          style: const TextStyle(
                            fontWeight: FontWeight.w600,
                            color: Colors.green,
                          ),
                        ),
                      ),
                    ],
                  ),
                )
              else
                _buildPlanningSelector(),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: Colors.grey[600],
        ),
        const SizedBox(width: 8),
        Text(
          '$label: ',
          style: TextStyle(
            fontSize: 13,
            color: Colors.grey[600],
            fontWeight: FontWeight.w500,
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(
              fontSize: 13,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPlanningSelector() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(10),
      ),
      child: Material(
        color: Colors.transparent,
        child: PopupMenuButton<Option>(
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Row(
              children: [
                Icon(
                  Icons.schedule_outlined,
                  color: Colors.blue,
                  size: 20,
                ),
                const SizedBox(width: 8),
                const Expanded(
                  child: Text(
                    'Sélectionner un planning',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Colors.blue,
                    ),
                  ),
                ),
                Icon(
                  Icons.arrow_drop_down,
                  color: Colors.grey[600],
                ),
              ],
            ),
          ),
          itemBuilder: (BuildContext context) {
            return planningOptions.map((Option option) {
              return PopupMenuItem<Option>(
                value: option,
                child: Text(
                  option.value,
                  style: const TextStyle(fontSize: 14),
                ),
              );
            }).toList();
          },
          onSelected: (Option choice) {
            onPlanSelected(choice.id, delivery.commande, choice.value);
          },
        ),
      ),
    );
  }
}

class DeliveryInfo {
  final String commande;
  final String dateFin;
  final String nom;
  final String prenom;
  final String contact;
  final String choix;
  final int delivered;
  final int finished;

  DeliveryInfo(this.commande, this.dateFin, this.nom, this.prenom, this.contact,
      this.choix, this.delivered, this.finished);
}
