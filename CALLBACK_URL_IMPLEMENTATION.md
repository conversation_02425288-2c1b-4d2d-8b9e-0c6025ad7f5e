# 🔗 Extraction et Lancement des URLs de Callback

## 🎯 Objectif Atteint

**Extraire et stocker** les URLs de callback depuis l'URL de paiement CinetPay, puis **lancer l'URL appropriée** dans le navigateur selon le résultat du paiement.

## 📋 URL d'Exemple Analysée

```
https://checkout.cinetpay.com/payment/171cdaf31c51e17e8729c1a060fa06f2d6871888577b0fe2f5de8fd639be4dddfba87378d7474bf7b50b71d35f6fc41b1751d2a50dff8a?command_id=54855&client_id=576&transaction_id=unknown&success_url=https%3A%2F%2Fapi.callitris-distribution.com%2Fdeep_links_config%2Fbackend%2Fpayment_redirect.php%3Fprovider%3Dgeneric%26status%3Dsuccess%26command_id%3D54855%26client_id%3D576%26transaction_id%3Dunknown&failure_url=https%3A%2F%2Fapi.callitris-distribution.com%2Fdeep_links_config%2Fbackend%2Fpayment_redirect.php%3Fprovider%3Dgeneric%26status%3Dfailure%26command_id%3D54855%26client_id%3D576%26transaction_id%3Dunknown&pending_url=https%3A%2F%2Fapi.callitris-distribution.com%2Fdeep_links_config%2Fbackend%2Fpayment_redirect.php%3Fprovider%3Dgeneric%26status%3Dpending%26command_id%3D54855%26client_id%3D576%26transaction_id%3Dunknown
```

## 🛠️ Implémentation Technique

### 1. Variables de Stockage

```dart
class _PaymentWebViewState extends State<PaymentWebView> {
  String? _successUrl;
  String? _failureUrl;
  String? _pendingUrl;
```

### 2. Extraction des URLs

```dart
void _extractCallbackUrls() {
  try {
    final uri = Uri.parse(widget.paymentUrl);
    _successUrl = uri.queryParameters['success_url'];
    _failureUrl = uri.queryParameters['failure_url'];
    _pendingUrl = uri.queryParameters['pending_url'];
    
    // Décoder les URLs si elles sont encodées
    if (_successUrl != null) {
      _successUrl = Uri.decodeComponent(_successUrl!);
    }
    if (_failureUrl != null) {
      _failureUrl = Uri.decodeComponent(_failureUrl!);
    }
    if (_pendingUrl != null) {
      _pendingUrl = Uri.decodeComponent(_pendingUrl!);
    }
  } catch (e) {
    AppLogger.error('Erreur lors de l\'extraction des URLs: $e', 'PaymentWebView');
  }
}
```

### 3. Lancement selon le Résultat

```dart
void _handlePaymentSuccess(Map<String, String> params) {
  // ... logique existante ...
  
  // Lancer l'URL de succès dans le navigateur
  _launchSuccessUrl();
  
  // ... reste de la logique ...
}

void _handlePaymentFailure(Map<String, String> params) {
  // ... logique existante ...
  
  // Lancer l'URL d'échec dans le navigateur
  _launchFailureUrl();
  
  // ... reste de la logique ...
}
```

### 4. Méthodes de Lancement

```dart
void _launchSuccessUrl() {
  if (_successUrl != null && _successUrl!.isNotEmpty) {
    _launchUrlInBrowser(_successUrl!);
  }
}

void _launchFailureUrl() {
  if (_failureUrl != null && _failureUrl!.isNotEmpty) {
    _launchUrlInBrowser(_failureUrl!);
  }
}

void _launchUrlInBrowser(String url) {
  Future.delayed(const Duration(milliseconds: 300), () async {
    try {
      await launchUrl(
        Uri.parse(url),
        mode: LaunchMode.externalApplication,
      );
    } catch (e) {
      AppLogger.error('Erreur lors du lancement de l\'URL: $e', 'PaymentWebView');
    }
  });
}
```

## 🔄 Flux de Fonctionnement

### 1. Initialisation
```
WebView s'ouvre → _extractCallbackUrls() → URLs stockées
```

### 2. Paiement Réussi
```
Succès détecté → _handlePaymentSuccess() → _launchSuccessUrl() → Navigateur s'ouvre
```

### 3. Paiement Échoué
```
Échec détecté → _handlePaymentFailure() → _launchFailureUrl() → Navigateur s'ouvre
```

### 4. Paiement en Attente
```
Attente détectée → _handlePaymentPending() → _launchPendingUrl() → Navigateur s'ouvre
```

## 📊 URLs Extraites et Décodées

### Success URL
```
https://api.callitris-distribution.com/deep_links_config/backend/payment_redirect.php?provider=generic&status=success&command_id=54855&client_id=576&transaction_id=unknown
```

### Failure URL
```
https://api.callitris-distribution.com/deep_links_config/backend/payment_redirect.php?provider=generic&status=failure&command_id=54855&client_id=576&transaction_id=unknown
```

### Pending URL
```
https://api.callitris-distribution.com/deep_links_config/backend/payment_redirect.php?provider=generic&status=pending&command_id=54855&client_id=576&transaction_id=unknown
```

## 🎮 Scénarios d'Utilisation

### Scénario 1 : Paiement Réussi
1. Utilisateur effectue le paiement avec succès
2. WebView détecte le succès
3. **Success URL** lancée dans le navigateur
4. Backend traite le succès avec les bons paramètres
5. Deep links ramènent vers l'app si configuré

### Scénario 2 : Paiement Échoué
1. Paiement échoue ou est annulé
2. WebView détecte l'échec
3. **Failure URL** lancée dans le navigateur
4. Backend traite l'échec avec les bons paramètres
5. Deep links ramènent vers l'app si configuré

### Scénario 3 : Paiement en Attente
1. Paiement en cours de traitement
2. WebView détecte l'état en attente
3. **Pending URL** lancée dans le navigateur
4. Backend traite l'état en attente
5. Suivi possible du statut

## 🛡️ Sécurités Implémentées

### 1. Vérification d'Existence
```dart
if (_successUrl != null && _successUrl!.isNotEmpty) {
  _launchUrlInBrowser(_successUrl!);
} else {
  AppLogger.warning('URL de succès non disponible', 'PaymentWebView');
}
```

### 2. Décodage Sécurisé
```dart
if (_successUrl != null) {
  _successUrl = Uri.decodeComponent(_successUrl!);
}
```

### 3. Gestion d'Erreurs
```dart
try {
  await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
} catch (e) {
  AppLogger.error('Erreur lors du lancement de l\'URL: $e', 'PaymentWebView');
}
```

### 4. Délai de Sécurité
```dart
Future.delayed(const Duration(milliseconds: 300), () async {
  // Lancement après que la WebView soit fermée
});
```

## 📝 Logs de Débogage

### Extraction
```
[PaymentWebView] URLs extraites - Success: https://api.callitris-distribution.com/..., Failure: https://api.callitris-distribution.com/..., Pending: https://api.callitris-distribution.com/...
```

### Lancement
```
[PaymentWebView] Lancement URL de succès: https://api.callitris-distribution.com/...
[PaymentWebView] URL lancée avec succès dans le navigateur: https://api.callitris-distribution.com/...
```

## 🎉 Avantages

### Pour l'Utilisateur
- ✅ **Traitement automatique** des résultats de paiement
- ✅ **URLs correctes** lancées selon le contexte
- ✅ **Paramètres préservés** (command_id, client_id, etc.)
- ✅ **Intégration fluide** avec le backend

### Pour le Développeur
- ✅ **Extraction automatique** des URLs
- ✅ **Décodage sécurisé** des URLs encodées
- ✅ **Gestion d'erreurs** complète
- ✅ **Logs détaillés** pour le débogage

## 🚀 Résultat Final

**Maintenant, selon le résultat du paiement** :

1. **Succès** → URL de succès lancée avec `status=success` ✅
2. **Échec** → URL d'échec lancée avec `status=failure` ✅  
3. **En attente** → URL en attente lancée avec `status=pending` ✅

Le backend reçoit automatiquement les **bons paramètres** et peut traiter le résultat approprié, permettant aux **deep links de fonctionner** pour ramener l'utilisateur dans l'application ! 🎯
