import 'dart:convert';

import 'package:callitris/pages/auth_provider.dart';
import 'package:flutter/material.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';

class ScannerEncaissement extends StatefulWidget {
  const ScannerEncaissement({this.id, super.key});

  final String? id;

  @override
  State<ScannerEncaissement> createState() => _ScannerEncaissementState();
}

class _ScannerEncaissementState extends State<ScannerEncaissement> {
  Barcode? _barcode;
  bool _hasScanned = false;
  MobileScannerController? _scannerController;
  Map<String, dynamic> _clientData = {};
  String? _clientRoute = '';

  @override
  void initState() {
    super.initState();
    _scannerController = MobileScannerController(
      detectionSpeed: DetectionSpeed.noDuplicates,
      facing: CameraFacing.back,
      torchEnabled: false,
      returnImage: false,
    );
  }

  @override
  void dispose() {
    _scannerController?.dispose();
    super.dispose();
  }

  void _handleBarcode(BarcodeCapture capture) async {
    if (!_hasScanned && capture.barcodes.isNotEmpty) {
      final code = capture.barcodes.firstOrNull?.rawValue;

      if (code != null) {
        setState(() {
          _hasScanned = true;
          _barcode = capture.barcodes.firstOrNull;
        });

        await Future.delayed(const Duration(milliseconds: 500));

        if (!mounted) return;

        try {
          _processClientPayment(code);
        } catch (e) {
          if (!mounted) return;
          _showErrorSnackBar("Erreur lors du scan : ${e.toString()}");
        }
      }
    }
  }

  Map<String, dynamic> _parseClientData(String qrCode) {
    try {
      // Essayer de parser comme JSON
      if (qrCode.startsWith('{') && qrCode.endsWith('}')) {
        return Map<String, dynamic>.from(Uri.splitQueryString(
            qrCode.replaceAll('{', '').replaceAll('}', '')));
      }

      if (qrCode.startsWith('client_data:')) {
        final dataString = qrCode.substring('client_data:'.length);
        final params = Uri.splitQueryString(dataString);
        return params;
      }

      return {};
    } catch (e) {
      return {};
    }
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.w600,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _processClientPayment(String result) {
    try {
      final Map<String, dynamic> data = jsonDecode(result);
      final user = Provider.of<AuthProvider>(context, listen: false).user;

      String idPersonnel = user!['id_personnel'].toString();

      print("data: $data");
      print("idPersonnel: $idPersonnel");

      if (data.isNotEmpty) {
        if (data['personnel_id'] == idPersonnel) {
          setState(() {
            _clientData = data;
            _clientRoute = '/client_payment';
          });
        } else {
          setState(() {
            _clientData = {};
            _clientRoute = null;
          });
          _showErrorSnackBar(
              "Oups ! Désolé, ce client ne fait pas partie de votre base de données clients.");
        }
      } else {
        _showErrorSnackBar("Le QR code ne contient aucune donnée.");
      }
    } catch (e) {
      _showErrorSnackBar("QR code non valide (JSON attendu).");
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red.shade600,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          MobileScanner(
            controller: _scannerController,
            onDetect: _handleBarcode,
          ),
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.black.withValues(alpha: 0.3),
                  Colors.transparent,
                  Colors.transparent,
                  Colors.black.withValues(alpha: 0.5),
                ],
                stops: const [0.0, 0.3, 0.7, 1.0],
              ),
            ),
          ),
          SafeArea(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Row(
                children: [
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.5),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: IconButton(
                      icon: const Icon(Icons.arrow_back,
                          color: Colors.white, size: 24),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 12),
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.5),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Text(
                        'Scanner QR Code',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          Center(
            child: Container(
              width: 280,
              height: 280,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: Colors.teal.shade400,
                  width: 3,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.teal.withValues(alpha: 0.3),
                    blurRadius: 20,
                    spreadRadius: 5,
                  ),
                ],
              ),
              child: Stack(
                children: [
                  ...List.generate(4, (index) => _buildCorner(index)),
                  Center(
                    child: Container(
                      width: 4,
                      height: 4,
                      decoration: BoxDecoration(
                        color: Colors.teal.shade400,
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.teal.withValues(alpha: 0.5),
                            blurRadius: 8,
                            spreadRadius: 2,
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Container(
              padding: const EdgeInsets.all(5),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.transparent,
                    Colors.black.withValues(alpha: 0.8),
                    Colors.black.withValues(alpha: 0.9),
                  ],
                ),
              ),
              child: SafeArea(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (_clientData.isNotEmpty) ...[
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(16),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.2),
                              blurRadius: 10,
                              offset: const Offset(0, -2),
                            ),
                          ],
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Container(
                                  padding: const EdgeInsets.all(8),
                                  decoration: BoxDecoration(
                                    color: Colors.teal.shade100,
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Icon(
                                    Icons.person,
                                    color: Colors.teal.shade700,
                                    size: 24,
                                  ),
                                ),
                                const SizedBox(width: 12),
                                const Expanded(
                                  child: Text(
                                    'Informations Client',
                                    style: TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.black87,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),
                            _buildClientInfoRow(
                                'Nom complet',
                                '${_clientData['nom'] ?? ''} ${_clientData['prenom'] ?? ''}'
                                    .trim(),
                                Icons.person_outline),
                            _buildClientInfoRow(
                                'Téléphone',
                                _clientData['telephone'] ?? 'Non spécifié',
                                Icons.phone_outlined),
                            if (_clientData['code_client'] != null &&
                                _clientData['code_client']
                                    .toString()
                                    .isNotEmpty)
                              _buildClientInfoRow(
                                  'Code Client',
                                  _clientData['code_client'] ?? 'Non spécifié',
                                  Icons.code_outlined),
                            _buildClientInfoRow(
                                'Type Client',
                                _clientData['type'] ?? 'Non spécifié',
                                Icons.account_circle_outlined),
                            const SizedBox(height: 12),
                            Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: Colors.grey.shade50,
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(color: Colors.grey.shade200),
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Icon(Icons.info_outline,
                                          size: 16,
                                          color: Colors.grey.shade600),
                                      const SizedBox(width: 8),
                                      Text(
                                        'Données récupérées du QR Code',
                                        style: TextStyle(
                                          fontSize: 12,
                                          fontWeight: FontWeight.w600,
                                          color: Colors.grey.shade700,
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    'Code: ${_clientData['code_client'] ?? ''}\n'
                                        .trim(),
                                    style: TextStyle(
                                      fontSize: 11,
                                      color: Colors.grey.shade600,
                                    ),
                                  ),
                                  const SizedBox(height: 4),
                                ],
                              ),
                            ),
                            const SizedBox(height: 20),
                            Row(
                              children: [
                                const SizedBox(width: 12),
                                Expanded(
                                  child: ElevatedButton.icon(
                                    onPressed: () {
                                      if (_clientRoute != null &&
                                          _clientRoute!.isNotEmpty) {
                                        Navigator.pushNamed(
                                          context,
                                          _clientRoute!,
                                          arguments: {
                                            'clientData': _clientData,
                                            'commandeId': widget.id,
                                          },
                                        );
                                      } else {
                                        _showErrorSnackBar(
                                            "Impossible de rediriger : données manquantes.");
                                      }
                                    },
                                    icon: const Icon(Icons.check, size: 18),
                                    label:
                                        const Text('Acceder au(x) paiement(s)'),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.teal,
                                      foregroundColor: Colors.white,
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 12),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildClientInfoRow(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Row(
        children: [
          Icon(
            icon,
            size: 18,
            color: Colors.teal.shade600,
          ),
          const SizedBox(width: 12),
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.w600,
                color: Colors.grey,
                fontSize: 14,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: 14,
                color: Colors.black87,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCorner(int index) {
    final positions = [
      {'top': 15.0, 'left': 15.0},
      {'top': 15.0, 'right': 15.0},
      {'bottom': 15.0, 'left': 15.0},
      {'bottom': 15.0, 'right': 15.0},
    ];

    final pos = positions[index];

    return Positioned(
      top: pos['top'],
      left: pos['left'],
      right: pos['right'],
      bottom: pos['bottom'],
      child: Container(
        width: 20,
        height: 20,
        decoration: BoxDecoration(
          border: Border(
            top: index < 2
                ? BorderSide(color: Colors.white, width: 3)
                : BorderSide.none,
            bottom: index >= 2
                ? BorderSide(color: Colors.white, width: 3)
                : BorderSide.none,
            left: index % 2 == 0
                ? BorderSide(color: Colors.white, width: 3)
                : BorderSide.none,
            right: index % 2 == 1
                ? BorderSide(color: Colors.white, width: 3)
                : BorderSide.none,
          ),
        ),
      ),
    );
  }
}
